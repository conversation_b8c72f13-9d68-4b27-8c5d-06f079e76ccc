<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Classes
    |--------------------------------------------------------------------------
    |
    | This config option allows you to set default classes that will be applied
    | to all Solar icons. You can override this on a per-icon basis by passing
    | the class attribute directly to the icon component.
    |
    */

    'class' => '',

    /*
    |--------------------------------------------------------------------------
    | Default Attributes
    |--------------------------------------------------------------------------
    |
    | This config option allows you to set default attributes that will be
    | applied to all Solar icons. You can override this on a per-icon basis by
    | passing the attribute directly to the icon component.
    |
    */

    'attributes' => [
        // 'width' => 50,
        // 'height' => 50,
    ],

    /*
    |--------------------------------------------------------------------------
    | Icon Sets
    |--------------------------------------------------------------------------
    |
    | This config option allows you to define which icon sets should be
    | registered. By default, all Solar icon sets are registered.
    |
    */

    'sets' => [
        'solar-bold',
        'solar-bold-duotone',
        'solar-broken',
        'solar-line-duotone',
        'solar-linear',
        'solar-outline',
    ],
];
