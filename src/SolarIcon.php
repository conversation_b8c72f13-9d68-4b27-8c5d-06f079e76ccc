<?php

declare(strict_types=1);

namespace Monsefeledrisse\FilamentSolarIcons;

/**
 * Solar Icon Set for Filament v4
 *
 * This enum provides type-safe access to Solar icons in Filament v4,
 * similar to how Heroicon works in the core Filament package.
 *
 * Each enum case represents a specific Solar icon with its full identifier.
 * The enum implements ScalableIcon when Filament is available.
 *
 * @example
 * ```php
 * // In Filament components
 * Action::make('star')->icon(SolarIcon::Star)
 * TextInput::make('name')->prefixIcon(SolarIcon::OutlineUser)
 * NavigationItem::make('Dashboard')->icon(SolarIcon::LinearHome)
 * ```
 *
 * @package Monsefeledrisse\FilamentSolarIcons
 */
enum SolarIcon: string
{
    // ========================================
    // AUTO-GENERATED ENUM CASES
    // Generated by: bin/sync-solar-icons.php
    // Total icons: 7330
    // Generated on: 2025-08-03 12:46:50
    // ========================================

    // Bold Style (1235 icons)
    case FacemaskCircle = 'solar-bold-facemask_circle';
    case ConfoundedCircle = 'solar-bold-confounded_circle';
    case SadSquare = 'solar-bold-sad_square';
    case SleepingCircle = 'solar-bold-sleeping_circle';
    case FaceScanCircle = 'solar-bold-face_scan_circle';
    case SmileCircle = 'solar-bold-smile_circle';
    case StickerSmileCircle = 'solar-bold-sticker_smile_circle';
    case StickerSquare = 'solar-bold-sticker_square';
    case EmojiFunnyCircle = 'solar-bold-emoji_funny_circle';
    case ExpressionlessSquare = 'solar-bold-expressionless_square';
    case SleepingSquare = 'solar-bold-sleeping_square';
    case SadCircle = 'solar-bold-sad_circle';
    case FacemaskSquare = 'solar-bold-facemask_square';
    case ConfoundedSquare = 'solar-bold-confounded_square';
    case FaceScanSquare = 'solar-bold-face_scan_square';
    case SmileSquare = 'solar-bold-smile_square';
    case StickerSmileCircle2 = 'solar-bold-sticker_smile_circle2';
    case StickerSmileSquare = 'solar-bold-sticker_smile_square';
    case EmojiFunnySquare = 'solar-bold-emoji_funny_square';
    case StickerCircle = 'solar-bold-sticker_circle';
    case ExpressionlessCircle = 'solar-bold-expressionless_circle';
    case Like = 'solar-bold-Like';
    case MedalStarSquare = 'solar-bold-medal_star_square';
    case Dislike = 'solar-bold-Dislike';
    case StarShine = 'solar-bold-star_shine';
    case HeartAngle = 'solar-bold-heart_angle';
    case MedalRibbon = 'solar-bold-medal_ribbon';
    case HeartShine = 'solar-bold-heart_shine';
    case MedalStarCircle = 'solar-bold-medal_star_circle';
    case MedalRibbonsStar = 'solar-bold-medal_ribbons_star';
    case Star = 'solar-bold-Star';
    case HeartUnlock = 'solar-bold-heart_unlock';
    case MedalRibbonStar = 'solar-bold-medal_ribbon_star';
    case HeartLock = 'solar-bold-heart_lock';
    case HeartBroken = 'solar-bold-heart_broken';
    case Hearts = 'solar-bold-Hearts';
    case MedalStar = 'solar-bold-medal_star';
    case Heart = 'solar-bold-Heart';
    case Closet = 'solar-bold-Closet';
    case Bed = 'solar-bold-Bed';
    case WashingMachine = 'solar-bold-washing_machine';
    case BedsideTable = 'solar-bold-bedside_table';
    case Sofa3 = 'solar-bold-sofa3';
    case Sofa2 = 'solar-bold-sofa2';
    case Chair2 = 'solar-bold-chair2';
    case Bath = 'solar-bold-Bath';
    case SmartVacuumCleaner2 = 'solar-bold-smart_vacuum_cleaner2';
    case Condicioner = 'solar-bold-Condicioner';
    case SmartVacuumCleaner = 'solar-bold-smart_vacuum_cleaner';
    case RemoteController2 = 'solar-bold-remote_controller2';
    case FloorLampMinimalistic = 'solar-bold-floor_lamp_minimalistic';
    case Lamp = 'solar-bold-Lamp';
    case BarChair = 'solar-bold-bar_chair';
    case BedsideTable2 = 'solar-bold-bedside_table2';
    case Closet2 = 'solar-bold-closet2';
    case BedsideTable3 = 'solar-bold-bedside_table3';
    case Speaker = 'solar-bold-Speaker';
    case VolumeKnob = 'solar-bold-volume_knob';
    case Armchair = 'solar-bold-Armchair';
    case SpeakerMinimalistic = 'solar-bold-speaker_minimalistic';
    case RemoteController = 'solar-bold-remote_controller';
    case Trellis = 'solar-bold-Trellis';
    case FloorLamp = 'solar-bold-floor_lamp';
    case Condicioner2 = 'solar-bold-condicioner2';
    case BedsideTable4 = 'solar-bold-bedside_table4';
    case Armchair2 = 'solar-bold-armchair2';
    case WashingMachineMinimalistic = 'solar-bold-washing_machine_minimalistic';
    case Chair = 'solar-bold-Chair';
    case RemoteControllerMinimalistic = 'solar-bold-remote_controller_minimalistic';
    case Chandelier = 'solar-bold-Chandelier';
    case Fridge = 'solar-bold-Fridge';
    case Mirror = 'solar-bold-Mirror';
    case Sofa = 'solar-bold-Sofa';
    case Earth = 'solar-bold-Earth';
    case StarsLine = 'solar-bold-stars_line';
    case StarFall2 = 'solar-bold-star_fall2';
    case StarFall = 'solar-bold-star_fall';
    case BlackHole3 = 'solar-bold-black_hole3';
    case Women = 'solar-bold-Women';
    case BlackHole = 'solar-bold-black_hole';
    case StarRings = 'solar-bold-star_rings';
    case BlackHole2 = 'solar-bold-black_hole2';
    case StarFallMinimalistic2 = 'solar-bold-star_fall_minimalistic2';
    case Planet = 'solar-bold-Planet';
    case Satellite = 'solar-bold-Satellite';
    case Men = 'solar-bold-Men';
    case Rocket2 = 'solar-bold-rocket2';
    case Stars = 'solar-bold-Stars';
    case StarAngle = 'solar-bold-star_angle';
    case Infinity = 'solar-bold-Infinity';
    case Ufo2 = 'solar-bold-ufo2';
    case Ufo3 = 'solar-bold-ufo3';
    case StarRing = 'solar-bold-star_ring';
    case Planet2 = 'solar-bold-planet2';
    case Planet3 = 'solar-bold-planet3';
    case Asteroid = 'solar-bold-Asteroid';
    case StarsMinimalistic = 'solar-bold-stars_minimalistic';
    case UFO = 'solar-bold-UFO';
    case Planet4 = 'solar-bold-planet4';
    case Rocket = 'solar-bold-Rocket';
    case StarFallMinimalistic = 'solar-bold-star_fall_minimalistic';
    case StarRainbow = 'solar-bold-star_rainbow';
    case Atom = 'solar-bold-Atom';
    case StarCircle = 'solar-bold-star_circle';
    case CompassBig = 'solar-bold-compass_big';
    case MapPointSchool = 'solar-bold-map_point_school';
    case Signpost = 'solar-bold-Signpost';
    case MapArrowDown = 'solar-bold-map_arrow_down';
    case Map = 'solar-bold-Map';
    case MapArrowUp = 'solar-bold-map_arrow_up';
    case PointOnMapPerspective = 'solar-bold-point_on_map_perspective';
    case Radar = 'solar-bold-Radar';
    case Streets = 'solar-bold-Streets';
    case MapPointWave = 'solar-bold-map_point_wave';
    case PeopleNearby = 'solar-bold-people_nearby';
    case StreetsMapPoint = 'solar-bold-streets_map_point';
    case MapPointSearch = 'solar-bold-map_point_search';
    case GPS = 'solar-bold-GPS';
    case MapArrowSquare = 'solar-bold-map_arrow_square';
    case BranchingPathsDown = 'solar-bold-branching_paths_down';
    case MapPointRotate = 'solar-bold-map_point_rotate';
    case Global = 'solar-bold-Global';
    case CompassSquare = 'solar-bold-compass_square';
    case Routing3 = 'solar-bold-routing3';
    case Routing2 = 'solar-bold-routing2';
    case MapPointRemove = 'solar-bold-map_point_remove';
    case Globus = 'solar-bold-Globus';
    case Signpost2 = 'solar-bold-signpost2';
    case Radar2 = 'solar-bold-radar2';
    case StreetsNavigation = 'solar-bold-streets_navigation';
    case MapPoint = 'solar-bold-map_point';
    case MapPointHospital = 'solar-bold-map_point_hospital';
    case Compass = 'solar-bold-Compass';
    case MapPointAdd = 'solar-bold-map_point_add';
    case BranchingPathsUp = 'solar-bold-branching_paths_up';
    case MapPointFavourite = 'solar-bold-map_point_favourite';
    case Route = 'solar-bold-Route';
    case PointOnMap = 'solar-bold-point_on_map';
    case MapArrowRight = 'solar-bold-map_arrow_right';
    case Routing = 'solar-bold-Routing';
    case MapArrowLeft = 'solar-bold-map_arrow_left';
    case Incognito = 'solar-bold-Incognito';
    case LockPassword = 'solar-bold-lock_password';
    case ShieldNetwork = 'solar-bold-shield_network';
    case KeyMinimalisticSquare = 'solar-bold-key_minimalistic_square';
    case LockKeyholeUnlocked = 'solar-bold-lock_keyhole_unlocked';
    case Lock = 'solar-bold-Lock';
    case ShieldKeyhole = 'solar-bold-shield_keyhole';
    case EyeClosed = 'solar-bold-eye_closed';
    case Key = 'solar-bold-Key';
    case ShieldMinus = 'solar-bold-shield_minus';
    case Shield = 'solar-bold-Shield';
    case LockUnlocked = 'solar-bold-lock_unlocked';
    case BombMinimalistic = 'solar-bold-bomb_minimalistic';
    case ShieldStar = 'solar-bold-shield_star';
    case Bomb = 'solar-bold-Bomb';
    case KeySquare = 'solar-bold-key_square';
    case LockKeyholeMinimalisticUnlocked = 'solar-bold-lock_keyhole_minimalistic_unlocked';
    case ShieldCross = 'solar-bold-shield_cross';
    case ObjectScan = 'solar-bold-object_scan';
    case PasswordMinimalisticInput = 'solar-bold-password_minimalistic_input';
    case LockPasswordUnlocked = 'solar-bold-lock_password_unlocked';
    case Siren = 'solar-bold-Siren';
    case ShieldMinimalistic = 'solar-bold-shield_minimalistic';
    case EyeScan = 'solar-bold-eye_scan';
    case KeyMinimalisticSquare2 = 'solar-bold-key_minimalistic_square2';
    case Scanner2 = 'solar-bold-scanner2';
    case KeyMinimalisticSquare3 = 'solar-bold-key_minimalistic_square3';
    case KeyMinimalistic2 = 'solar-bold-key_minimalistic2';
    case CodeScan = 'solar-bold-code_scan';
    case ShieldPlus = 'solar-bold-shield_plus';
    case PasswordMinimalistic = 'solar-bold-password_minimalistic';
    case Eye = 'solar-bold-Eye';
    case QrCode = 'solar-bold-qr_code';
    case ShieldCheck = 'solar-bold-shield_check';
    case KeyMinimalistic = 'solar-bold-key_minimalistic';
    case LockKeyhole = 'solar-bold-lock_keyhole';
    case ShieldUser = 'solar-bold-shield_user';
    case KeySquare2 = 'solar-bold-key_square2';
    case BombEmoji = 'solar-bold-bomb_emoji';
    case Scanner = 'solar-bold-Scanner';
    case ShieldUp = 'solar-bold-shield_up';
    case SirenRounded = 'solar-bold-siren_rounded';
    case LockKeyholeMinimalistic = 'solar-bold-lock_keyhole_minimalistic';
    case Password = 'solar-bold-Password';
    case ShieldKeyholeMinimalistic = 'solar-bold-shield_keyhole_minimalistic';
    case ShieldWarning = 'solar-bold-shield_warning';
    case Pallete2 = 'solar-bold-pallete2';
    case AlignVerticalSpacing = 'solar-bold-align_vertical_spacing';
    case AlignVerticalCenter = 'solar-bold-align_vertical_center';
    case CropMinimalistic = 'solar-bold-crop_minimalistic';
    case MirrorRight = 'solar-bold-mirror_right';
    case AlignBottom = 'solar-bold-align_bottom';
    case RadialBlur = 'solar-bold-radial_blur';
    case Crop = 'solar-bold-Crop';
    case AlignHorizontaSpacing = 'solar-bold-align_horizonta_spacing';
    case RulerPen = 'solar-bold-ruler_pen';
    case ThreeSquares = 'solar-bold-three_squares';
    case PaintRoller = 'solar-bold-paint_roller';
    case Layers = 'solar-bold-Layers';
    case Filters = 'solar-bold-Filters';
    case RulerCrossPen = 'solar-bold-ruler_cross_pen';
    case FlipHorizontal = 'solar-bold-flip_horizontal';
    case AlignLeft = 'solar-bold-align_left';
    case Ruler = 'solar-bold-Ruler';
    case Palette = 'solar-bold-Palette';
    case AlignTop = 'solar-bold-align_top';
    case AlignHorizontalCenter = 'solar-bold-align_horizontal_center';
    case AlignRight = 'solar-bold-align_right';
    case RulerAngular = 'solar-bold-ruler_angular';
    case Pipette = 'solar-bold-Pipette';
    case FlipVertical = 'solar-bold-flip_vertical';
    case MirrorLeft = 'solar-bold-mirror_left';
    case LayersMinimalistic = 'solar-bold-layers_minimalistic';
    case ColourTuneing = 'solar-bold-colour_tuneing';
    case PaletteRound = 'solar-bold-palette_round';
    case Eraser = 'solar-bold-Eraser';
    case TextItalicCircle = 'solar-bold-text_italic_circle';
    case LinkRound = 'solar-bold-link_round';
    case TextItalic = 'solar-bold-text_italic';
    case LinkBrokenMinimalistic = 'solar-bold-link_broken_minimalistic';
    case TextUnderlineCross = 'solar-bold-text_underline_cross';
    case Link = 'solar-bold-Link';
    case EraserCircle = 'solar-bold-eraser_circle';
    case LinkCircle = 'solar-bold-link_circle';
    case TextBoldCircle = 'solar-bold-text_bold_circle';
    case TextField = 'solar-bold-text_field';
    case TextSquare = 'solar-bold-text_square';
    case TextSquare2 = 'solar-bold-text_square2';
    case LinkRoundAngle = 'solar-bold-link_round_angle';
    case TextUnderlineCircle = 'solar-bold-text_underline_circle';
    case TextCrossCircle = 'solar-bold-text_cross_circle';
    case TextItalicSquare = 'solar-bold-text_italic_square';
    case ParagraphSpacing = 'solar-bold-paragraph_spacing';
    case Text = 'solar-bold-Text';
    case LinkBroken = 'solar-bold-link_broken';
    case TextCross = 'solar-bold-text_cross';
    case TextUnderline = 'solar-bold-text_underline';
    case LinkMinimalistic = 'solar-bold-link_minimalistic';
    case LinkMinimalistic2 = 'solar-bold-link_minimalistic2';
    case TextBold = 'solar-bold-text_bold';
    case TextSelection = 'solar-bold-text_selection';
    case TextFieldFocus = 'solar-bold-text_field_focus';
    case TextBoldSquare = 'solar-bold-text_bold_square';
    case EraserSquare = 'solar-bold-eraser_square';
    case LinkSquare = 'solar-bold-link_square';
    case TextCircle = 'solar-bold-text_circle';
    case Backspace = 'solar-bold-Backspace';
    case TextCrossSquare = 'solar-bold-text_cross_square';
    case InboxUnread = 'solar-bold-inbox_unread';
    case ChatUnread = 'solar-bold-chat_unread';
    case ChatRound = 'solar-bold-chat_round';
    case Unread = 'solar-bold-Unread';
    case Mailbox = 'solar-bold-Mailbox';
    case Letter = 'solar-bold-Letter';
    case PenNewRound = 'solar-bold-pen_new_round';
    case MultipleForwardRight = 'solar-bold-multiple_forward_right';
    case MultipleForwardLeft = 'solar-bold-multiple_forward_left';
    case InboxArchive = 'solar-bold-inbox_archive';
    case Inbox = 'solar-bold-Inbox';
    case Pen2 = 'solar-bold-pen2';
    case PenNewSquare = 'solar-bold-pen_new_square';
    case Pen = 'solar-bold-Pen';
    case ChatDots = 'solar-bold-chat_dots';
    case ChatSquareCall = 'solar-bold-chat_square_call';
    case SquareShareLine = 'solar-bold-square_share_line';
    case ChatRoundCheck = 'solar-bold-chat_round_check';
    case InboxOut = 'solar-bold-inbox_out';
    case Plain3 = 'solar-bold-plain3';
    case ChatRoundDots = 'solar-bold-chat_round_dots';
    case ChatRoundLike = 'solar-bold-chat_round_like';
    case Plain2 = 'solar-bold-plain2';
    case ChatRoundUnread = 'solar-bold-chat_round_unread';
    case ChatSquareLike = 'solar-bold-chat_square_like';
    case Paperclip = 'solar-bold-Paperclip';
    case ChatSquareCheck = 'solar-bold-chat_square_check';
    case ChatSquare = 'solar-bold-chat_square';
    case LetterOpened = 'solar-bold-letter_opened';
    case SquareForward = 'solar-bold-square_forward';
    case LetterUnread = 'solar-bold-letter_unread';
    case PaperclipRounded2 = 'solar-bold-paperclip_rounded2';
    case ChatRoundCall = 'solar-bold-chat_round_call';
    case InboxLine = 'solar-bold-inbox_line';
    case ChatRoundVideo = 'solar-bold-chat_round_video';
    case ChatRoundMoney = 'solar-bold-chat_round_money';
    case InboxIn = 'solar-bold-inbox_in';
    case CheckRead = 'solar-bold-check_read';
    case ChatRoundLine = 'solar-bold-chat_round_line';
    case Forward = 'solar-bold-Forward';
    case Paperclip2 = 'solar-bold-paperclip2';
    case Dialog2 = 'solar-bold-dialog2';
    case Dialog = 'solar-bold-Dialog';
    case PaperclipRounded = 'solar-bold-paperclip_rounded';
    case Plain = 'solar-bold-Plain';
    case ChatSquareArrow = 'solar-bold-chat_square_arrow';
    case ChatSquareCode = 'solar-bold-chat_square_code';
    case ChatLine = 'solar-bold-chat_line';
    case Tennis = 'solar-bold-Tennis';
    case BicyclingRound = 'solar-bold-bicycling_round';
    case Balls = 'solar-bold-Balls';
    case MeditationRound = 'solar-bold-meditation_round';
    case StretchingRound = 'solar-bold-stretching_round';
    case Dumbbells2 = 'solar-bold-dumbbells2';
    case Meditation = 'solar-bold-Meditation';
    case Running2 = 'solar-bold-running2';
    case Rugby = 'solar-bold-Rugby';
    case BodyShapeMinimalistic = 'solar-bold-body_shape_minimalistic';
    case Stretching = 'solar-bold-Stretching';
    case Bowling = 'solar-bold-Bowling';
    case Ranking = 'solar-bold-Ranking';
    case TreadmillRound = 'solar-bold-treadmill_round';
    case Volleyball = 'solar-bold-Volleyball';
    case DumbbellLargeMinimalistic = 'solar-bold-dumbbell_large_minimalistic';
    case RunningRound = 'solar-bold-running_round';
    case Hiking = 'solar-bold-Hiking';
    case HikingMinimalistic = 'solar-bold-hiking_minimalistic';
    case WaterSun = 'solar-bold-water_sun';
    case Golf = 'solar-bold-Golf';
    case Skateboarding = 'solar-bold-Skateboarding';
    case Dumbbells = 'solar-bold-Dumbbells';
    case WalkingRound = 'solar-bold-walking_round';
    case Running = 'solar-bold-Running';
    case Treadmill = 'solar-bold-Treadmill';
    case Skateboard = 'solar-bold-Skateboard';
    case DumbbellSmall = 'solar-bold-dumbbell_small';
    case Basketball = 'solar-bold-Basketball';
    case Football = 'solar-bold-Football';
    case Dumbbell = 'solar-bold-Dumbbell';
    case BodyShape = 'solar-bold-body_shape';
    case Water = 'solar-bold-Water';
    case SkateboardingRound = 'solar-bold-skateboarding_round';
    case HikingRound = 'solar-bold-hiking_round';
    case Volleyball2 = 'solar-bold-volleyball2';
    case Tennis2 = 'solar-bold-tennis2';
    case Swimming = 'solar-bold-Swimming';
    case Bicycling = 'solar-bold-Bicycling';
    case Walking = 'solar-bold-Walking';
    case DumbbellLarge = 'solar-bold-dumbbell_large';
    case CalendarMark = 'solar-bold-calendar_mark';
    case History2 = 'solar-bold-history2';
    case WatchSquareMinimalisticCharge = 'solar-bold-watch_square_minimalistic_charge';
    case History3 = 'solar-bold-history3';
    case Hourglass = 'solar-bold-Hourglass';
    case CalendarSearch = 'solar-bold-calendar_search';
    case StopwatchPlay = 'solar-bold-stopwatch_play';
    case WatchRound = 'solar-bold-watch_round';
    case CalendarAdd = 'solar-bold-calendar_add';
    case CalendarDate = 'solar-bold-calendar_date';
    case Stopwatch = 'solar-bold-Stopwatch';
    case AlarmPause = 'solar-bold-alarm_pause';
    case AlarmTurnOff = 'solar-bold-alarm_turn_off';
    case ClockSquare = 'solar-bold-clock_square';
    case StopwatchPause = 'solar-bold-stopwatch_pause';
    case CalendarMinimalistic = 'solar-bold-calendar_minimalistic';
    case AlarmAdd = 'solar-bold-alarm_add';
    case AlarmPlay = 'solar-bold-alarm_play';
    case HourglassLine = 'solar-bold-hourglass_line';
    case AlarmSleep = 'solar-bold-alarm_sleep';
    case AlarmRemove = 'solar-bold-alarm_remove';
    case Calendar = 'solar-bold-Calendar';
    case ClockCircle = 'solar-bold-clock_circle';
    case History = 'solar-bold-History';
    case Alarm = 'solar-bold-Alarm';
    case WatchSquare = 'solar-bold-watch_square';
    case WatchSquareMinimalistic = 'solar-bold-watch_square_minimalistic';
    case MagniferBug = 'solar-bold-magnifer_bug';
    case Magnifer = 'solar-bold-Magnifer';
    case MagniferZoomIn = 'solar-bold-magnifer_zoom_in';
    case RoundedMagnifer = 'solar-bold-rounded_magnifer';
    case RoundedMagniferZoomIn = 'solar-bold-rounded_magnifer_zoom_in';
    case MinimalisticMagniferBug = 'solar-bold-minimalistic_magnifer_bug';
    case RoundedMagniferBug = 'solar-bold-rounded_magnifer_bug';
    case MinimalisticMagniferZoomOut = 'solar-bold-minimalistic_magnifer_zoom_out';
    case MinimalisticMagnifer = 'solar-bold-minimalistic_magnifer';
    case RoundedMagniferZoomOut = 'solar-bold-rounded_magnifer_zoom_out';
    case MinimalisticMagniferZoomIn = 'solar-bold-minimalistic_magnifer_zoom_in';
    case MagniferZoomOut = 'solar-bold-magnifer_zoom_out';
    case BagCheck = 'solar-bold-bag_check';
    case ShopMinimalistic = 'solar-bold-shop_minimalistic';
    case Shop = 'solar-bold-Shop';
    case CartCheck = 'solar-bold-cart_check';
    case Cart = 'solar-bold-Cart';
    case Cart3 = 'solar-bold-cart3';
    case Cart2 = 'solar-bold-cart2';
    case BagMusic = 'solar-bold-bag_music';
    case CartLargeMinimalistic = 'solar-bold-cart_large_minimalistic';
    case Cart5 = 'solar-bold-cart5';
    case Cart4 = 'solar-bold-cart4';
    case Bag = 'solar-bold-Bag';
    case BagHeart = 'solar-bold-bag_heart';
    case CartPlus = 'solar-bold-cart_plus';
    case CartLarge = 'solar-bold-cart_large';
    case BagCross = 'solar-bold-bag_cross';
    case BagMusic2 = 'solar-bold-bag_music2';
    case Bag5 = 'solar-bold-bag5';
    case Bag4 = 'solar-bold-bag4';
    case CartLarge4 = 'solar-bold-cart_large4';
    case CartLarge3 = 'solar-bold-cart_large3';
    case Bag3 = 'solar-bold-bag3';
    case Bag2 = 'solar-bold-bag2';
    case Shop2 = 'solar-bold-shop2';
    case CartLarge2 = 'solar-bold-cart_large2';
    case BagSmile = 'solar-bold-bag_smile';
    case CartCross = 'solar-bold-cart_cross';
    case InfoSquare = 'solar-bold-info_square';
    case FlashlightOn = 'solar-bold-flashlight_on';
    case XXX = 'solar-bold-XXX';
    case Figma = 'solar-bold-Figma';
    case Flashlight = 'solar-bold-Flashlight';
    case Ghost = 'solar-bold-Ghost';
    case CupMusic = 'solar-bold-cup_music';
    case BatteryFullMinimalistic = 'solar-bold-battery_full_minimalistic';
    case DangerCircle = 'solar-bold-danger_circle';
    case CheckSquare = 'solar-bold-check_square';
    case GhostSmile = 'solar-bold-ghost_smile';
    case Target = 'solar-bold-Target';
    case BatteryHalfMinimalistic = 'solar-bold-battery_half_minimalistic';
    case Scissors = 'solar-bold-Scissors';
    case PinList = 'solar-bold-pin_list';
    case BatteryCharge = 'solar-bold-battery_charge';
    case Umbrella = 'solar-bold-Umbrella';
    case HomeSmile = 'solar-bold-home_smile';
    case Home = 'solar-bold-Home';
    case Copyright = 'solar-bold-Copyright';
    case HomeWifi = 'solar-bold-home_wifi';
    case TShirt = 'solar-bold-t_shirt';
    case BatteryChargeMinimalistic = 'solar-bold-battery_charge_minimalistic';
    case CupStar = 'solar-bold-cup_star';
    case SpecialEffects = 'solar-bold-special_effects';
    case Body = 'solar-bold-Body';
    case HamburgerMenu = 'solar-bold-hamburger_menu';
    case Power = 'solar-bold-Power';
    case Database = 'solar-bold-Database';
    case CursorSquare = 'solar-bold-cursor_square';
    case Fuel = 'solar-bold-Fuel';
    case MentionCircle = 'solar-bold-mention_circle';
    case ConfettiMinimalistic = 'solar-bold-confetti_minimalistic';
    case MenuDotsCircle = 'solar-bold-menu_dots_circle';
    case Paw = 'solar-bold-Paw';
    case Subtitles = 'solar-bold-Subtitles';
    case SliderVerticalMinimalistic = 'solar-bold-slider_vertical_minimalistic';
    case CrownMinimalistic = 'solar-bold-crown_minimalistic';
    case MenuDots = 'solar-bold-menu_dots';
    case Delivery = 'solar-bold-Delivery';
    case Waterdrop = 'solar-bold-Waterdrop';
    case Perfume = 'solar-bold-Perfume';
    case HomeAngle2 = 'solar-bold-home_angle2';
    case HomeWifiAngle = 'solar-bold-home_wifi_angle';
    case QuestionCircle = 'solar-bold-question_circle';
    case TrashBinMinimalistic = 'solar-bold-trash_bin_minimalistic';
    case MagicStick3 = 'solar-bold-magic_stick3';
    case AddSquare = 'solar-bold-add_square';
    case CrownStar = 'solar-bold-crown_star';
    case Magnet = 'solar-bold-Magnet';
    case Confetti = 'solar-bold-Confetti';
    case Pin = 'solar-bold-Pin';
    case MinusSquare = 'solar-bold-minus_square';
    case Bolt = 'solar-bold-Bolt';
    case CloseCircle = 'solar-bold-close_circle';
    case ForbiddenCircle = 'solar-bold-forbidden_circle';
    case MagicStick2 = 'solar-bold-magic_stick2';
    case CrownLine = 'solar-bold-crown_line';
    case BoltCircle = 'solar-bold-bolt_circle';
    case Flag = 'solar-bold-Flag';
    case SliderHorizontal = 'solar-bold-slider_horizontal';
    case HighDefinition = 'solar-bold-high_definition';
    case Cursor = 'solar-bold-Cursor';
    case Feed = 'solar-bold-Feed';
    case TrafficEconomy = 'solar-bold-traffic_economy';
    case AugmentedReality = 'solar-bold-augmented_reality';
    case Icon4K = 'solar-bold-4_k';
    case MagnetWave = 'solar-bold-magnet_wave';
    case HomeSmileAngle = 'solar-bold-home_smile_angle';
    case SliderVertical = 'solar-bold-slider_vertical';
    case CheckCircle = 'solar-bold-check_circle';
    case Copy = 'solar-bold-Copy';
    case DangerSquare = 'solar-bold-danger_square';
    case Skirt = 'solar-bold-Skirt';
    case Glasses = 'solar-bold-Glasses';
    case HomeAdd = 'solar-bold-home_add';
    case Sledgehammer = 'solar-bold-Sledgehammer';
    case InfoCircle = 'solar-bold-info_circle';
    case DangerTriangle = 'solar-bold-danger_triangle';
    case PinCircle = 'solar-bold-pin_circle';
    case SmartHome = 'solar-bold-smart_home';
    case ScissorsSquare = 'solar-bold-scissors_square';
    case Sleeping = 'solar-bold-Sleeping';
    case Box = 'solar-bold-Box';
    case Crown = 'solar-bold-Crown';
    case Broom = 'solar-bold-Broom';
    case PostsCarouselHorizontal = 'solar-bold-posts_carousel_horizontal';
    case Flag2 = 'solar-bold-flag2';
    case Plate = 'solar-bold-Plate';
    case TrashBinTrash = 'solar-bold-trash_bin_trash';
    case CupFirst = 'solar-bold-cup_first';
    case SmartHomeAngle = 'solar-bold-smart_home_angle';
    case PaperBin = 'solar-bold-paper_bin';
    case BoxMinimalistic = 'solar-bold-box_minimalistic';
    case Danger = 'solar-bold-Danger';
    case MenuDotsSquare = 'solar-bold-menu_dots_square';
    case Hanger2 = 'solar-bold-hanger2';
    case BatteryHalf = 'solar-bold-battery_half';
    case Home2 = 'solar-bold-home2';
    case PostsCarouselVertical = 'solar-bold-posts_carousel_vertical';
    case Revote = 'solar-bold-Revote';
    case MentionSquare = 'solar-bold-mention_square';
    case WinRar = 'solar-bold-win_rar';
    case Forbidden = 'solar-bold-Forbidden';
    case QuestionSquare = 'solar-bold-question_square';
    case Hanger = 'solar-bold-Hanger';
    case Reorder = 'solar-bold-Reorder';
    case HomeAddAngle = 'solar-bold-home_add_angle';
    case Masks = 'solar-bold-Masks';
    case Gift = 'solar-bold-Gift';
    case CreativeCommons = 'solar-bold-creative_commons';
    case SliderMinimalisticHorizontal = 'solar-bold-slider_minimalistic_horizontal';
    case HomeAngle = 'solar-bold-home_angle';
    case BatteryLowMinimalistic = 'solar-bold-battery_low_minimalistic';
    case Share = 'solar-bold-Share';
    case TrashBin2 = 'solar-bold-trash_bin2';
    case Sort = 'solar-bold-Sort';
    case MinusCircle = 'solar-bold-minus_circle';
    case Explicit = 'solar-bold-Explicit';
    case Traffic = 'solar-bold-Traffic';
    case Filter = 'solar-bold-Filter';
    case CloseSquare = 'solar-bold-close_square';
    case AddCircle = 'solar-bold-add_circle';
    case FerrisWheel = 'solar-bold-ferris_wheel';
    case Cup = 'solar-bold-Cup';
    case Balloon = 'solar-bold-Balloon';
    case Help = 'solar-bold-Help';
    case BatteryFull = 'solar-bold-battery_full';
    case Cat = 'solar-bold-Cat';
    case MaskSad = 'solar-bold-mask_sad';
    case HighQuality = 'solar-bold-high_quality';
    case MagicStick = 'solar-bold-magic_stick';
    case Cosmetic = 'solar-bold-Cosmetic';
    case BatteryLow = 'solar-bold-battery_low';
    case ShareCircle = 'solar-bold-share_circle';
    case MaskHapply = 'solar-bold-mask_happly';
    case Accessibility = 'solar-bold-Accessibility';
    case TrashBinMinimalistic2 = 'solar-bold-trash_bin_minimalistic2';
    case IncomingCallRounded = 'solar-bold-incoming_call_rounded';
    case CallDropped = 'solar-bold-call_dropped';
    case CallChat = 'solar-bold-call_chat';
    case CallCancelRounded = 'solar-bold-call_cancel_rounded';
    case CallMedicineRounded = 'solar-bold-call_medicine_rounded';
    case CallDroppedRounded = 'solar-bold-call_dropped_rounded';
    case RecordSquare = 'solar-bold-record_square';
    case PhoneCalling = 'solar-bold-phone_calling';
    case PhoneRounded = 'solar-bold-phone_rounded';
    case CallMedicine = 'solar-bold-call_medicine';
    case RecordMinimalistic = 'solar-bold-record_minimalistic';
    case EndCall = 'solar-bold-end_call';
    case OutgoingCall = 'solar-bold-outgoing_call';
    case RecordCircle = 'solar-bold-record_circle';
    case IncomingCall = 'solar-bold-incoming_call';
    case CallChatRounded = 'solar-bold-call_chat_rounded';
    case EndCallRounded = 'solar-bold-end_call_rounded';
    case Phone = 'solar-bold-Phone';
    case OutgoingCallRounded = 'solar-bold-outgoing_call_rounded';
    case CallCancel = 'solar-bold-call_cancel';
    case PhoneCallingRounded = 'solar-bold-phone_calling_rounded';
    case StationMinimalistic = 'solar-bold-station_minimalistic';
    case SidebarCode = 'solar-bold-sidebar_code';
    case WiFiRouterMinimalistic = 'solar-bold-wi_fi_router_minimalistic';
    case USB = 'solar-bold-USB';
    case Siderbar = 'solar-bold-Siderbar';
    case Code2 = 'solar-bold-code2';
    case SlashCircle = 'solar-bold-slash_circle';
    case Screencast = 'solar-bold-Screencast';
    case HashtagSquare = 'solar-bold-hashtag_square';
    case SidebarMinimalistic = 'solar-bold-sidebar_minimalistic';
    case Code = 'solar-bold-Code';
    case UsbSquare = 'solar-bold-usb_square';
    case WiFiRouter = 'solar-bold-wi_fi_router';
    case CodeCircle = 'solar-bold-code_circle';
    case Translation = 'solar-bold-Translation';
    case BugMinimalistic = 'solar-bold-bug_minimalistic';
    case Station = 'solar-bold-Station';
    case Programming = 'solar-bold-Programming';
    case WiFiRouterRound = 'solar-bold-wi_fi_router_round';
    case Hashtag = 'solar-bold-Hashtag';
    case Bug = 'solar-bold-Bug';
    case HashtagChat = 'solar-bold-hashtag_chat';
    case Command = 'solar-bold-Command';
    case Translation2 = 'solar-bold-translation2';
    case HashtagCircle = 'solar-bold-hashtag_circle';
    case Screencast2 = 'solar-bold-screencast2';
    case SlashSquare = 'solar-bold-slash_square';
    case WindowFrame = 'solar-bold-window_frame';
    case Structure = 'solar-bold-Structure';
    case UsbCircle = 'solar-bold-usb_circle';
    case CodeSquare = 'solar-bold-code_square';
    case Notes = 'solar-bold-Notes';
    case DocumentText = 'solar-bold-document_text';
    case DocumentAdd = 'solar-bold-document_add';
    case DocumentMedicine = 'solar-bold-document_medicine';
    case ArchiveMinimalistic = 'solar-bold-archive_minimalistic';
    case Clipboard = 'solar-bold-Clipboard';
    case ClipboardAdd = 'solar-bold-clipboard_add';
    case Archive = 'solar-bold-Archive';
    case ClipboardHeart = 'solar-bold-clipboard_heart';
    case ClipboardRemove = 'solar-bold-clipboard_remove';
    case ClipboardText = 'solar-bold-clipboard_text';
    case Document = 'solar-bold-Document';
    case NotesMinimalistic = 'solar-bold-notes_minimalistic';
    case ArchiveUp = 'solar-bold-archive_up';
    case ArchiveUpMinimlistic = 'solar-bold-archive_up_minimlistic';
    case ArchiveCheck = 'solar-bold-archive_check';
    case ArchiveDown = 'solar-bold-archive_down';
    case ArchiveDownMinimlistic = 'solar-bold-archive_down_minimlistic';
    case DocumentsMinimalistic = 'solar-bold-documents_minimalistic';
    case ClipboardCheck = 'solar-bold-clipboard_check';
    case ClipboardList = 'solar-bold-clipboard_list';
    case Documents = 'solar-bold-Documents';
    case Notebook = 'solar-bold-Notebook';
    case GalleryRound = 'solar-bold-gallery_round';
    case PlayCircle = 'solar-bold-play_circle';
    case Stream = 'solar-bold-Stream';
    case GalleryRemove = 'solar-bold-gallery_remove';
    case Clapperboard = 'solar-bold-Clapperboard';
    case PauseCircle = 'solar-bold-pause_circle';
    case Rewind5SecondsBack = 'solar-bold-rewind5_seconds_back';
    case Repeat = 'solar-bold-Repeat';
    case ClapperboardEdit = 'solar-bold-clapperboard_edit';
    case VideoFrameCut = 'solar-bold-video_frame_cut';
    case Panorama = 'solar-bold-Panorama';
    case PlayStream = 'solar-bold-play_stream';
    case ClapperboardOpen = 'solar-bold-clapperboard_open';
    case ClapperboardText = 'solar-bold-clapperboard_text';
    case Library = 'solar-bold-Library';
    case Reel2 = 'solar-bold-reel2';
    case VolumeSmall = 'solar-bold-volume_small';
    case VideoFrame = 'solar-bold-video_frame';
    case MicrophoneLarge = 'solar-bold-microphone_large';
    case RewindForward = 'solar-bold-rewind_forward';
    case RewindBackCircle = 'solar-bold-rewind_back_circle';
    case Microphone = 'solar-bold-Microphone';
    case VideoFrameReplace = 'solar-bold-video_frame_replace';
    case ClapperboardPlay = 'solar-bold-clapperboard_play';
    case GalleryDownload = 'solar-bold-gallery_download';
    case MusicNote4 = 'solar-bold-music_note4';
    case VideocameraRecord = 'solar-bold-videocamera_record';
    case PlaybackSpeed = 'solar-bold-playback_speed';
    case Soundwave = 'solar-bold-Soundwave';
    case StopCircle = 'solar-bold-stop_circle';
    case QuitFullScreenCircle = 'solar-bold-quit_full_screen_circle';
    case RewindBack = 'solar-bold-rewind_back';
    case RepeatOne = 'solar-bold-repeat_one';
    case GalleryCheck = 'solar-bold-gallery_check';
    case Wallpaper = 'solar-bold-Wallpaper';
    case RewindForwardCircle = 'solar-bold-rewind_forward_circle';
    case GalleryEdit = 'solar-bold-gallery_edit';
    case Gallery = 'solar-bold-Gallery';
    case GalleryMinimalistic = 'solar-bold-gallery_minimalistic';
    case UploadTrack = 'solar-bold-upload_track';
    case Volume = 'solar-bold-Volume';
    case UploadTrack2 = 'solar-bold-upload_track2';
    case MusicNotes = 'solar-bold-music_notes';
    case MusicNote2 = 'solar-bold-music_note2';
    case CameraAdd = 'solar-bold-camera_add';
    case Podcast = 'solar-bold-Podcast';
    case CameraRotate = 'solar-bold-camera_rotate';
    case MusicNote3 = 'solar-bold-music_note3';
    case Stop = 'solar-bold-Stop';
    case Muted = 'solar-bold-Muted';
    case SkipNext = 'solar-bold-skip_next';
    case GallerySend = 'solar-bold-gallery_send';
    case Record = 'solar-bold-Record';
    case FullScreenCircle = 'solar-bold-full_screen_circle';
    case VolumeCross = 'solar-bold-volume_cross';
    case SoundwaveCircle = 'solar-bold-soundwave_circle';
    case SkipPrevious = 'solar-bold-skip_previous';
    case Rewind5SecondsForward = 'solar-bold-rewind5_seconds_forward';
    case Play = 'solar-bold-Play';
    case PIP = 'solar-bold-PIP';
    case MusicLibrary = 'solar-bold-music_library';
    case VideoFrame2 = 'solar-bold-video_frame2';
    case Camera = 'solar-bold-Camera';
    case QuitPip = 'solar-bold-quit_pip';
    case ClapperboardOpenPlay = 'solar-bold-clapperboard_open_play';
    case Rewind10SecondsBack = 'solar-bold-rewind10_seconds_back';
    case RepeatOneMinimalistic = 'solar-bold-repeat_one_minimalistic';
    case Vinyl = 'solar-bold-Vinyl';
    case VideoLibrary = 'solar-bold-video_library';
    case GalleryWide = 'solar-bold-gallery_wide';
    case Reel = 'solar-bold-Reel';
    case ToPip = 'solar-bold-to_pip';
    case Pip2 = 'solar-bold-pip2';
    case FullScreen = 'solar-bold-full_screen';
    case CameraMinimalistic = 'solar-bold-camera_minimalistic';
    case VideoFrameCut2 = 'solar-bold-video_frame_cut2';
    case GalleryCircle = 'solar-bold-gallery_circle';
    case VideoFramePlayHorizontal = 'solar-bold-video_frame_play_horizontal';
    case MusicNoteSlider2 = 'solar-bold-music_note_slider2';
    case MusicNoteSlider = 'solar-bold-music_note_slider';
    case VideocameraAdd = 'solar-bold-videocamera_add';
    case QuitFullScreenSquare = 'solar-bold-quit_full_screen_square';
    case Album = 'solar-bold-Album';
    case GalleryAdd = 'solar-bold-gallery_add';
    case CameraSquare = 'solar-bold-camera_square';
    case Rewind15SecondsBack = 'solar-bold-rewind15_seconds_back';
    case Rewind15SecondsForward = 'solar-bold-rewind15_seconds_forward';
    case VinylRecord = 'solar-bold-vinyl_record';
    case Shuffle = 'solar-bold-Shuffle';
    case Pause = 'solar-bold-Pause';
    case MusicNote = 'solar-bold-music_note';
    case QuitFullScreen = 'solar-bold-quit_full_screen';
    case Microphone2 = 'solar-bold-microphone2';
    case Videocamera = 'solar-bold-Videocamera';
    case GalleryFavourite = 'solar-bold-gallery_favourite';
    case MusicLibrary2 = 'solar-bold-music_library2';
    case VideoFramePlayVertical = 'solar-bold-video_frame_play_vertical';
    case FullScreenSquare = 'solar-bold-full_screen_square';
    case Rewind10SecondsForward = 'solar-bold-rewind10_seconds_forward';
    case VolumeLoud = 'solar-bold-volume_loud';
    case Microphone3 = 'solar-bold-microphone3';
    case SoundwaveSquare = 'solar-bold-soundwave_square';
    case Cardholder = 'solar-bold-Cardholder';
    case BillList = 'solar-bold-bill_list';
    case SaleSquare = 'solar-bold-sale_square';
    case Dollar = 'solar-bold-Dollar';
    case Ticket = 'solar-bold-Ticket';
    case Tag = 'solar-bold-Tag';
    case CashOut = 'solar-bold-cash_out';
    case Wallet2 = 'solar-bold-wallet2';
    case Ruble = 'solar-bold-Ruble';
    case CardTransfer = 'solar-bold-card_transfer';
    case Euro = 'solar-bold-Euro';
    case Sale = 'solar-bold-Sale';
    case CardSearch = 'solar-bold-card_search';
    case Wallet = 'solar-bold-Wallet';
    case BillCross = 'solar-bold-bill_cross';
    case TicketSale = 'solar-bold-ticket_sale';
    case SafeSquare = 'solar-bold-safe_square';
    case Card = 'solar-bold-Card';
    case Safe2 = 'solar-bold-safe2';
    case DollarMinimalistic = 'solar-bold-dollar_minimalistic';
    case TagPrice = 'solar-bold-tag_price';
    case MoneyBag = 'solar-bold-money_bag';
    case Bill = 'solar-bold-Bill';
    case CardSend = 'solar-bold-card_send';
    case CardRecive = 'solar-bold-card_recive';
    case Banknote2 = 'solar-bold-banknote2';
    case TagHorizontal = 'solar-bold-tag_horizontal';
    case BillCheck = 'solar-bold-bill_check';
    case TickerStar = 'solar-bold-ticker_star';
    case Banknote = 'solar-bold-Banknote';
    case VerifiedCheck = 'solar-bold-verified_check';
    case WadOfMoney = 'solar-bold-wad_of_money';
    case Card2 = 'solar-bold-card2';
    case SafeCircle = 'solar-bold-safe_circle';
    case WalletMoney = 'solar-bold-wallet_money';
    case List = 'solar-bold-List';
    case ListDownMinimalistic = 'solar-bold-list_down_minimalistic';
    case Playlist2 = 'solar-bold-playlist2';
    case ChecklistMinimalistic = 'solar-bold-checklist_minimalistic';
    case PlaaylistMinimalistic = 'solar-bold-plaaylist_minimalistic';
    case ListHeart = 'solar-bold-list_heart';
    case ListArrowDown = 'solar-bold-list_arrow_down';
    case ListArrowUp = 'solar-bold-list_arrow_up';
    case ListUpMinimalistic = 'solar-bold-list_up_minimalistic';
    case Playlist = 'solar-bold-Playlist';
    case ListUp = 'solar-bold-list_up';
    case ListCrossMinimalistic = 'solar-bold-list_cross_minimalistic';
    case ListCross = 'solar-bold-list_cross';
    case ListArrowDownMinimalistic = 'solar-bold-list_arrow_down_minimalistic';
    case SortByAlphabet = 'solar-bold-sort_by_alphabet';
    case Checklist = 'solar-bold-Checklist';
    case SortFromBottomToTop = 'solar-bold-sort_from_bottom_to_top';
    case ListCheck = 'solar-bold-list_check';
    case PlaylistMinimalistic2 = 'solar-bold-playlist_minimalistic2';
    case PlaylistMinimalistic3 = 'solar-bold-playlist_minimalistic3';
    case List1 = 'solar-bold-list1';
    case SortFromTopToBottom = 'solar-bold-sort_from_top_to_bottom';
    case SortByTime = 'solar-bold-sort_by_time';
    case ListDown = 'solar-bold-list_down';
    case ListHeartMinimalistic = 'solar-bold-list_heart_minimalistic';
    case ListCheckMinimalistic = 'solar-bold-list_check_minimalistic';
    case ListArrowUpMinimalistic = 'solar-bold-list_arrow_up_minimalistic';
    case UserCrossRounded = 'solar-bold-user_cross_rounded';
    case User = 'solar-bold-User';
    case UsersGroupRounded = 'solar-bold-users_group_rounded';
    case UserPlusRounded = 'solar-bold-user_plus_rounded';
    case UserBlock = 'solar-bold-user_block';
    case UserMinus = 'solar-bold-user_minus';
    case UserHands = 'solar-bold-user_hands';
    case UserHeart = 'solar-bold-user_heart';
    case UserMinusRounded = 'solar-bold-user_minus_rounded';
    case UserCross = 'solar-bold-user_cross';
    case UserSpeakRounded = 'solar-bold-user_speak_rounded';
    case UserId = 'solar-bold-user_id';
    case UserBlockRounded = 'solar-bold-user_block_rounded';
    case UserHeartRounded = 'solar-bold-user_heart_rounded';
    case UsersGroupTwoRounded = 'solar-bold-users_group_two_rounded';
    case UserHandUp = 'solar-bold-user_hand_up';
    case UserCircle = 'solar-bold-user_circle';
    case UserRounded = 'solar-bold-user_rounded';
    case UserCheck = 'solar-bold-user_check';
    case UserPlus = 'solar-bold-user_plus';
    case UserCheckRounded = 'solar-bold-user_check_rounded';
    case UserSpeak = 'solar-bold-user_speak';
    case Virus = 'solar-bold-Virus';
    case AdhesivePlaster2 = 'solar-bold-adhesive_plaster2';
    case Dropper = 'solar-bold-Dropper';
    case Pulse2 = 'solar-bold-pulse2';
    case BoneBroken = 'solar-bold-bone_broken';
    case HeartPulse2 = 'solar-bold-heart_pulse2';
    case MedicalKit = 'solar-bold-medical_kit';
    case TestTube = 'solar-bold-test_tube';
    case Health = 'solar-bold-Health';
    case DropperMinimalistic2 = 'solar-bold-dropper_minimalistic2';
    case DNA = 'solar-bold-DNA';
    case Dropper3 = 'solar-bold-dropper3';
    case Thermometer = 'solar-bold-Thermometer';
    case Dropper2 = 'solar-bold-dropper2';
    case JarOfPills2 = 'solar-bold-jar_of_pills2';
    case BoneCrack = 'solar-bold-bone_crack';
    case JarOfPills = 'solar-bold-jar_of_pills';
    case Syringe = 'solar-bold-Syringe';
    case Stethoscope = 'solar-bold-Stethoscope';
    case BenzeneRing = 'solar-bold-benzene_ring';
    case Bacteria = 'solar-bold-Bacteria';
    case AdhesivePlaster = 'solar-bold-adhesive_plaster';
    case Bone = 'solar-bold-Bone';
    case Bones = 'solar-bold-Bones';
    case Pill = 'solar-bold-Pill';
    case Pills = 'solar-bold-Pills';
    case HeartPulse = 'solar-bold-heart_pulse';
    case TestTubeMinimalistic = 'solar-bold-test_tube_minimalistic';
    case Pills2 = 'solar-bold-pills2';
    case Pulse = 'solar-bold-Pulse';
    case DropperMinimalistic = 'solar-bold-dropper_minimalistic';
    case Pills3 = 'solar-bold-pills3';
    case Whisk = 'solar-bold-Whisk';
    case Bottle = 'solar-bold-Bottle';
    case OvenMittsMinimalistic = 'solar-bold-oven_mitts_minimalistic';
    case ChefHatMinimalistic = 'solar-bold-chef_hat_minimalistic';
    case TeaCup = 'solar-bold-tea_cup';
    case WineglassTriangle = 'solar-bold-wineglass_triangle';
    case OvenMitts = 'solar-bold-oven_mitts';
    case CupPaper = 'solar-bold-cup_paper';
    case Ladle = 'solar-bold-Ladle';
    case Corkscrew = 'solar-bold-Corkscrew';
    case DonutBitten = 'solar-bold-donut_bitten';
    case Wineglass = 'solar-bold-Wineglass';
    case Donut = 'solar-bold-Donut';
    case CupHot = 'solar-bold-cup_hot';
    case ChefHatHeart = 'solar-bold-chef_hat_heart';
    case ChefHat = 'solar-bold-chef_hat';
    case RollingPin = 'solar-bold-rolling_pin';
    case CodeFile = 'solar-bold-code_file';
    case FileCorrupted = 'solar-bold-file_corrupted';
    case File = 'solar-bold-File';
    case FileRight = 'solar-bold-file_right';
    case FileFavourite = 'solar-bold-file_favourite';
    case FileDownload = 'solar-bold-file_download';
    case ZipFile = 'solar-bold-zip_file';
    case FileText = 'solar-bold-file_text';
    case FileSmile = 'solar-bold-file_smile_)';
    case FileCheck = 'solar-bold-file_check';
    case FileSend = 'solar-bold-file_send';
    case FileLeft = 'solar-bold-file_left';
    case FigmaFile = 'solar-bold-figma_file';
    case FileRemove = 'solar-bold-file_remove';
    case CloudFile = 'solar-bold-cloud_file';
    case Suspension = 'solar-bold-Suspension';
    case SpedometerMax = 'solar-bold-spedometer_max';
    case TransmissionCircle = 'solar-bold-transmission_circle';
    case GasStation = 'solar-bold-gas_station';
    case Wheel = 'solar-bold-Wheel';
    case Transmission = 'solar-bold-Transmission';
    case KickScooter = 'solar-bold-kick_scooter';
    case SpedometerLow = 'solar-bold-spedometer_low';
    case SpedometerMiddle = 'solar-bold-spedometer_middle';
    case WheelAngle = 'solar-bold-wheel_angle';
    case Tram = 'solar-bold-Tram';
    case TransmissionSquare = 'solar-bold-transmission_square';
    case Scooter = 'solar-bold-Scooter';
    case ShockAbsorber = 'solar-bold-shock_absorber';
    case Bus = 'solar-bold-Bus';
    case SuspensionCross = 'solar-bold-suspension_cross';
    case SuspensionBolt = 'solar-bold-suspension_bolt';
    case ElectricRefueling = 'solar-bold-electric_refueling';
    case Accumulator = 'solar-bold-Accumulator';
    case HandPills = 'solar-bold-hand_pills';
    case HandMoney = 'solar-bold-hand_money';
    case HandShake = 'solar-bold-hand_shake';
    case HandHeart = 'solar-bold-hand_heart';
    case HandStars = 'solar-bold-hand_stars';
    case RemoveFolder = 'solar-bold-remove_folder';
    case FolderFavouritestar = 'solar-bold-folder_favourite(star)';
    case AddFolder = 'solar-bold-add_folder';
    case FolderCheck = 'solar-bold-folder_check';
    case FolderFavouritebookmark = 'solar-bold-folder_favourite(bookmark)';
    case Folder2 = 'solar-bold-folder2';
    case FolderSecurity = 'solar-bold-folder_security';
    case FolderCloud = 'solar-bold-folder_cloud';
    case MoveToFolder = 'solar-bold-move_to_folder';
    case FolderError = 'solar-bold-folder_error';
    case FolderPathConnect = 'solar-bold-folder_path_connect';
    case FolderOpen = 'solar-bold-folder_open';
    case Folder = 'solar-bold-Folder';
    case FolderWithFiles = 'solar-bold-folder_with_files';
    case CloudCheck = 'solar-bold-cloud_check';
    case Temperature = 'solar-bold-Temperature';
    case Wind = 'solar-bold-Wind';
    case CloudSnowfall = 'solar-bold-cloud_snowfall';
    case Sunrise = 'solar-bold-Sunrise';
    case Sun2 = 'solar-bold-sun2';
    case CloudSun = 'solar-bold-cloud_sun';
    case CloudBoltMinimalistic = 'solar-bold-cloud_bolt_minimalistic';
    case CloudDownload = 'solar-bold-cloud_download';
    case Clouds = 'solar-bold-Clouds';
    case Tornado = 'solar-bold-Tornado';
    case MoonSleep = 'solar-bold-moon_sleep';
    case CloudUpload = 'solar-bold-cloud_upload';
    case CloudRain = 'solar-bold-cloud_rain';
    case Fog = 'solar-bold-Fog';
    case Snowflake = 'solar-bold-Snowflake';
    case MoonFog = 'solar-bold-moon_fog';
    case CloudMinus = 'solar-bold-cloud_minus';
    case CloudBolt = 'solar-bold-cloud_bolt';
    case CloudWaterdrop = 'solar-bold-cloud_waterdrop';
    case Sunset = 'solar-bold-Sunset';
    case Waterdrops = 'solar-bold-Waterdrops';
    case MoonStars = 'solar-bold-moon_stars';
    case CloudPlus = 'solar-bold-cloud_plus';
    case Sun = 'solar-bold-Sun';
    case CloudWaterdrops = 'solar-bold-cloud_waterdrops';
    case CloudSun2 = 'solar-bold-cloud_sun2';
    case CloudyMoon = 'solar-bold-cloudy_moon';
    case TornadoSmall = 'solar-bold-tornado_small';
    case Cloud = 'solar-bold-Cloud';
    case SunFog = 'solar-bold-sun_fog';
    case CloundCross = 'solar-bold-clound_cross';
    case CloudSnowfallMinimalistic = 'solar-bold-cloud_snowfall_minimalistic';
    case CloudStorm = 'solar-bold-cloud_storm';
    case Moon = 'solar-bold-Moon';
    case RefreshCircle = 'solar-bold-refresh_circle';
    case SquareArrowRightDown = 'solar-bold-square_arrow_right_down';
    case RoundArrowLeftDown = 'solar-bold-round_arrow_left_down';
    case Restart = 'solar-bold-Restart';
    case RoundAltArrowDown = 'solar-bold-round_alt_arrow_down';
    case RoundSortVertical = 'solar-bold-round_sort_vertical';
    case SquareAltArrowUp = 'solar-bold-square_alt_arrow_up';
    case ArrowLeftUp = 'solar-bold-arrow_left_up';
    case SortHorizontal = 'solar-bold-sort_horizontal';
    case TransferHorizontal = 'solar-bold-transfer_horizontal';
    case SquareDoubleAltArrowUp = 'solar-bold-square_double_alt_arrow_up';
    case RoundArrowLeftUp = 'solar-bold-round_arrow_left_up';
    case AltArrowRight = 'solar-bold-alt_arrow_right';
    case RoundDoubleAltArrowUp = 'solar-bold-round_double_alt_arrow_up';
    case RestartCircle = 'solar-bold-restart_circle';
    case SquareArrowDown = 'solar-bold-square_arrow_down';
    case SortVertical = 'solar-bold-sort_vertical';
    case SquareSortHorizontal = 'solar-bold-square_sort_horizontal';
    case DoubleAltArrowLeft = 'solar-bold-double_alt_arrow_left';
    case SquareAltArrowDown = 'solar-bold-square_alt_arrow_down';
    case SquareAltArrowRight = 'solar-bold-square_alt_arrow_right';
    case SquareArrowUp = 'solar-bold-square_arrow_up';
    case DoubleAltArrowRight = 'solar-bold-double_alt_arrow_right';
    case RoundTransferVertical = 'solar-bold-round_transfer_vertical';
    case ArrowLeft = 'solar-bold-arrow_left';
    case RoundDoubleAltArrowRight = 'solar-bold-round_double_alt_arrow_right';
    case SquareDoubleAltArrowLeft = 'solar-bold-square_double_alt_arrow_left';
    case AltArrowDown = 'solar-bold-alt_arrow_down';
    case RoundTransferHorizontal = 'solar-bold-round_transfer_horizontal';
    case RoundArrowRightDown = 'solar-bold-round_arrow_right_down';
    case ArrowUp = 'solar-bold-arrow_up';
    case RoundArrowLeft = 'solar-bold-round_arrow_left';
    case DoubleAltArrowUp = 'solar-bold-double_alt_arrow_up';
    case RoundArrowRight = 'solar-bold-round_arrow_right';
    case SquareTransferHorizontal = 'solar-bold-square_transfer_horizontal';
    case ArrowRight = 'solar-bold-arrow_right';
    case RoundDoubleAltArrowLeft = 'solar-bold-round_double_alt_arrow_left';
    case RoundArrowUp = 'solar-bold-round_arrow_up';
    case SquareSortVertical = 'solar-bold-square_sort_vertical';
    case AltArrowLeft = 'solar-bold-alt_arrow_left';
    case SquareDoubleAltArrowRight = 'solar-bold-square_double_alt_arrow_right';
    case Refresh = 'solar-bold-Refresh';
    case TransferVertical = 'solar-bold-transfer_vertical';
    case RefreshSquare = 'solar-bold-refresh_square';
    case SquareTransferVertical = 'solar-bold-square_transfer_vertical';
    case SquareDoubleAltArrowDown = 'solar-bold-square_double_alt_arrow_down';
    case RoundArrowRightUp = 'solar-bold-round_arrow_right_up';
    case ArrowDown = 'solar-bold-arrow_down';
    case RestartSquare = 'solar-bold-restart_square';
    case SquareArrowRight = 'solar-bold-square_arrow_right';
    case RoundDoubleAltArrowDown = 'solar-bold-round_double_alt_arrow_down';
    case SquareArrowLeftUp = 'solar-bold-square_arrow_left_up';
    case RoundArrowDown = 'solar-bold-round_arrow_down';
    case SquareArrowRightUp = 'solar-bold-square_arrow_right_up';
    case RoundTransferDiagonal = 'solar-bold-round_transfer_diagonal';
    case ArrowRightDown = 'solar-bold-arrow_right_down';
    case ArrowLeftDown = 'solar-bold-arrow_left_down';
    case RoundAltArrowLeft = 'solar-bold-round_alt_arrow_left';
    case ArrowRightUp = 'solar-bold-arrow_right_up';
    case SquareArrowLeftDown = 'solar-bold-square_arrow_left_down';
    case RoundAltArrowUp = 'solar-bold-round_alt_arrow_up';
    case AltArrowUp = 'solar-bold-alt_arrow_up';
    case SquareAltArrowLeft = 'solar-bold-square_alt_arrow_left';
    case RoundSortHorizontal = 'solar-bold-round_sort_horizontal';
    case DoubleAltArrowDown = 'solar-bold-double_alt_arrow_down';
    case RoundAltArrowRight = 'solar-bold-round_alt_arrow_right';
    case SquareArrowLeft = 'solar-bold-square_arrow_left';
    case TuningSquare2 = 'solar-bold-tuning_square2';
    case WidgetAdd = 'solar-bold-widget_add';
    case TuningSquare = 'solar-bold-tuning_square';
    case SettingsMinimalistic = 'solar-bold-settings_minimalistic';
    case Widget6 = 'solar-bold-widget6';
    case Widget4 = 'solar-bold-widget4';
    case Settings = 'solar-bold-Settings';
    case Widget5 = 'solar-bold-widget5';
    case Widget2 = 'solar-bold-widget2';
    case Widget3 = 'solar-bold-widget3';
    case Tuning2 = 'solar-bold-tuning2';
    case Tuning3 = 'solar-bold-tuning3';
    case Widget = 'solar-bold-Widget';
    case Tuning4 = 'solar-bold-tuning4';
    case Tuning = 'solar-bold-Tuning';
    case DiagramDown = 'solar-bold-diagram_down';
    case Chart2 = 'solar-bold-chart2';
    case Chart = 'solar-bold-Chart';
    case DiagramUp = 'solar-bold-diagram_up';
    case GraphNew = 'solar-bold-graph_new';
    case CourseUp = 'solar-bold-course_up';
    case GraphDownNew = 'solar-bold-graph_down_new';
    case PieChart3 = 'solar-bold-pie_chart3';
    case PieChart2 = 'solar-bold-pie_chart2';
    case GraphNewUp = 'solar-bold-graph_new_up';
    case PieChart = 'solar-bold-pie_chart';
    case RoundGraph = 'solar-bold-round_graph';
    case GraphUp = 'solar-bold-graph_up';
    case ChartSquare = 'solar-bold-chart_square';
    case CourseDown = 'solar-bold-course_down';
    case ChatSquare2 = 'solar-bold-chat_square2';
    case GraphDown = 'solar-bold-graph_down';
    case Graph = 'solar-bold-Graph';
    case PresentationGraph = 'solar-bold-presentation_graph';
    case MaximizeSquare3 = 'solar-bold-maximize_square3';
    case MaximizeSquareMinimalistic = 'solar-bold-maximize_square_minimalistic';
    case MaximizeSquare2 = 'solar-bold-maximize_square2';
    case MinimizeSquare = 'solar-bold-minimize_square';
    case DownloadSquare = 'solar-bold-download_square';
    case UndoLeftRoundSquare = 'solar-bold-undo_left_round_square';
    case Reply = 'solar-bold-Reply';
    case Logout = 'solar-bold-Logout';
    case ReciveSquare = 'solar-bold-recive_square';
    case Export = 'solar-bold-Export';
    case SendTwiceSquare = 'solar-bold-send_twice_square';
    case UndoLeftRound = 'solar-bold-undo_left_round';
    case Forward2 = 'solar-bold-forward2';
    case Maximize = 'solar-bold-Maximize';
    case UndoRightRound = 'solar-bold-undo_right_round';
    case MinimizeSquare2 = 'solar-bold-minimize_square2';
    case MinimizeSquare3 = 'solar-bold-minimize_square3';
    case UploadTwiceSquare = 'solar-bold-upload_twice_square';
    case Minimize = 'solar-bold-Minimize';
    case CircleTopUp = 'solar-bold-circle_top_up';
    case UploadMinimalistic = 'solar-bold-upload_minimalistic';
    case Download = 'solar-bold-Download';
    case Import = 'solar-bold-Import';
    case Login = 'solar-bold-Login';
    case UndoLeft = 'solar-bold-undo_left';
    case SquareTopUp = 'solar-bold-square_top_up';
    case DownloadTwiceSquare = 'solar-bold-download_twice_square';
    case CircleBottomDown = 'solar-bold-circle_bottom_down';
    case MaximizeSquare = 'solar-bold-maximize_square';
    case UploadSquare = 'solar-bold-upload_square';
    case UndoRightSquare = 'solar-bold-undo_right_square';
    case ReciveTwiceSquare = 'solar-bold-recive_twice_square';
    case CircleTopDown = 'solar-bold-circle_top_down';
    case ArrowToDownLeft = 'solar-bold-arrow_to_down_left';
    case Logout2 = 'solar-bold-logout2';
    case Logout3 = 'solar-bold-logout3';
    case Scale = 'solar-bold-Scale';
    case ArrowToDownRight = 'solar-bold-arrow_to_down_right';
    case DownloadMinimalistic = 'solar-bold-download_minimalistic';
    case MinimizeSquareMinimalistic = 'solar-bold-minimize_square_minimalistic';
    case Reply2 = 'solar-bold-reply2';
    case SquareBottomUp = 'solar-bold-square_bottom_up';
    case UndoRight = 'solar-bold-undo_right';
    case UndoLeftSquare = 'solar-bold-undo_left_square';
    case SendSquare = 'solar-bold-send_square';
    case Exit = 'solar-bold-Exit';
    case SquareBottomDown = 'solar-bold-square_bottom_down';
    case UndoRightRoundSquare = 'solar-bold-undo_right_round_square';
    case ArrowToTopLeft = 'solar-bold-arrow_to_top_left';
    case CircleBottomUp = 'solar-bold-circle_bottom_up';
    case ScreenShare = 'solar-bold-screen_share';
    case Upload = 'solar-bold-Upload';
    case SquareTopDown = 'solar-bold-square_top_down';
    case ArrowToTopRight = 'solar-bold-arrow_to_top_right';
    case Login3 = 'solar-bold-login3';
    case Login2 = 'solar-bold-login2';
    case City = 'solar-bold-City';
    case Buildings = 'solar-bold-Buildings';
    case Buildings3 = 'solar-bold-buildings3';
    case Buildings2 = 'solar-bold-buildings2';
    case Hospital = 'solar-bold-Hospital';
    case Garage = 'solar-bold-Garage';
    case Passport = 'solar-bold-Passport';
    case DiplomaVerified = 'solar-bold-diploma_verified';
    case CaseRound = 'solar-bold-case_round';
    case Backpack = 'solar-bold-Backpack';
    case Book2 = 'solar-bold-book2';
    case SquareAcademicCap2 = 'solar-bold-square_academic_cap2';
    case CaseRoundMinimalistic = 'solar-bold-case_round_minimalistic';
    case Case = 'solar-bold-Case';
    case BookBookmarkMinimalistic = 'solar-bold-book_bookmark_minimalistic';
    case BookmarkOpened = 'solar-bold-bookmark_opened';
    case Diploma = 'solar-bold-Diploma';
    case Book = 'solar-bold-Book';
    case SquareAcademicCap = 'solar-bold-square_academic_cap';
    case BookmarkCircle = 'solar-bold-bookmark_circle';
    case CalculatorMinimalistic = 'solar-bold-calculator_minimalistic';
    case NotebookSquare = 'solar-bold-notebook_square';
    case BookMinimalistic = 'solar-bold-book_minimalistic';
    case CaseMinimalistic = 'solar-bold-case_minimalistic';
    case NotebookBookmark = 'solar-bold-notebook_bookmark';
    case PassportMinimalistic = 'solar-bold-passport_minimalistic';
    case BookBookmark = 'solar-bold-book_bookmark';
    case BookmarkSquareMinimalistic = 'solar-bold-bookmark_square_minimalistic';
    case Bookmark = 'solar-bold-Bookmark';
    case PlusMinus = 'solar-bold-plus,_minus';
    case Calculator = 'solar-bold-Calculator';
    case BookmarkSquare = 'solar-bold-bookmark_square';
    case NotebookMinimalistic = 'solar-bold-notebook_minimalistic';
    case FireSquare = 'solar-bold-fire_square';
    case SuitcaseLines = 'solar-bold-suitcase_lines';
    case Fire = 'solar-bold-Fire';
    case Bonfire = 'solar-bold-Bonfire';
    case SuitcaseTag = 'solar-bold-suitcase_tag';
    case Leaf = 'solar-bold-Leaf';
    case Suitcase = 'solar-bold-Suitcase';
    case Flame = 'solar-bold-Flame';
    case FireMinimalistic = 'solar-bold-fire_minimalistic';
    case BellBing = 'solar-bold-bell_bing';
    case NotificationLinesRemove = 'solar-bold-notification_lines_remove';
    case NotificationUnread = 'solar-bold-notification_unread';
    case Bell = 'solar-bold-Bell';
    case NotificationRemove = 'solar-bold-notification_remove';
    case NotificationUnreadLines = 'solar-bold-notification_unread_lines';
    case BellOff = 'solar-bold-bell_off';
    case Lightning = 'solar-bold-Lightning';
    case LightbulbMinimalistic = 'solar-bold-lightbulb_minimalistic';
    case ServerSquareCloud = 'solar-bold-server_square_cloud';
    case LightbulbBolt = 'solar-bold-lightbulb_bolt';
    case AirbudsCharge = 'solar-bold-airbuds_charge';
    case ServerPath = 'solar-bold-server_path';
    case SimCardMinimalistic = 'solar-bold-sim_card_minimalistic';
    case Smartphone = 'solar-bold-Smartphone';
    case Turntable = 'solar-bold-Turntable';
    case AirbudsCheck = 'solar-bold-airbuds_check';
    case MouseMinimalistic = 'solar-bold-mouse_minimalistic';
    case SmartphoneRotateAngle = 'solar-bold-smartphone_rotate_angle';
    case RadioMinimalistic = 'solar-bold-radio_minimalistic';
    case Airbuds = 'solar-bold-Airbuds';
    case SmartphoneRotateOrientation = 'solar-bold-smartphone_rotate_orientation';
    case IPhone = 'solar-bold-i_phone';
    case SimCard = 'solar-bold-sim_card';
    case FlashDrive = 'solar-bold-flash_drive';
    case Devices = 'solar-bold-Devices';
    case SimCards = 'solar-bold-sim_cards';
    case AirbudsCaseOpen = 'solar-bold-airbuds_case_open';
    case TurntableMusicNote = 'solar-bold-turntable_music_note';
    case Keyboard = 'solar-bold-Keyboard';
    case GamepadCharge = 'solar-bold-gamepad_charge';
    case Boombox = 'solar-bold-Boombox';
    case SmartSpeakerMinimalistic = 'solar-bold-smart_speaker_minimalistic';
    case Telescope = 'solar-bold-Telescope';
    case MonitorCamera = 'solar-bold-monitor_camera';
    case LaptopMinimalistic = 'solar-bold-laptop_minimalistic';
    case Server2 = 'solar-bold-server2';
    case SmartSpeaker = 'solar-bold-smart_speaker';
    case Projector = 'solar-bold-Projector';
    case Server = 'solar-bold-Server';
    case TV = 'solar-bold-TV';
    case Cassette2 = 'solar-bold-cassette2';
    case Radio = 'solar-bold-Radio';
    case SmartphoneVibration = 'solar-bold-smartphone_vibration';
    case AirbudsLeft = 'solar-bold-airbuds_left';
    case HeadphonesRound = 'solar-bold-headphones_round';
    case Gameboy = 'solar-bold-Gameboy';
    case HeadphonesRoundSound = 'solar-bold-headphones_round_sound';
    case CPU = 'solar-bold-CPU';
    case Printer2 = 'solar-bold-printer2';
    case HeadphonesSquare = 'solar-bold-headphones_square';
    case ServerSquareUpdate = 'solar-bold-server_square_update';
    case PrinterMinimalistic = 'solar-bold-printer_minimalistic';
    case Bluetooth = 'solar-bold-Bluetooth';
    case WirelessCharge = 'solar-bold-wireless_charge';
    case BluetoothCircle = 'solar-bold-bluetooth_circle';
    case AirbudsCaseMinimalistic = 'solar-bold-airbuds_case_minimalistic';
    case Lightbulb = 'solar-bold-Lightbulb';
    case AirbudsRemove = 'solar-bold-airbuds_remove';
    case SmartphoneRotate2 = 'solar-bold-smartphone_rotate2';
    case SsdSquare = 'solar-bold-ssd_square';
    case Printer = 'solar-bold-Printer';
    case Smartphone2 = 'solar-bold-smartphone2';
    case ServerMinimalistic = 'solar-bold-server_minimalistic';
    case HeadphonesSquareSound = 'solar-bold-headphones_square_sound';
    case Diskette = 'solar-bold-Diskette';
    case BluetoothWave = 'solar-bold-bluetooth_wave';
    case SmartSpeaker2 = 'solar-bold-smart_speaker2';
    case Laptop3 = 'solar-bold-laptop3';
    case Laptop2 = 'solar-bold-laptop2';
    case MouseCircle = 'solar-bold-mouse_circle';
    case TurntableMinimalistic = 'solar-bold-turntable_minimalistic';
    case SmartphoneUpdate = 'solar-bold-smartphone_update';
    case GamepadMinimalistic = 'solar-bold-gamepad_minimalistic';
    case SdCard = 'solar-bold-sd_card';
    case PlugCircle = 'solar-bold-plug_circle';
    case AirbudsCase = 'solar-bold-airbuds_case';
    case SsdRound = 'solar-bold-ssd_round';
    case Laptop = 'solar-bold-Laptop';
    case AirbudsRight = 'solar-bold-airbuds_right';
    case Display = 'solar-bold-Display';
    case MonitorSmartphone = 'solar-bold-monitor_smartphone';
    case Socket = 'solar-bold-Socket';
    case GamepadOld = 'solar-bold-gamepad_old';
    case CpuBolt = 'solar-bold-cpu_bolt';
    case AirbudsCaseCharge = 'solar-bold-airbuds_case_charge';
    case Tablet = 'solar-bold-Tablet';
    case Weigher = 'solar-bold-Weigher';
    case ServerSquare = 'solar-bold-server_square';
    case Mouse = 'solar-bold-Mouse';
    case GamepadNoCharge = 'solar-bold-gamepad_no_charge';
    case BluetoothSquare = 'solar-bold-bluetooth_square';
    case CloudStorage = 'solar-bold-cloud_storage';
    case Gamepad = 'solar-bold-Gamepad';
    case Monitor = 'solar-bold-Monitor';
    case Cassette = 'solar-bold-Cassette';
    // Outline Style (1205 icons)
    case OutlineFacemaskCircle = 'solar-outline-facemask_circle';
    case OutlineConfoundedCircle = 'solar-outline-confounded_circle';
    case OutlineSadSquare = 'solar-outline-sad_square';
    case OutlineSleepingCircle = 'solar-outline-sleeping_circle';
    case OutlineFaceScanCircle = 'solar-outline-face_scan_circle';
    case OutlineSmileCircle = 'solar-outline-smile_circle';
    case OutlineStickerSmileCircle = 'solar-outline-sticker_smile_circle';
    case OutlineStickerSquare = 'solar-outline-sticker_square';
    case OutlineEmojiFunnyCircle = 'solar-outline-emoji_funny_circle';
    case OutlineExpressionlessSquare = 'solar-outline-expressionless_square';
    case OutlineSleepingSquare = 'solar-outline-sleeping_square';
    case OutlineSadCircle = 'solar-outline-sad_circle';
    case OutlineFacemaskSquare = 'solar-outline-facemask_square';
    case OutlineConfoundedSquare = 'solar-outline-confounded_square';
    case OutlineFaceScanSquare = 'solar-outline-face_scan_square';
    case OutlineSmileSquare = 'solar-outline-smile_square';
    case OutlineStickerSmileCircle2 = 'solar-outline-sticker_smile_circle2';
    case OutlineStickerSmileSquare = 'solar-outline-sticker_smile_square';
    case OutlineEmojiFunnySquare = 'solar-outline-emoji_funny_square';
    case OutlineStickerCircle = 'solar-outline-sticker_circle';
    case OutlineExpressionlessCircle = 'solar-outline-expressionless_circle';
    case OutlineLike = 'solar-outline-Like';
    case OutlineMedalStarSquare = 'solar-outline-medal_star_square';
    case OutlineDislike = 'solar-outline-Dislike';
    case OutlineStarShine = 'solar-outline-star_shine';
    case OutlineHeartAngle = 'solar-outline-heart_angle';
    case OutlineMedalRibbon = 'solar-outline-medal_ribbon';
    case OutlineHeartShine = 'solar-outline-heart_shine';
    case OutlineMedalStarCircle = 'solar-outline-medal_star_circle';
    case OutlineMedalRibbonsStar = 'solar-outline-medal_ribbons_star';
    case OutlineStar = 'solar-outline-Star';
    case OutlineHeartUnlock = 'solar-outline-heart_unlock';
    case OutlineMedalRibbonStar = 'solar-outline-medal_ribbon_star';
    case OutlineHeartLock = 'solar-outline-heart_lock';
    case OutlineHeartBroken = 'solar-outline-heart_broken';
    case OutlineHearts = 'solar-outline-Hearts';
    case OutlineMedalStar = 'solar-outline-medal_star';
    case OutlineHeart = 'solar-outline-Heart';
    case OutlineCloset = 'solar-outline-Closet';
    case OutlineBed = 'solar-outline-Bed';
    case OutlineWashingMachine = 'solar-outline-washing_machine';
    case OutlineBedsideTable = 'solar-outline-bedside_table';
    case OutlineSofa3 = 'solar-outline-sofa3';
    case OutlineSofa2 = 'solar-outline-sofa2';
    case OutlineChair2 = 'solar-outline-chair2';
    case OutlineBath = 'solar-outline-Bath';
    case OutlineSmartVacuumCleaner2 = 'solar-outline-smart_vacuum_cleaner2';
    case OutlineCondicioner = 'solar-outline-Condicioner';
    case OutlineSmartVacuumCleaner = 'solar-outline-smart_vacuum_cleaner';
    case OutlineRemoteController2 = 'solar-outline-remote_controller2';
    case OutlineFloorLampMinimalistic = 'solar-outline-floor_lamp_minimalistic';
    case OutlineLamp = 'solar-outline-Lamp';
    case OutlineBarChair = 'solar-outline-bar_chair';
    case OutlineBedsideTable2 = 'solar-outline-bedside_table2';
    case OutlineCloset2 = 'solar-outline-closet2';
    case OutlineBedsideTable3 = 'solar-outline-bedside_table3';
    case OutlineSpeaker = 'solar-outline-Speaker';
    case OutlineVolumeKnob = 'solar-outline-volume_knob';
    case OutlineArmchair = 'solar-outline-Armchair';
    case OutlineSpeakerMinimalistic = 'solar-outline-speaker_minimalistic';
    case OutlineRemoteController = 'solar-outline-remote_controller';
    case OutlineTrellis = 'solar-outline-Trellis';
    case OutlineFloorLamp = 'solar-outline-floor_lamp';
    case OutlineCondicioner2 = 'solar-outline-condicioner2';
    case OutlineBedsideTable4 = 'solar-outline-bedside_table4';
    case OutlineArmchair2 = 'solar-outline-armchair2';
    case OutlineWashingMachineMinimalistic = 'solar-outline-washing_machine_minimalistic';
    case OutlineChair = 'solar-outline-Chair';
    case OutlineRemoteControllerMinimalistic = 'solar-outline-remote_controller_minimalistic';
    case OutlineChandelier = 'solar-outline-Chandelier';
    case OutlineFridge = 'solar-outline-Fridge';
    case OutlineMirror = 'solar-outline-Mirror';
    case OutlineSofa = 'solar-outline-Sofa';
    case OutlineEarth = 'solar-outline-Earth';
    case OutlineStarsLine = 'solar-outline-stars_line';
    case OutlineStarFall2 = 'solar-outline-star_fall2';
    case OutlineStarFall = 'solar-outline-star_fall';
    case OutlineBlackHole3 = 'solar-outline-black_hole3';
    case OutlineWomen = 'solar-outline-Women';
    case OutlineBlackHole = 'solar-outline-black_hole';
    case OutlineStarRings = 'solar-outline-star_rings';
    case OutlineBlackHole2 = 'solar-outline-black_hole2';
    case OutlineStarFallMinimalistic2 = 'solar-outline-star_fall_minimalistic2';
    case OutlinePlanet = 'solar-outline-Planet';
    case OutlineSatellite = 'solar-outline-Satellite';
    case OutlineMen = 'solar-outline-Men';
    case OutlineRocket2 = 'solar-outline-rocket2';
    case OutlineStars = 'solar-outline-Stars';
    case OutlineStarAngle = 'solar-outline-star_angle';
    case OutlineInfinity = 'solar-outline-Infinity';
    case OutlineUfo2 = 'solar-outline-ufo2';
    case OutlineUfo3 = 'solar-outline-ufo3';
    case OutlineStarRing = 'solar-outline-star_ring';
    case OutlinePlanet2 = 'solar-outline-planet2';
    case OutlinePlanet3 = 'solar-outline-planet3';
    case OutlineAsteroid = 'solar-outline-Asteroid';
    case OutlineStarsMinimalistic = 'solar-outline-stars_minimalistic';
    case OutlineUFO = 'solar-outline-UFO';
    case OutlinePlanet4 = 'solar-outline-planet4';
    case OutlineRocket = 'solar-outline-Rocket';
    case OutlineStarFallMinimalistic = 'solar-outline-star_fall_minimalistic';
    case OutlineStarRainbow = 'solar-outline-star_rainbow';
    case OutlineAtom = 'solar-outline-Atom';
    case OutlineStarCircle = 'solar-outline-star_circle';
    case OutlineCompassBig = 'solar-outline-compass_big';
    case OutlineMapPointSchool = 'solar-outline-map_point_school';
    case OutlineSignpost = 'solar-outline-Signpost';
    case OutlineMapArrowDown = 'solar-outline-map_arrow_down';
    case OutlineMap = 'solar-outline-Map';
    case OutlineMapArrowUp = 'solar-outline-map_arrow_up';
    case OutlinePointOnMapPerspective = 'solar-outline-point_on_map_perspective';
    case OutlineRadar = 'solar-outline-Radar';
    case OutlineStreets = 'solar-outline-Streets';
    case OutlineMapPointWave = 'solar-outline-map_point_wave';
    case OutlinePeopleNearby = 'solar-outline-people_nearby';
    case OutlineStreetsMapPoint = 'solar-outline-streets_map_point';
    case OutlineMapPointSearch = 'solar-outline-map_point_search';
    case OutlineGPS = 'solar-outline-GPS';
    case OutlineMapArrowSquare = 'solar-outline-map_arrow_square';
    case OutlineBranchingPathsDown = 'solar-outline-branching_paths_down';
    case OutlineMapPointRotate = 'solar-outline-map_point_rotate';
    case OutlineGlobal = 'solar-outline-Global';
    case OutlineCompassSquare = 'solar-outline-compass_square';
    case OutlineRouting3 = 'solar-outline-routing3';
    case OutlineRouting2 = 'solar-outline-routing2';
    case OutlineMapPointRemove = 'solar-outline-map_point_remove';
    case OutlineGlobus = 'solar-outline-Globus';
    case OutlineSignpost2 = 'solar-outline-signpost2';
    case OutlineRadar2 = 'solar-outline-radar2';
    case OutlineStreetsNavigation = 'solar-outline-streets_navigation';
    case OutlineMapPoint = 'solar-outline-map_point';
    case OutlineMapPointHospital = 'solar-outline-map_point_hospital';
    case OutlineCompass = 'solar-outline-Compass';
    case OutlineMapPointAdd = 'solar-outline-map_point_add';
    case OutlineBranchingPathsUp = 'solar-outline-branching_paths_up';
    case OutlineMapPointFavourite = 'solar-outline-map_point_favourite';
    case OutlineRoute = 'solar-outline-Route';
    case OutlinePointOnMap = 'solar-outline-point_on_map';
    case OutlineMapArrowRight = 'solar-outline-map_arrow_right';
    case OutlineRouting = 'solar-outline-Routing';
    case OutlineMapArrowLeft = 'solar-outline-map_arrow_left';
    case OutlineIncognito = 'solar-outline-Incognito';
    case OutlineLockPassword = 'solar-outline-lock_password';
    case OutlineShieldNetwork = 'solar-outline-shield_network';
    case OutlineKeyMinimalisticSquare = 'solar-outline-key_minimalistic_square';
    case OutlineLockKeyholeUnlocked = 'solar-outline-lock_keyhole_unlocked';
    case OutlineLock = 'solar-outline-Lock';
    case OutlineShieldKeyhole = 'solar-outline-shield_keyhole';
    case OutlineEyeClosed = 'solar-outline-eye_closed';
    case OutlineKey = 'solar-outline-Key';
    case OutlineShieldMinus = 'solar-outline-shield_minus';
    case OutlineShield = 'solar-outline-Shield';
    case OutlineLockUnlocked = 'solar-outline-lock_unlocked';
    case OutlineBombMinimalistic = 'solar-outline-bomb_minimalistic';
    case OutlineShieldStar = 'solar-outline-shield_star';
    case OutlineBomb = 'solar-outline-Bomb';
    case OutlineKeySquare = 'solar-outline-key_square';
    case OutlineLockKeyholeMinimalisticUnlocked = 'solar-outline-lock_keyhole_minimalistic_unlocked';
    case OutlineShieldCross = 'solar-outline-shield_cross';
    case OutlineObjectScan = 'solar-outline-object_scan';
    case OutlinePasswordMinimalisticInput = 'solar-outline-password_minimalistic_input';
    case OutlineLockPasswordUnlocked = 'solar-outline-lock_password_unlocked';
    case OutlineSiren = 'solar-outline-Siren';
    case OutlineShieldMinimalistic = 'solar-outline-shield_minimalistic';
    case OutlineEyeScan = 'solar-outline-eye_scan';
    case OutlineKeyMinimalisticSquare2 = 'solar-outline-key_minimalistic_square2';
    case OutlineScanner2 = 'solar-outline-scanner2';
    case OutlineKeyMinimalisticSquare3 = 'solar-outline-key_minimalistic_square3';
    case OutlineKeyMinimalistic2 = 'solar-outline-key_minimalistic2';
    case OutlineCodeScan = 'solar-outline-code_scan';
    case OutlineShieldPlus = 'solar-outline-shield_plus';
    case OutlinePasswordMinimalistic = 'solar-outline-password_minimalistic';
    case OutlineEye = 'solar-outline-Eye';
    case OutlineQrCode = 'solar-outline-qr_code';
    case OutlineShieldCheck = 'solar-outline-shield_check';
    case OutlineKeyMinimalistic = 'solar-outline-key_minimalistic';
    case OutlineLockKeyhole = 'solar-outline-lock_keyhole';
    case OutlineShieldUser = 'solar-outline-shield_user';
    case OutlineKeySquare2 = 'solar-outline-key_square2';
    case OutlineBombEmoji = 'solar-outline-bomb_emoji';
    case OutlineScanner = 'solar-outline-Scanner';
    case OutlineShieldUp = 'solar-outline-shield_up';
    case OutlineSirenRounded = 'solar-outline-siren_rounded';
    case OutlineLockKeyholeMinimalistic = 'solar-outline-lock_keyhole_minimalistic';
    case OutlinePassword = 'solar-outline-Password';
    case OutlineShieldKeyholeMinimalistic = 'solar-outline-shield_keyhole_minimalistic';
    case OutlineShieldWarning = 'solar-outline-shield_warning';
    case OutlinePallete2 = 'solar-outline-pallete2';
    case OutlineAlignVerticalSpacing = 'solar-outline-align_vertical_spacing';
    case OutlineAlignVerticalCenter = 'solar-outline-align_vertical_center';
    case OutlineCropMinimalistic = 'solar-outline-crop_minimalistic';
    case OutlineMirrorRight = 'solar-outline-mirror_right';
    case OutlineAlignBottom = 'solar-outline-align_bottom';
    case OutlineRadialBlur = 'solar-outline-radial_blur';
    case OutlineCrop = 'solar-outline-Crop';
    case OutlineAlignHorizontaSpacing = 'solar-outline-align_horizonta_spacing';
    case OutlineRulerPen = 'solar-outline-ruler_pen';
    case OutlineThreeSquares = 'solar-outline-three_squares';
    case OutlinePaintRoller = 'solar-outline-paint_roller';
    case OutlineLayers = 'solar-outline-Layers';
    case OutlineFilters = 'solar-outline-Filters';
    case OutlineRulerCrossPen = 'solar-outline-ruler_cross_pen';
    case OutlineFlipHorizontal = 'solar-outline-flip_horizontal';
    case OutlineAlignLeft = 'solar-outline-align_left';
    case OutlineRuler = 'solar-outline-Ruler';
    case OutlinePalette = 'solar-outline-Palette';
    case OutlineAlignTop = 'solar-outline-align_top';
    case OutlineAlignHorizontalCenter = 'solar-outline-align_horizontal_center';
    case OutlineAlignRight = 'solar-outline-align_right';
    case OutlineRulerAngular = 'solar-outline-ruler_angular';
    case OutlinePipette = 'solar-outline-Pipette';
    case OutlineFlipVertical = 'solar-outline-flip_vertical';
    case OutlineMirrorLeft = 'solar-outline-mirror_left';
    case OutlineLayersMinimalistic = 'solar-outline-layers_minimalistic';
    case OutlineColourTuneing = 'solar-outline-colour_tuneing';
    case OutlinePaletteRound = 'solar-outline-palette_round';
    case OutlineEraser = 'solar-outline-Eraser';
    case OutlineTextItalicCircle = 'solar-outline-text_italic_circle';
    case OutlineLinkRound = 'solar-outline-link_round';
    case OutlineTextItalic = 'solar-outline-text_italic';
    case OutlineLinkBrokenMinimalistic = 'solar-outline-link_broken_minimalistic';
    case OutlineTextUnderlineCross = 'solar-outline-text_underline_cross';
    case OutlineLink = 'solar-outline-Link';
    case OutlineEraserCircle = 'solar-outline-eraser_circle';
    case OutlineLinkCircle = 'solar-outline-link_circle';
    case OutlineTextBoldCircle = 'solar-outline-text_bold_circle';
    case OutlineTextField = 'solar-outline-text_field';
    case OutlineTextSquare = 'solar-outline-text_square';
    case OutlineTextSquare2 = 'solar-outline-text_square2';
    case OutlineLinkRoundAngle = 'solar-outline-link_round_angle';
    case OutlineTextUnderlineCircle = 'solar-outline-text_underline_circle';
    case OutlineTextCrossCircle = 'solar-outline-text_cross_circle';
    case OutlineTextItalicSquare = 'solar-outline-text_italic_square';
    case OutlineParagraphSpacing = 'solar-outline-paragraph_spacing';
    case OutlineText = 'solar-outline-Text';
    case OutlineLinkBroken = 'solar-outline-link_broken';
    case OutlineTextCross = 'solar-outline-text_cross';
    case OutlineTextUnderline = 'solar-outline-text_underline';
    case OutlineLinkMinimalistic = 'solar-outline-link_minimalistic';
    case OutlineLinkMinimalistic2 = 'solar-outline-link_minimalistic2';
    case OutlineTextBold = 'solar-outline-text_bold';
    case OutlineTextSelection = 'solar-outline-text_selection';
    case OutlineTextFieldFocus = 'solar-outline-text_field_focus';
    case OutlineTextBoldSquare = 'solar-outline-text_bold_square';
    case OutlineEraserSquare = 'solar-outline-eraser_square';
    case OutlineLinkSquare = 'solar-outline-link_square';
    case OutlineTextCircle = 'solar-outline-text_circle';
    case OutlineBackspace = 'solar-outline-Backspace';
    case OutlineTextCrossSquare = 'solar-outline-text_cross_square';
    case OutlineInboxUnread = 'solar-outline-inbox_unread';
    case OutlineChatUnread = 'solar-outline-chat_unread';
    case OutlineChatRound = 'solar-outline-chat_round';
    case OutlineUnread = 'solar-outline-Unread';
    case OutlineMailbox = 'solar-outline-Mailbox';
    case OutlineLetter = 'solar-outline-Letter';
    case OutlinePenNewRound = 'solar-outline-pen_new_round';
    case OutlineMultipleForwardRight = 'solar-outline-multiple_forward_right';
    case OutlineMultipleForwardLeft = 'solar-outline-multiple_forward_left';
    case OutlineInboxArchive = 'solar-outline-inbox_archive';
    case OutlineInbox = 'solar-outline-Inbox';
    case OutlinePen2 = 'solar-outline-pen2';
    case OutlinePenNewSquare = 'solar-outline-pen_new_square';
    case OutlinePen = 'solar-outline-Pen';
    case OutlineChatDots = 'solar-outline-chat_dots';
    case OutlineChatSquareCall = 'solar-outline-chat_square_call';
    case OutlineSquareShareLine = 'solar-outline-square_share_line';
    case OutlineChatRoundCheck = 'solar-outline-chat_round_check';
    case OutlineInboxOut = 'solar-outline-inbox_out';
    case OutlinePlain3 = 'solar-outline-plain3';
    case OutlineChatRoundDots = 'solar-outline-chat_round_dots';
    case OutlineChatRoundLike = 'solar-outline-chat_round_like';
    case OutlinePlain2 = 'solar-outline-plain2';
    case OutlineChatRoundUnread = 'solar-outline-chat_round_unread';
    case OutlineChatSquareLike = 'solar-outline-chat_square_like';
    case OutlinePaperclip = 'solar-outline-Paperclip';
    case OutlineChatSquareCheck = 'solar-outline-chat_square_check';
    case OutlineChatSquare = 'solar-outline-chat_square';
    case OutlineLetterOpened = 'solar-outline-letter_opened';
    case OutlineSquareForward = 'solar-outline-square_forward';
    case OutlineLetterUnread = 'solar-outline-letter_unread';
    case OutlinePaperclipRounded2 = 'solar-outline-paperclip_rounded2';
    case OutlineChatRoundCall = 'solar-outline-chat_round_call';
    case OutlineInboxLine = 'solar-outline-inbox_line';
    case OutlineChatRoundVideo = 'solar-outline-chat_round_video';
    case OutlineChatRoundMoney = 'solar-outline-chat_round_money';
    case OutlineInboxIn = 'solar-outline-inbox_in';
    case OutlineCheckRead = 'solar-outline-check_read';
    case OutlineChatRoundLine = 'solar-outline-chat_round_line';
    case OutlineForward = 'solar-outline-Forward';
    case OutlinePaperclip2 = 'solar-outline-paperclip2';
    case OutlineDialog2 = 'solar-outline-dialog2';
    case OutlineDialog = 'solar-outline-Dialog';
    case OutlinePaperclipRounded = 'solar-outline-paperclip_rounded';
    case OutlinePlain = 'solar-outline-Plain';
    case OutlineChatSquareArrow = 'solar-outline-chat_square_arrow';
    case OutlineChatSquareCode = 'solar-outline-chat_square_code';
    case OutlineChatLine = 'solar-outline-chat_line';
    case OutlineTennis = 'solar-outline-Tennis';
    case OutlineBicyclingRound = 'solar-outline-bicycling_round';
    case OutlineBalls = 'solar-outline-Balls';
    case OutlineMeditationRound = 'solar-outline-meditation_round';
    case OutlineStretchingRound = 'solar-outline-stretching_round';
    case OutlineDumbbells2 = 'solar-outline-dumbbells2';
    case OutlineMeditation = 'solar-outline-Meditation';
    case OutlineRunning2 = 'solar-outline-running2';
    case OutlineRugby = 'solar-outline-Rugby';
    case OutlineBodyShapeMinimalistic = 'solar-outline-body_shape_minimalistic';
    case OutlineStretching = 'solar-outline-Stretching';
    case OutlineBowling = 'solar-outline-Bowling';
    case OutlineRanking = 'solar-outline-Ranking';
    case OutlineTreadmillRound = 'solar-outline-treadmill_round';
    case OutlineVolleyball = 'solar-outline-Volleyball';
    case OutlineDumbbellLargeMinimalistic = 'solar-outline-dumbbell_large_minimalistic';
    case OutlineRunningRound = 'solar-outline-running_round';
    case OutlineHiking = 'solar-outline-Hiking';
    case OutlineHikingMinimalistic = 'solar-outline-hiking_minimalistic';
    case OutlineWaterSun = 'solar-outline-water_sun';
    case OutlineGolf = 'solar-outline-Golf';
    case OutlineSkateboarding = 'solar-outline-Skateboarding';
    case OutlineDumbbells = 'solar-outline-Dumbbells';
    case OutlineWalkingRound = 'solar-outline-walking_round';
    case OutlineRunning = 'solar-outline-Running';
    case OutlineTreadmill = 'solar-outline-Treadmill';
    case OutlineSkateboard = 'solar-outline-Skateboard';
    case OutlineDumbbellSmall = 'solar-outline-dumbbell_small';
    case OutlineBasketball = 'solar-outline-Basketball';
    case OutlineFootball = 'solar-outline-Football';
    case OutlineDumbbell = 'solar-outline-Dumbbell';
    case OutlineBodyShape = 'solar-outline-body_shape';
    case OutlineWater = 'solar-outline-Water';
    case OutlineSkateboardingRound = 'solar-outline-skateboarding_round';
    case OutlineHikingRound = 'solar-outline-hiking_round';
    case OutlineVolleyball2 = 'solar-outline-volleyball2';
    case OutlineTennis2 = 'solar-outline-tennis2';
    case OutlineSwimming = 'solar-outline-Swimming';
    case OutlineBicycling = 'solar-outline-Bicycling';
    case OutlineWalking = 'solar-outline-Walking';
    case OutlineDumbbellLarge = 'solar-outline-dumbbell_large';
    case OutlineCalendarMark = 'solar-outline-calendar_mark';
    case OutlineHistory2 = 'solar-outline-history2';
    case OutlineWatchSquareMinimalisticCharge = 'solar-outline-watch_square_minimalistic_charge';
    case OutlineHistory3 = 'solar-outline-history3';
    case OutlineHourglass = 'solar-outline-Hourglass';
    case OutlineCalendarSearch = 'solar-outline-calendar_search';
    case OutlineStopwatchPlay = 'solar-outline-stopwatch_play';
    case OutlineWatchRound = 'solar-outline-watch_round';
    case OutlineCalendarAdd = 'solar-outline-calendar_add';
    case OutlineCalendarDate = 'solar-outline-calendar_date';
    case OutlineStopwatch = 'solar-outline-Stopwatch';
    case OutlineAlarmPause = 'solar-outline-alarm_pause';
    case OutlineAlarmTurnOff = 'solar-outline-alarm_turn_off';
    case OutlineClockSquare = 'solar-outline-clock_square';
    case OutlineStopwatchPause = 'solar-outline-stopwatch_pause';
    case OutlineCalendarMinimalistic = 'solar-outline-calendar_minimalistic';
    case OutlineAlarmAdd = 'solar-outline-alarm_add';
    case OutlineAlarmPlay = 'solar-outline-alarm_play';
    case OutlineHourglassLine = 'solar-outline-hourglass_line';
    case OutlineAlarmSleep = 'solar-outline-alarm_sleep';
    case OutlineAlarmRemove = 'solar-outline-alarm_remove';
    case OutlineCalendar = 'solar-outline-Calendar';
    case OutlineClockCircle = 'solar-outline-clock_circle';
    case OutlineHistory = 'solar-outline-History';
    case OutlineAlarm = 'solar-outline-Alarm';
    case OutlineWatchSquare = 'solar-outline-watch_square';
    case OutlineWatchSquareMinimalistic = 'solar-outline-watch_square_minimalistic';
    case OutlineMagniferBug = 'solar-outline-magnifer_bug';
    case OutlineMagnifer = 'solar-outline-Magnifer';
    case OutlineMagniferZoomIn = 'solar-outline-magnifer_zoom_in';
    case OutlineRoundedMagnifer = 'solar-outline-rounded_magnifer';
    case OutlineRoundedMagniferZoomIn = 'solar-outline-rounded_magnifer_zoom_in';
    case OutlineMinimalisticMagniferBug = 'solar-outline-minimalistic_magnifer_bug';
    case OutlineRoundedMagniferBug = 'solar-outline-rounded_magnifer_bug';
    case OutlineMinimalisticMagniferZoomOut = 'solar-outline-minimalistic_magnifer_zoom_out';
    case OutlineMinimalisticMagnifer = 'solar-outline-minimalistic_magnifer';
    case OutlineRoundedMagniferZoomOut = 'solar-outline-rounded_magnifer_zoom_out';
    case OutlineMinimalisticMagniferZoomIn = 'solar-outline-minimalistic_magnifer_zoom_in';
    case OutlineMagniferZoomOut = 'solar-outline-magnifer_zoom_out';
    case OutlineBagCheck = 'solar-outline-bag_check';
    case OutlineShopMinimalistic = 'solar-outline-shop_minimalistic';
    case OutlineShop = 'solar-outline-Shop';
    case OutlineCartCheck = 'solar-outline-cart_check';
    case OutlineCart = 'solar-outline-Cart';
    case OutlineCart3 = 'solar-outline-cart3';
    case OutlineCart2 = 'solar-outline-cart2';
    case OutlineBagMusic = 'solar-outline-bag_music';
    case OutlineCartLargeMinimalistic = 'solar-outline-cart_large_minimalistic';
    case OutlineCart5 = 'solar-outline-cart5';
    case OutlineCart4 = 'solar-outline-cart4';
    case OutlineBag = 'solar-outline-Bag';
    case OutlineBagHeart = 'solar-outline-bag_heart';
    case OutlineCartPlus = 'solar-outline-cart_plus';
    case OutlineCartLarge = 'solar-outline-cart_large';
    case OutlineBagCross = 'solar-outline-bag_cross';
    case OutlineBagMusic2 = 'solar-outline-bag_music2';
    case OutlineBag5 = 'solar-outline-bag5';
    case OutlineBag4 = 'solar-outline-bag4';
    case OutlineCartLarge4 = 'solar-outline-cart_large4';
    case OutlineCartLarge3 = 'solar-outline-cart_large3';
    case OutlineBag3 = 'solar-outline-bag3';
    case OutlineBag2 = 'solar-outline-bag2';
    case OutlineShop2 = 'solar-outline-shop2';
    case OutlineCartLarge2 = 'solar-outline-cart_large2';
    case OutlineBagSmile = 'solar-outline-bag_smile';
    case OutlineCartCross = 'solar-outline-cart_cross';
    case OutlineInfoSquare = 'solar-outline-info_square';
    case OutlineFlashlightOn = 'solar-outline-flashlight_on';
    case OutlineXXX = 'solar-outline-XXX';
    case OutlineFigma = 'solar-outline-Figma';
    case OutlineFlashlight = 'solar-outline-Flashlight';
    case OutlineGhost = 'solar-outline-Ghost';
    case OutlineCupMusic = 'solar-outline-cup_music';
    case OutlineBatteryFullMinimalistic = 'solar-outline-battery_full_minimalistic';
    case OutlineDangerCircle = 'solar-outline-danger_circle';
    case OutlineCheckSquare = 'solar-outline-check_square';
    case OutlineGhostSmile = 'solar-outline-ghost_smile';
    case OutlineTarget = 'solar-outline-Target';
    case OutlineBatteryHalfMinimalistic = 'solar-outline-battery_half_minimalistic';
    case OutlineScissors = 'solar-outline-Scissors';
    case OutlinePinList = 'solar-outline-pin_list';
    case OutlineBatteryCharge = 'solar-outline-battery_charge';
    case OutlineUmbrella = 'solar-outline-Umbrella';
    case OutlineHomeSmile = 'solar-outline-home_smile';
    case OutlineHome = 'solar-outline-Home';
    case OutlineCopyright = 'solar-outline-Copyright';
    case OutlineHomeWifi = 'solar-outline-home_wifi';
    case OutlineTShirt = 'solar-outline-t_shirt';
    case OutlineBatteryChargeMinimalistic = 'solar-outline-battery_charge_minimalistic';
    case OutlineCupStar = 'solar-outline-cup_star';
    case OutlineSpecialEffects = 'solar-outline-special_effects';
    case OutlineBody = 'solar-outline-Body';
    case OutlineHamburgerMenu = 'solar-outline-hamburger_menu';
    case OutlinePower = 'solar-outline-Power';
    case OutlineDatabase = 'solar-outline-Database';
    case OutlineCursorSquare = 'solar-outline-cursor_square';
    case OutlineFuel = 'solar-outline-Fuel';
    case OutlineMentionCircle = 'solar-outline-mention_circle';
    case OutlineConfettiMinimalistic = 'solar-outline-confetti_minimalistic';
    case OutlineMenuDotsCircle = 'solar-outline-menu_dots_circle';
    case OutlinePaw = 'solar-outline-Paw';
    case OutlineSubtitles = 'solar-outline-Subtitles';
    case OutlineSliderVerticalMinimalistic = 'solar-outline-slider_vertical_minimalistic';
    case OutlineCrownMinimalistic = 'solar-outline-crown_minimalistic';
    case OutlineMenuDots = 'solar-outline-menu_dots';
    case OutlineDelivery = 'solar-outline-Delivery';
    case OutlineWaterdrop = 'solar-outline-Waterdrop';
    case OutlinePerfume = 'solar-outline-Perfume';
    case OutlineHomeAngle2 = 'solar-outline-home_angle2';
    case OutlineHomeWifiAngle = 'solar-outline-home_wifi_angle';
    case OutlineQuestionCircle = 'solar-outline-question_circle';
    case OutlineTrashBinMinimalistic = 'solar-outline-trash_bin_minimalistic';
    case OutlineMagicStick3 = 'solar-outline-magic_stick3';
    case OutlineAddSquare = 'solar-outline-add_square';
    case OutlineCrownStar = 'solar-outline-crown_star';
    case OutlineMagnet = 'solar-outline-Magnet';
    case OutlineConfetti = 'solar-outline-Confetti';
    case OutlinePin = 'solar-outline-Pin';
    case OutlineMinusSquare = 'solar-outline-minus_square';
    case OutlineBolt = 'solar-outline-Bolt';
    case OutlineCloseCircle = 'solar-outline-close_circle';
    case OutlineForbiddenCircle = 'solar-outline-forbidden_circle';
    case OutlineMagicStick2 = 'solar-outline-magic_stick2';
    case OutlineCrownLine = 'solar-outline-crown_line';
    case OutlineBoltCircle = 'solar-outline-bolt_circle';
    case OutlineFlag = 'solar-outline-Flag';
    case OutlineSliderHorizontal = 'solar-outline-slider_horizontal';
    case OutlineHighDefinition = 'solar-outline-high_definition';
    case OutlineCursor = 'solar-outline-Cursor';
    case OutlineFeed = 'solar-outline-Feed';
    case OutlineTrafficEconomy = 'solar-outline-traffic_economy';
    case OutlineAugmentedReality = 'solar-outline-augmented_reality';
    case OutlineIcon4K = 'solar-outline-4_k';
    case OutlineMagnetWave = 'solar-outline-magnet_wave';
    case OutlineHomeSmileAngle = 'solar-outline-home_smile_angle';
    case OutlineSliderVertical = 'solar-outline-slider_vertical';
    case OutlineCheckCircle = 'solar-outline-check_circle';
    case OutlineCopy = 'solar-outline-Copy';
    case OutlineDangerSquare = 'solar-outline-danger_square';
    case OutlineSkirt = 'solar-outline-Skirt';
    case OutlineGlasses = 'solar-outline-Glasses';
    case OutlineHomeAdd = 'solar-outline-home_add';
    case OutlineSledgehammer = 'solar-outline-Sledgehammer';
    case OutlineInfoCircle = 'solar-outline-info_circle';
    case OutlineDangerTriangle = 'solar-outline-danger_triangle';
    case OutlinePinCircle = 'solar-outline-pin_circle';
    case OutlineSmartHome = 'solar-outline-smart_home';
    case OutlineScissorsSquare = 'solar-outline-scissors_square';
    case OutlineSleeping = 'solar-outline-Sleeping';
    case OutlineBox = 'solar-outline-Box';
    case OutlineCrown = 'solar-outline-Crown';
    case OutlineBroom = 'solar-outline-Broom';
    case OutlinePostsCarouselHorizontal = 'solar-outline-posts_carousel_horizontal';
    case OutlineFlag2 = 'solar-outline-flag2';
    case OutlinePlate = 'solar-outline-Plate';
    case OutlineTrashBinTrash = 'solar-outline-trash_bin_trash';
    case OutlineCupFirst = 'solar-outline-cup_first';
    case OutlineSmartHomeAngle = 'solar-outline-smart_home_angle';
    case OutlinePaperBin = 'solar-outline-paper_bin';
    case OutlineBoxMinimalistic = 'solar-outline-box_minimalistic';
    case OutlineDanger = 'solar-outline-Danger';
    case OutlineMenuDotsSquare = 'solar-outline-menu_dots_square';
    case OutlineHanger2 = 'solar-outline-hanger2';
    case OutlineBatteryHalf = 'solar-outline-battery_half';
    case OutlineHome2 = 'solar-outline-home2';
    case OutlinePostsCarouselVertical = 'solar-outline-posts_carousel_vertical';
    case OutlineRevote = 'solar-outline-Revote';
    case OutlineMentionSquare = 'solar-outline-mention_square';
    case OutlineWinRar = 'solar-outline-win_rar';
    case OutlineForbidden = 'solar-outline-Forbidden';
    case OutlineQuestionSquare = 'solar-outline-question_square';
    case OutlineHanger = 'solar-outline-Hanger';
    case OutlineReorder = 'solar-outline-Reorder';
    case OutlineHomeAddAngle = 'solar-outline-home_add_angle';
    case OutlineMasks = 'solar-outline-Masks';
    case OutlineGift = 'solar-outline-Gift';
    case OutlineCreativeCommons = 'solar-outline-creative_commons';
    case OutlineSliderMinimalisticHorizontal = 'solar-outline-slider_minimalistic_horizontal';
    case OutlineHomeAngle = 'solar-outline-home_angle';
    case OutlineBatteryLowMinimalistic = 'solar-outline-battery_low_minimalistic';
    case OutlineShare = 'solar-outline-Share';
    case OutlineTrashBin2 = 'solar-outline-trash_bin2';
    case OutlineSort = 'solar-outline-Sort';
    case OutlineMinusCircle = 'solar-outline-minus_circle';
    case OutlineExplicit = 'solar-outline-Explicit';
    case OutlineTraffic = 'solar-outline-Traffic';
    case OutlineFilter = 'solar-outline-Filter';
    case OutlineCloseSquare = 'solar-outline-close_square';
    case OutlineAddCircle = 'solar-outline-add_circle';
    case OutlineFerrisWheel = 'solar-outline-ferris_wheel';
    case OutlineCup = 'solar-outline-Cup';
    case OutlineBalloon = 'solar-outline-Balloon';
    case OutlineHelp = 'solar-outline-Help';
    case OutlineBatteryFull = 'solar-outline-battery_full';
    case OutlineCat = 'solar-outline-Cat';
    case OutlineMaskSad = 'solar-outline-mask_sad';
    case OutlineHighQuality = 'solar-outline-high_quality';
    case OutlineMagicStick = 'solar-outline-magic_stick';
    case OutlineCosmetic = 'solar-outline-Cosmetic';
    case OutlineBatteryLow = 'solar-outline-battery_low';
    case OutlineShareCircle = 'solar-outline-share_circle';
    case OutlineMaskHapply = 'solar-outline-mask_happly';
    case OutlineAccessibility = 'solar-outline-Accessibility';
    case OutlineTrashBinMinimalistic2 = 'solar-outline-trash_bin_minimalistic2';
    case OutlineIncomingCallRounded = 'solar-outline-incoming_call_rounded';
    case OutlineCallDropped = 'solar-outline-call_dropped';
    case OutlineCallChat = 'solar-outline-call_chat';
    case OutlineCallCancelRounded = 'solar-outline-call_cancel_rounded';
    case OutlineCallMedicineRounded = 'solar-outline-call_medicine_rounded';
    case OutlineCallDroppedRounded = 'solar-outline-call_dropped_rounded';
    case OutlineRecordSquare = 'solar-outline-record_square';
    case OutlinePhoneCalling = 'solar-outline-phone_calling';
    case OutlinePhoneRounded = 'solar-outline-phone_rounded';
    case OutlineCallMedicine = 'solar-outline-call_medicine';
    case OutlineRecordMinimalistic = 'solar-outline-record_minimalistic';
    case OutlineEndCall = 'solar-outline-end_call';
    case OutlineOutgoingCall = 'solar-outline-outgoing_call';
    case OutlineRecordCircle = 'solar-outline-record_circle';
    case OutlineIncomingCall = 'solar-outline-incoming_call';
    case OutlineCallChatRounded = 'solar-outline-call_chat_rounded';
    case OutlineEndCallRounded = 'solar-outline-end_call_rounded';
    case OutlinePhone = 'solar-outline-Phone';
    case OutlineOutgoingCallRounded = 'solar-outline-outgoing_call_rounded';
    case OutlineCallCancel = 'solar-outline-call_cancel';
    case OutlinePhoneCallingRounded = 'solar-outline-phone_calling_rounded';
    case OutlineStationMinimalistic = 'solar-outline-station_minimalistic';
    case OutlineSidebarCode = 'solar-outline-sidebar_code';
    case OutlineWiFiRouterMinimalistic = 'solar-outline-wi_fi_router_minimalistic';
    case OutlineUSB = 'solar-outline-USB';
    case OutlineSiderbar = 'solar-outline-Siderbar';
    case OutlineCode2 = 'solar-outline-code2';
    case OutlineSlashCircle = 'solar-outline-slash_circle';
    case OutlineScreencast = 'solar-outline-Screencast';
    case OutlineHashtagSquare = 'solar-outline-hashtag_square';
    case OutlineSidebarMinimalistic = 'solar-outline-sidebar_minimalistic';
    case OutlineCode = 'solar-outline-Code';
    case OutlineUsbSquare = 'solar-outline-usb_square';
    case OutlineWiFiRouter = 'solar-outline-wi_fi_router';
    case OutlineCodeCircle = 'solar-outline-code_circle';
    case OutlineTranslation = 'solar-outline-Translation';
    case OutlineBugMinimalistic = 'solar-outline-bug_minimalistic';
    case OutlineStation = 'solar-outline-Station';
    case OutlineProgramming = 'solar-outline-Programming';
    case OutlineWiFiRouterRound = 'solar-outline-wi_fi_router_round';
    case OutlineHashtag = 'solar-outline-Hashtag';
    case OutlineBug = 'solar-outline-Bug';
    case OutlineHashtagChat = 'solar-outline-hashtag_chat';
    case OutlineCommand = 'solar-outline-Command';
    case OutlineTranslation2 = 'solar-outline-translation2';
    case OutlineHashtagCircle = 'solar-outline-hashtag_circle';
    case OutlineScreencast2 = 'solar-outline-screencast2';
    case OutlineSlashSquare = 'solar-outline-slash_square';
    case OutlineWindowFrame = 'solar-outline-window_frame';
    case OutlineStructure = 'solar-outline-Structure';
    case OutlineUsbCircle = 'solar-outline-usb_circle';
    case OutlineCodeSquare = 'solar-outline-code_square';
    case OutlineNotes = 'solar-outline-Notes';
    case OutlineDocumentText = 'solar-outline-document_text';
    case OutlineDocumentAdd = 'solar-outline-document_add';
    case OutlineDocumentMedicine = 'solar-outline-document_medicine';
    case OutlineArchiveMinimalistic = 'solar-outline-archive_minimalistic';
    case OutlineClipboard = 'solar-outline-Clipboard';
    case OutlineClipboardAdd = 'solar-outline-clipboard_add';
    case OutlineArchive = 'solar-outline-Archive';
    case OutlineClipboardHeart = 'solar-outline-clipboard_heart';
    case OutlineClipboardRemove = 'solar-outline-clipboard_remove';
    case OutlineClipboardText = 'solar-outline-clipboard_text';
    case OutlineDocument = 'solar-outline-Document';
    case OutlineNotesMinimalistic = 'solar-outline-notes_minimalistic';
    case OutlineArchiveUp = 'solar-outline-archive_up';
    case OutlineArchiveUpMinimlistic = 'solar-outline-archive_up_minimlistic';
    case OutlineArchiveCheck = 'solar-outline-archive_check';
    case OutlineArchiveDown = 'solar-outline-archive_down';
    case OutlineArchiveDownMinimlistic = 'solar-outline-archive_down_minimlistic';
    case OutlineDocumentsMinimalistic = 'solar-outline-documents_minimalistic';
    case OutlineClipboardCheck = 'solar-outline-clipboard_check';
    case OutlineClipboardList = 'solar-outline-clipboard_list';
    case OutlineDocuments = 'solar-outline-Documents';
    case OutlineNotebook = 'solar-outline-Notebook';
    case OutlineGalleryRound = 'solar-outline-gallery_round';
    case OutlinePlayCircle = 'solar-outline-play_circle';
    case OutlineStream = 'solar-outline-Stream';
    case OutlineGalleryRemove = 'solar-outline-gallery_remove';
    case OutlineClapperboard = 'solar-outline-Clapperboard';
    case OutlinePauseCircle = 'solar-outline-pause_circle';
    case OutlineRewind5SecondsBack = 'solar-outline-rewind5_seconds_back';
    case OutlineRepeat = 'solar-outline-Repeat';
    case OutlineClapperboardEdit = 'solar-outline-clapperboard_edit';
    case OutlineVideoFrameCut = 'solar-outline-video_frame_cut';
    case OutlinePanorama = 'solar-outline-Panorama';
    case OutlinePlayStream = 'solar-outline-play_stream';
    case OutlineClapperboardOpen = 'solar-outline-clapperboard_open';
    case OutlineClapperboardText = 'solar-outline-clapperboard_text';
    case OutlineLibrary = 'solar-outline-Library';
    case OutlineReel2 = 'solar-outline-reel2';
    case OutlineVolumeSmall = 'solar-outline-volume_small';
    case OutlineVideoFrame = 'solar-outline-video_frame';
    case OutlineMicrophoneLarge = 'solar-outline-microphone_large';
    case OutlineRewindForward = 'solar-outline-rewind_forward';
    case OutlineRewindBackCircle = 'solar-outline-rewind_back_circle';
    case OutlineMicrophone = 'solar-outline-Microphone';
    case OutlineVideoFrameReplace = 'solar-outline-video_frame_replace';
    case OutlineClapperboardPlay = 'solar-outline-clapperboard_play';
    case OutlineGalleryDownload = 'solar-outline-gallery_download';
    case OutlineMusicNote4 = 'solar-outline-music_note4';
    case OutlineVideocameraRecord = 'solar-outline-videocamera_record';
    case OutlinePlaybackSpeed = 'solar-outline-playback_speed';
    case OutlineSoundwave = 'solar-outline-Soundwave';
    case OutlineStopCircle = 'solar-outline-stop_circle';
    case OutlineQuitFullScreenCircle = 'solar-outline-quit_full_screen_circle';
    case OutlineRewindBack = 'solar-outline-rewind_back';
    case OutlineRepeatOne = 'solar-outline-repeat_one';
    case OutlineGalleryCheck = 'solar-outline-gallery_check';
    case OutlineWallpaper = 'solar-outline-Wallpaper';
    case OutlineRewindForwardCircle = 'solar-outline-rewind_forward_circle';
    case OutlineGalleryEdit = 'solar-outline-gallery_edit';
    case OutlineGallery = 'solar-outline-Gallery';
    case OutlineGalleryMinimalistic = 'solar-outline-gallery_minimalistic';
    case OutlineUploadTrack = 'solar-outline-upload_track';
    case OutlineVolume = 'solar-outline-Volume';
    case OutlineUploadTrack2 = 'solar-outline-upload_track2';
    case OutlineMusicNotes = 'solar-outline-music_notes';
    case OutlineMusicNote2 = 'solar-outline-music_note2';
    case OutlineCameraAdd = 'solar-outline-camera_add';
    case OutlinePodcast = 'solar-outline-Podcast';
    case OutlineCameraRotate = 'solar-outline-camera_rotate';
    case OutlineMusicNote3 = 'solar-outline-music_note3';
    case OutlineStop = 'solar-outline-Stop';
    case OutlineMuted = 'solar-outline-Muted';
    case OutlineSkipNext = 'solar-outline-skip_next';
    case OutlineGallerySend = 'solar-outline-gallery_send';
    case OutlineRecord = 'solar-outline-Record';
    case OutlineFullScreenCircle = 'solar-outline-full_screen_circle';
    case OutlineVolumeCross = 'solar-outline-volume_cross';
    case OutlineSoundwaveCircle = 'solar-outline-soundwave_circle';
    case OutlineSkipPrevious = 'solar-outline-skip_previous';
    case OutlineRewind5SecondsForward = 'solar-outline-rewind5_seconds_forward';
    case OutlinePlay = 'solar-outline-Play';
    case OutlinePIP = 'solar-outline-PIP';
    case OutlineMusicLibrary = 'solar-outline-music_library';
    case OutlineVideoFrame2 = 'solar-outline-video_frame2';
    case OutlineCamera = 'solar-outline-Camera';
    case OutlineQuitPip = 'solar-outline-quit_pip';
    case OutlineClapperboardOpenPlay = 'solar-outline-clapperboard_open_play';
    case OutlineRewind10SecondsBack = 'solar-outline-rewind10_seconds_back';
    case OutlineRepeatOneMinimalistic = 'solar-outline-repeat_one_minimalistic';
    case OutlineVinyl = 'solar-outline-Vinyl';
    case OutlineVideoLibrary = 'solar-outline-video_library';
    case OutlineGalleryWide = 'solar-outline-gallery_wide';
    case OutlineReel = 'solar-outline-Reel';
    case OutlineToPip = 'solar-outline-to_pip';
    case OutlinePip2 = 'solar-outline-pip2';
    case OutlineFullScreen = 'solar-outline-full_screen';
    case OutlineCameraMinimalistic = 'solar-outline-camera_minimalistic';
    case OutlineVideoFrameCut2 = 'solar-outline-video_frame_cut2';
    case OutlineGalleryCircle = 'solar-outline-gallery_circle';
    case OutlineVideoFramePlayHorizontal = 'solar-outline-video_frame_play_horizontal';
    case OutlineMusicNoteSlider2 = 'solar-outline-music_note_slider2';
    case OutlineMusicNoteSlider = 'solar-outline-music_note_slider';
    case OutlineVideocameraAdd = 'solar-outline-videocamera_add';
    case OutlineQuitFullScreenSquare = 'solar-outline-quit_full_screen_square';
    case OutlineAlbum = 'solar-outline-Album';
    case OutlineGalleryAdd = 'solar-outline-gallery_add';
    case OutlineCameraSquare = 'solar-outline-camera_square';
    case OutlineRewind15SecondsBack = 'solar-outline-rewind15_seconds_back';
    case OutlineRewind15SecondsForward = 'solar-outline-rewind15_seconds_forward';
    case OutlineVinylRecord = 'solar-outline-vinyl_record';
    case OutlineShuffle = 'solar-outline-Shuffle';
    case OutlinePause = 'solar-outline-Pause';
    case OutlineMusicNote = 'solar-outline-music_note';
    case OutlineQuitFullScreen = 'solar-outline-quit_full_screen';
    case OutlineMicrophone2 = 'solar-outline-microphone2';
    case OutlineVideocamera = 'solar-outline-Videocamera';
    case OutlineGalleryFavourite = 'solar-outline-gallery_favourite';
    case OutlineMusicLibrary2 = 'solar-outline-music_library2';
    case OutlineVideoFramePlayVertical = 'solar-outline-video_frame_play_vertical';
    case OutlineFullScreenSquare = 'solar-outline-full_screen_square';
    case OutlineRewind10SecondsForward = 'solar-outline-rewind10_seconds_forward';
    case OutlineVolumeLoud = 'solar-outline-volume_loud';
    case OutlineMicrophone3 = 'solar-outline-microphone3';
    case OutlineSoundwaveSquare = 'solar-outline-soundwave_square';
    case OutlineCardholder = 'solar-outline-Cardholder';
    case OutlineBillList = 'solar-outline-bill_list';
    case OutlineSaleSquare = 'solar-outline-sale_square';
    case OutlineDollar = 'solar-outline-Dollar';
    case OutlineTicket = 'solar-outline-Ticket';
    case OutlineTag = 'solar-outline-Tag';
    case OutlineCashOut = 'solar-outline-cash_out';
    case OutlineWallet2 = 'solar-outline-wallet2';
    case OutlineRuble = 'solar-outline-Ruble';
    case OutlineCardTransfer = 'solar-outline-card_transfer';
    case OutlineEuro = 'solar-outline-Euro';
    case OutlineSale = 'solar-outline-Sale';
    case OutlineCardSearch = 'solar-outline-card_search';
    case OutlineWallet = 'solar-outline-Wallet';
    case OutlineBillCross = 'solar-outline-bill_cross';
    case OutlineTicketSale = 'solar-outline-ticket_sale';
    case OutlineSafeSquare = 'solar-outline-safe_square';
    case OutlineCard = 'solar-outline-Card';
    case OutlineSafe2 = 'solar-outline-safe2';
    case OutlineDollarMinimalistic = 'solar-outline-dollar_minimalistic';
    case OutlineTagPrice = 'solar-outline-tag_price';
    case OutlineMoneyBag = 'solar-outline-money_bag';
    case OutlineBill = 'solar-outline-Bill';
    case OutlineCardSend = 'solar-outline-card_send';
    case OutlineCardRecive = 'solar-outline-card_recive';
    case OutlineBanknote2 = 'solar-outline-banknote2';
    case OutlineTagHorizontal = 'solar-outline-tag_horizontal';
    case OutlineBillCheck = 'solar-outline-bill_check';
    case OutlineTickerStar = 'solar-outline-ticker_star';
    case OutlineBanknote = 'solar-outline-Banknote';
    case OutlineVerifiedCheck = 'solar-outline-verified_check';
    case OutlineWadOfMoney = 'solar-outline-wad_of_money';
    case OutlineCard2 = 'solar-outline-card2';
    case OutlineSafeCircle = 'solar-outline-safe_circle';
    case OutlineWalletMoney = 'solar-outline-wallet_money';
    case OutlineList = 'solar-outline-List';
    case OutlineListDownMinimalistic = 'solar-outline-list_down_minimalistic';
    case OutlinePlaylist2 = 'solar-outline-playlist2';
    case OutlineChecklistMinimalistic = 'solar-outline-checklist_minimalistic';
    case OutlinePlaaylistMinimalistic = 'solar-outline-plaaylist_minimalistic';
    case OutlineListHeart = 'solar-outline-list_heart';
    case OutlineListArrowDown = 'solar-outline-list_arrow_down';
    case OutlineListArrowUp = 'solar-outline-list_arrow_up';
    case OutlineListUpMinimalistic = 'solar-outline-list_up_minimalistic';
    case OutlinePlaylist = 'solar-outline-Playlist';
    case OutlineListUp = 'solar-outline-list_up';
    case OutlineListCrossMinimalistic = 'solar-outline-list_cross_minimalistic';
    case OutlineListCross = 'solar-outline-list_cross';
    case OutlineListArrowDownMinimalistic = 'solar-outline-list_arrow_down_minimalistic';
    case OutlineSortByAlphabet = 'solar-outline-sort_by_alphabet';
    case OutlineChecklist = 'solar-outline-Checklist';
    case OutlineSortFromBottomToTop = 'solar-outline-sort_from_bottom_to_top';
    case OutlineListCheck = 'solar-outline-list_check';
    case OutlinePlaylistMinimalistic2 = 'solar-outline-playlist_minimalistic2';
    case OutlinePlaylistMinimalistic3 = 'solar-outline-playlist_minimalistic3';
    case OutlineList1 = 'solar-outline-list1';
    case OutlineSortFromTopToBottom = 'solar-outline-sort_from_top_to_bottom';
    case OutlineSortByTime = 'solar-outline-sort_by_time';
    case OutlineListDown = 'solar-outline-list_down';
    case OutlineListHeartMinimalistic = 'solar-outline-list_heart_minimalistic';
    case OutlineListCheckMinimalistic = 'solar-outline-list_check_minimalistic';
    case OutlineListArrowUpMinimalistic = 'solar-outline-list_arrow_up_minimalistic';
    case OutlineUserCrossRounded = 'solar-outline-user_cross_rounded';
    case OutlineUser = 'solar-outline-User';
    case OutlineUsersGroupRounded = 'solar-outline-users_group_rounded';
    case OutlineUserPlusRounded = 'solar-outline-user_plus_rounded';
    case OutlineUserBlock = 'solar-outline-user_block';
    case OutlineUserMinus = 'solar-outline-user_minus';
    case OutlineUserHands = 'solar-outline-user_hands';
    case OutlineUserHeart = 'solar-outline-user_heart';
    case OutlineUserMinusRounded = 'solar-outline-user_minus_rounded';
    case OutlineUserCross = 'solar-outline-user_cross';
    case OutlineUserSpeakRounded = 'solar-outline-user_speak_rounded';
    case OutlineUserId = 'solar-outline-user_id';
    case OutlineUserBlockRounded = 'solar-outline-user_block_rounded';
    case OutlineUserHeartRounded = 'solar-outline-user_heart_rounded';
    case OutlineUsersGroupTwoRounded = 'solar-outline-users_group_two_rounded';
    case OutlineUserHandUp = 'solar-outline-user_hand_up';
    case OutlineUserCircle = 'solar-outline-user_circle';
    case OutlineUserRounded = 'solar-outline-user_rounded';
    case OutlineUserCheck = 'solar-outline-user_check';
    case OutlineUserPlus = 'solar-outline-user_plus';
    case OutlineUserCheckRounded = 'solar-outline-user_check_rounded';
    case OutlineUserSpeak = 'solar-outline-user_speak';
    case OutlineVirus = 'solar-outline-Virus';
    case OutlineAdhesivePlaster2 = 'solar-outline-adhesive_plaster2';
    case OutlineDropper = 'solar-outline-Dropper';
    case OutlinePulse2 = 'solar-outline-pulse2';
    case OutlineBoneBroken = 'solar-outline-bone_broken';
    case OutlineHeartPulse2 = 'solar-outline-heart_pulse2';
    case OutlineMedicalKit = 'solar-outline-medical_kit';
    case OutlineTestTube = 'solar-outline-test_tube';
    case OutlineHealth = 'solar-outline-Health';
    case OutlineDropperMinimalistic2 = 'solar-outline-dropper_minimalistic2';
    case OutlineDNA = 'solar-outline-DNA';
    case OutlineDropper3 = 'solar-outline-dropper3';
    case OutlineThermometer = 'solar-outline-Thermometer';
    case OutlineDropper2 = 'solar-outline-dropper2';
    case OutlineJarOfPills2 = 'solar-outline-jar_of_pills2';
    case OutlineBoneCrack = 'solar-outline-bone_crack';
    case OutlineJarOfPills = 'solar-outline-jar_of_pills';
    case OutlineSyringe = 'solar-outline-Syringe';
    case OutlineStethoscope = 'solar-outline-Stethoscope';
    case OutlineBenzeneRing = 'solar-outline-benzene_ring';
    case OutlineBacteria = 'solar-outline-Bacteria';
    case OutlineAdhesivePlaster = 'solar-outline-adhesive_plaster';
    case OutlineBone = 'solar-outline-Bone';
    case OutlineBones = 'solar-outline-Bones';
    case OutlinePill = 'solar-outline-Pill';
    case OutlinePills = 'solar-outline-Pills';
    case OutlineHeartPulse = 'solar-outline-heart_pulse';
    case OutlineTestTubeMinimalistic = 'solar-outline-test_tube_minimalistic';
    case OutlinePills2 = 'solar-outline-pills2';
    case OutlinePulse = 'solar-outline-Pulse';
    case OutlineDropperMinimalistic = 'solar-outline-dropper_minimalistic';
    case OutlinePills3 = 'solar-outline-pills3';
    case OutlineWhisk = 'solar-outline-Whisk';
    case OutlineBottle = 'solar-outline-Bottle';
    case OutlineOvenMittsMinimalistic = 'solar-outline-oven_mitts_minimalistic';
    case OutlineChefHatMinimalistic = 'solar-outline-chef_hat_minimalistic';
    case OutlineTeaCup = 'solar-outline-tea_cup';
    case OutlineWineglassTriangle = 'solar-outline-wineglass_triangle';
    case OutlineOvenMitts = 'solar-outline-oven_mitts';
    case OutlineCupPaper = 'solar-outline-cup_paper';
    case OutlineLadle = 'solar-outline-Ladle';
    case OutlineCorkscrew = 'solar-outline-Corkscrew';
    case OutlineDonutBitten = 'solar-outline-donut_bitten';
    case OutlineWineglass = 'solar-outline-Wineglass';
    case OutlineDonut = 'solar-outline-Donut';
    case OutlineCupHot = 'solar-outline-cup_hot';
    case OutlineChefHatHeart = 'solar-outline-chef_hat_heart';
    case OutlineChefHat = 'solar-outline-chef_hat';
    case OutlineRollingPin = 'solar-outline-rolling_pin';
    case OutlineCodeFile = 'solar-outline-code_file';
    case OutlineFileCorrupted = 'solar-outline-file_corrupted';
    case OutlineFile = 'solar-outline-File';
    case OutlineFileRight = 'solar-outline-file_right';
    case OutlineFileFavourite = 'solar-outline-file_favourite';
    case OutlineFileDownload = 'solar-outline-file_download';
    case OutlineZipFile = 'solar-outline-zip_file';
    case OutlineFileText = 'solar-outline-file_text';
    case OutlineFileSmile = 'solar-outline-file_smile_)';
    case OutlineFileCheck = 'solar-outline-file_check';
    case OutlineFileSend = 'solar-outline-file_send';
    case OutlineFileLeft = 'solar-outline-file_left';
    case OutlineFigmaFile = 'solar-outline-figma_file';
    case OutlineFileRemove = 'solar-outline-file_remove';
    case OutlineCloudFile = 'solar-outline-cloud_file';
    case OutlineRemoveFolder = 'solar-outline-remove_folder';
    case OutlineFolderFavouritestar = 'solar-outline-folder_favourite(star)';
    case OutlineAddFolder = 'solar-outline-add_folder';
    case OutlineFolderCheck = 'solar-outline-folder_check';
    case OutlineFolderFavouritebookmark = 'solar-outline-folder_favourite(bookmark)';
    case OutlineFolder2 = 'solar-outline-folder2';
    case OutlineFolderSecurity = 'solar-outline-folder_security';
    case OutlineFolderCloud = 'solar-outline-folder_cloud';
    case OutlineMoveToFolder = 'solar-outline-move_to_folder';
    case OutlineFolderError = 'solar-outline-folder_error';
    case OutlineFolderPathConnect = 'solar-outline-folder_path_connect';
    case OutlineFolderOpen = 'solar-outline-folder_open';
    case OutlineFolder = 'solar-outline-Folder';
    case OutlineFolderWithFiles = 'solar-outline-folder_with_files';
    case OutlineCloudCheck = 'solar-outline-cloud_check';
    case OutlineTemperature = 'solar-outline-Temperature';
    case OutlineWind = 'solar-outline-Wind';
    case OutlineCloudSnowfall = 'solar-outline-cloud_snowfall';
    case OutlineSunrise = 'solar-outline-Sunrise';
    case OutlineSun2 = 'solar-outline-sun2';
    case OutlineCloudSun = 'solar-outline-cloud_sun';
    case OutlineCloudBoltMinimalistic = 'solar-outline-cloud_bolt_minimalistic';
    case OutlineCloudDownload = 'solar-outline-cloud_download';
    case OutlineClouds = 'solar-outline-Clouds';
    case OutlineTornado = 'solar-outline-Tornado';
    case OutlineMoonSleep = 'solar-outline-moon_sleep';
    case OutlineCloudUpload = 'solar-outline-cloud_upload';
    case OutlineCloudRain = 'solar-outline-cloud_rain';
    case OutlineFog = 'solar-outline-Fog';
    case OutlineSnowflake = 'solar-outline-Snowflake';
    case OutlineMoonFog = 'solar-outline-moon_fog';
    case OutlineCloudMinus = 'solar-outline-cloud_minus';
    case OutlineCloudBolt = 'solar-outline-cloud_bolt';
    case OutlineCloudWaterdrop = 'solar-outline-cloud_waterdrop';
    case OutlineSunset = 'solar-outline-Sunset';
    case OutlineWaterdrops = 'solar-outline-Waterdrops';
    case OutlineMoonStars = 'solar-outline-moon_stars';
    case OutlineCloudPlus = 'solar-outline-cloud_plus';
    case OutlineSun = 'solar-outline-Sun';
    case OutlineCloudWaterdrops = 'solar-outline-cloud_waterdrops';
    case OutlineCloudSun2 = 'solar-outline-cloud_sun2';
    case OutlineCloudyMoon = 'solar-outline-cloudy_moon';
    case OutlineTornadoSmall = 'solar-outline-tornado_small';
    case OutlineCloud = 'solar-outline-Cloud';
    case OutlineSunFog = 'solar-outline-sun_fog';
    case OutlineCloundCross = 'solar-outline-clound_cross';
    case OutlineCloudSnowfallMinimalistic = 'solar-outline-cloud_snowfall_minimalistic';
    case OutlineCloudStorm = 'solar-outline-cloud_storm';
    case OutlineMoon = 'solar-outline-Moon';
    case OutlineRefreshCircle = 'solar-outline-refresh_circle';
    case OutlineSquareArrowRightDown = 'solar-outline-square_arrow_right_down';
    case OutlineRoundArrowLeftDown = 'solar-outline-round_arrow_left_down';
    case OutlineRestart = 'solar-outline-Restart';
    case OutlineRoundAltArrowDown = 'solar-outline-round_alt_arrow_down';
    case OutlineRoundSortVertical = 'solar-outline-round_sort_vertical';
    case OutlineSquareAltArrowUp = 'solar-outline-square_alt_arrow_up';
    case OutlineArrowLeftUp = 'solar-outline-arrow_left_up';
    case OutlineSortHorizontal = 'solar-outline-sort_horizontal';
    case OutlineTransferHorizontal = 'solar-outline-transfer_horizontal';
    case OutlineSquareDoubleAltArrowUp = 'solar-outline-square_double_alt_arrow_up';
    case OutlineRoundArrowLeftUp = 'solar-outline-round_arrow_left_up';
    case OutlineAltArrowRight = 'solar-outline-alt_arrow_right';
    case OutlineRoundDoubleAltArrowUp = 'solar-outline-round_double_alt_arrow_up';
    case OutlineRestartCircle = 'solar-outline-restart_circle';
    case OutlineSquareArrowDown = 'solar-outline-square_arrow_down';
    case OutlineSortVertical = 'solar-outline-sort_vertical';
    case OutlineSquareSortHorizontal = 'solar-outline-square_sort_horizontal';
    case OutlineDoubleAltArrowLeft = 'solar-outline-double_alt_arrow_left';
    case OutlineSquareAltArrowDown = 'solar-outline-square_alt_arrow_down';
    case OutlineSquareAltArrowRight = 'solar-outline-square_alt_arrow_right';
    case OutlineSquareArrowUp = 'solar-outline-square_arrow_up';
    case OutlineDoubleAltArrowRight = 'solar-outline-double_alt_arrow_right';
    case OutlineRoundTransferVertical = 'solar-outline-round_transfer_vertical';
    case OutlineArrowLeft = 'solar-outline-arrow_left';
    case OutlineRoundDoubleAltArrowRight = 'solar-outline-round_double_alt_arrow_right';
    case OutlineSquareDoubleAltArrowLeft = 'solar-outline-square_double_alt_arrow_left';
    case OutlineAltArrowDown = 'solar-outline-alt_arrow_down';
    case OutlineRoundTransferHorizontal = 'solar-outline-round_transfer_horizontal';
    case OutlineRoundArrowRightDown = 'solar-outline-round_arrow_right_down';
    case OutlineArrowUp = 'solar-outline-arrow_up';
    case OutlineRoundArrowLeft = 'solar-outline-round_arrow_left';
    case OutlineDoubleAltArrowUp = 'solar-outline-double_alt_arrow_up';
    case OutlineRoundArrowRight = 'solar-outline-round_arrow_right';
    case OutlineSquareTransferHorizontal = 'solar-outline-square_transfer_horizontal';
    case OutlineArrowRight = 'solar-outline-arrow_right';
    case OutlineRoundDoubleAltArrowLeft = 'solar-outline-round_double_alt_arrow_left';
    case OutlineRoundArrowUp = 'solar-outline-round_arrow_up';
    case OutlineSquareSortVertical = 'solar-outline-square_sort_vertical';
    case OutlineAltArrowLeft = 'solar-outline-alt_arrow_left';
    case OutlineSquareDoubleAltArrowRight = 'solar-outline-square_double_alt_arrow_right';
    case OutlineRefresh = 'solar-outline-Refresh';
    case OutlineTransferVertical = 'solar-outline-transfer_vertical';
    case OutlineRefreshSquare = 'solar-outline-refresh_square';
    case OutlineSquareTransferVertical = 'solar-outline-square_transfer_vertical';
    case OutlineSquareDoubleAltArrowDown = 'solar-outline-square_double_alt_arrow_down';
    case OutlineRoundArrowRightUp = 'solar-outline-round_arrow_right_up';
    case OutlineArrowDown = 'solar-outline-arrow_down';
    case OutlineRestartSquare = 'solar-outline-restart_square';
    case OutlineSquareArrowRight = 'solar-outline-square_arrow_right';
    case OutlineRoundDoubleAltArrowDown = 'solar-outline-round_double_alt_arrow_down';
    case OutlineSquareArrowLeftUp = 'solar-outline-square_arrow_left_up';
    case OutlineRoundArrowDown = 'solar-outline-round_arrow_down';
    case OutlineSquareArrowRightUp = 'solar-outline-square_arrow_right_up';
    case OutlineRoundTransferDiagonal = 'solar-outline-round_transfer_diagonal';
    case OutlineArrowRightDown = 'solar-outline-arrow_right_down';
    case OutlineArrowLeftDown = 'solar-outline-arrow_left_down';
    case OutlineRoundAltArrowLeft = 'solar-outline-round_alt_arrow_left';
    case OutlineArrowRightUp = 'solar-outline-arrow_right_up';
    case OutlineSquareArrowLeftDown = 'solar-outline-square_arrow_left_down';
    case OutlineRoundAltArrowUp = 'solar-outline-round_alt_arrow_up';
    case OutlineAltArrowUp = 'solar-outline-alt_arrow_up';
    case OutlineSquareAltArrowLeft = 'solar-outline-square_alt_arrow_left';
    case OutlineRoundSortHorizontal = 'solar-outline-round_sort_horizontal';
    case OutlineDoubleAltArrowDown = 'solar-outline-double_alt_arrow_down';
    case OutlineRoundAltArrowRight = 'solar-outline-round_alt_arrow_right';
    case OutlineSquareArrowLeft = 'solar-outline-square_arrow_left';
    case OutlineTuningSquare2 = 'solar-outline-tuning_square2';
    case OutlineWidgetAdd = 'solar-outline-widget_add';
    case OutlineTuningSquare = 'solar-outline-tuning_square';
    case OutlineSettingsMinimalistic = 'solar-outline-settings_minimalistic';
    case OutlineWidget6 = 'solar-outline-widget6';
    case OutlineWidget4 = 'solar-outline-widget4';
    case OutlineSettings = 'solar-outline-Settings';
    case OutlineWidget5 = 'solar-outline-widget5';
    case OutlineWidget2 = 'solar-outline-widget2';
    case OutlineWidget3 = 'solar-outline-widget3';
    case OutlineTuning2 = 'solar-outline-tuning2';
    case OutlineTuning3 = 'solar-outline-tuning3';
    case OutlineWidget = 'solar-outline-Widget';
    case OutlineTuning4 = 'solar-outline-tuning4';
    case OutlineTuning = 'solar-outline-Tuning';
    case OutlineDiagramDown = 'solar-outline-diagram_down';
    case OutlineChart2 = 'solar-outline-chart2';
    case OutlineChart = 'solar-outline-Chart';
    case OutlineDiagramUp = 'solar-outline-diagram_up';
    case OutlineGraphNew = 'solar-outline-graph_new';
    case OutlineCourseUp = 'solar-outline-course_up';
    case OutlineGraphDownNew = 'solar-outline-graph_down_new';
    case OutlinePieChart3 = 'solar-outline-pie_chart3';
    case OutlinePieChart2 = 'solar-outline-pie_chart2';
    case OutlineGraphNewUp = 'solar-outline-graph_new_up';
    case OutlinePieChart = 'solar-outline-pie_chart';
    case OutlineRoundGraph = 'solar-outline-round_graph';
    case OutlineGraphUp = 'solar-outline-graph_up';
    case OutlineChartSquare = 'solar-outline-chart_square';
    case OutlineCourseDown = 'solar-outline-course_down';
    case OutlineChatSquare2 = 'solar-outline-chat_square2';
    case OutlineGraphDown = 'solar-outline-graph_down';
    case OutlineGraph = 'solar-outline-Graph';
    case OutlinePresentationGraph = 'solar-outline-presentation_graph';
    case OutlineMaximizeSquare3 = 'solar-outline-maximize_square3';
    case OutlineMaximizeSquareMinimalistic = 'solar-outline-maximize_square_minimalistic';
    case OutlineMaximizeSquare2 = 'solar-outline-maximize_square2';
    case OutlineMinimizeSquare = 'solar-outline-minimize_square';
    case OutlineDownloadSquare = 'solar-outline-download_square';
    case OutlineUndoLeftRoundSquare = 'solar-outline-undo_left_round_square';
    case OutlineReply = 'solar-outline-Reply';
    case OutlineLogout = 'solar-outline-Logout';
    case OutlineReciveSquare = 'solar-outline-recive_square';
    case OutlineExport = 'solar-outline-Export';
    case OutlineSendTwiceSquare = 'solar-outline-send_twice_square';
    case OutlineUndoLeftRound = 'solar-outline-undo_left_round';
    case OutlineForward2 = 'solar-outline-forward2';
    case OutlineMaximize = 'solar-outline-Maximize';
    case OutlineUndoRightRound = 'solar-outline-undo_right_round';
    case OutlineMinimizeSquare2 = 'solar-outline-minimize_square2';
    case OutlineMinimizeSquare3 = 'solar-outline-minimize_square3';
    case OutlineUploadTwiceSquare = 'solar-outline-upload_twice_square';
    case OutlineMinimize = 'solar-outline-Minimize';
    case OutlineCircleTopUp = 'solar-outline-circle_top_up';
    case OutlineUploadMinimalistic = 'solar-outline-upload_minimalistic';
    case OutlineDownload = 'solar-outline-Download';
    case OutlineImport = 'solar-outline-Import';
    case OutlineLogin = 'solar-outline-Login';
    case OutlineUndoLeft = 'solar-outline-undo_left';
    case OutlineSquareTopUp = 'solar-outline-square_top_up';
    case OutlineDownloadTwiceSquare = 'solar-outline-download_twice_square';
    case OutlineCircleBottomDown = 'solar-outline-circle_bottom_down';
    case OutlineMaximizeSquare = 'solar-outline-maximize_square';
    case OutlineUploadSquare = 'solar-outline-upload_square';
    case OutlineUndoRightSquare = 'solar-outline-undo_right_square';
    case OutlineReciveTwiceSquare = 'solar-outline-recive_twice_square';
    case OutlineCircleTopDown = 'solar-outline-circle_top_down';
    case OutlineArrowToDownLeft = 'solar-outline-arrow_to_down_left';
    case OutlineLogout2 = 'solar-outline-logout2';
    case OutlineLogout3 = 'solar-outline-logout3';
    case OutlineScale = 'solar-outline-Scale';
    case OutlineArrowToDownRight = 'solar-outline-arrow_to_down_right';
    case OutlineDownloadMinimalistic = 'solar-outline-download_minimalistic';
    case OutlineMinimizeSquareMinimalistic = 'solar-outline-minimize_square_minimalistic';
    case OutlineReply2 = 'solar-outline-reply2';
    case OutlineSquareBottomUp = 'solar-outline-square_bottom_up';
    case OutlineUndoRight = 'solar-outline-undo_right';
    case OutlineUndoLeftSquare = 'solar-outline-undo_left_square';
    case OutlineSendSquare = 'solar-outline-send_square';
    case OutlineExit = 'solar-outline-Exit';
    case OutlineSquareBottomDown = 'solar-outline-square_bottom_down';
    case OutlineUndoRightRoundSquare = 'solar-outline-undo_right_round_square';
    case OutlineArrowToTopLeft = 'solar-outline-arrow_to_top_left';
    case OutlineCircleBottomUp = 'solar-outline-circle_bottom_up';
    case OutlineScreenShare = 'solar-outline-screen_share';
    case OutlineUpload = 'solar-outline-Upload';
    case OutlineSquareTopDown = 'solar-outline-square_top_down';
    case OutlineArrowToTopRight = 'solar-outline-arrow_to_top_right';
    case OutlineLogin3 = 'solar-outline-login3';
    case OutlineLogin2 = 'solar-outline-login2';
    case OutlinePassport = 'solar-outline-Passport';
    case OutlineDiplomaVerified = 'solar-outline-diploma_verified';
    case OutlineCaseRound = 'solar-outline-case_round';
    case OutlineBackpack = 'solar-outline-Backpack';
    case OutlineBook2 = 'solar-outline-book2';
    case OutlineSquareAcademicCap2 = 'solar-outline-square_academic_cap2';
    case OutlineCaseRoundMinimalistic = 'solar-outline-case_round_minimalistic';
    case OutlineCase = 'solar-outline-Case';
    case OutlineBookBookmarkMinimalistic = 'solar-outline-book_bookmark_minimalistic';
    case OutlineBookmarkOpened = 'solar-outline-bookmark_opened';
    case OutlineDiploma = 'solar-outline-Diploma';
    case OutlineBook = 'solar-outline-Book';
    case OutlineSquareAcademicCap = 'solar-outline-square_academic_cap';
    case OutlineBookmarkCircle = 'solar-outline-bookmark_circle';
    case OutlineCalculatorMinimalistic = 'solar-outline-calculator_minimalistic';
    case OutlineNotebookSquare = 'solar-outline-notebook_square';
    case OutlineBookMinimalistic = 'solar-outline-book_minimalistic';
    case OutlineCaseMinimalistic = 'solar-outline-case_minimalistic';
    case OutlineNotebookBookmark = 'solar-outline-notebook_bookmark';
    case OutlinePassportMinimalistic = 'solar-outline-passport_minimalistic';
    case OutlineBookBookmark = 'solar-outline-book_bookmark';
    case OutlineBookmarkSquareMinimalistic = 'solar-outline-bookmark_square_minimalistic';
    case OutlineBookmark = 'solar-outline-Bookmark';
    case OutlinePlusMinus = 'solar-outline-plus,_minus';
    case OutlineCalculator = 'solar-outline-Calculator';
    case OutlineBookmarkSquare = 'solar-outline-bookmark_square';
    case OutlineNotebookMinimalistic = 'solar-outline-notebook_minimalistic';
    case OutlineFireSquare = 'solar-outline-fire_square';
    case OutlineSuitcaseLines = 'solar-outline-suitcase_lines';
    case OutlineFire = 'solar-outline-Fire';
    case OutlineBonfire = 'solar-outline-Bonfire';
    case OutlineSuitcaseTag = 'solar-outline-suitcase_tag';
    case OutlineLeaf = 'solar-outline-Leaf';
    case OutlineSuitcase = 'solar-outline-Suitcase';
    case OutlineFlame = 'solar-outline-Flame';
    case OutlineFireMinimalistic = 'solar-outline-fire_minimalistic';
    case OutlineBellBing = 'solar-outline-bell_bing';
    case OutlineNotificationLinesRemove = 'solar-outline-notification_lines_remove';
    case OutlineNotificationUnread = 'solar-outline-notification_unread';
    case OutlineBell = 'solar-outline-Bell';
    case OutlineNotificationRemove = 'solar-outline-notification_remove';
    case OutlineNotificationUnreadLines = 'solar-outline-notification_unread_lines';
    case OutlineBellOff = 'solar-outline-bell_off';
    case OutlineLightning = 'solar-outline-Lightning';
    case OutlineLightbulbMinimalistic = 'solar-outline-lightbulb_minimalistic';
    case OutlineServerSquareCloud = 'solar-outline-server_square_cloud';
    case OutlineLightbulbBolt = 'solar-outline-lightbulb_bolt';
    case OutlineAirbudsCharge = 'solar-outline-airbuds_charge';
    case OutlineServerPath = 'solar-outline-server_path';
    case OutlineSimCardMinimalistic = 'solar-outline-sim_card_minimalistic';
    case OutlineSmartphone = 'solar-outline-Smartphone';
    case OutlineTurntable = 'solar-outline-Turntable';
    case OutlineAirbudsCheck = 'solar-outline-airbuds_check';
    case OutlineMouseMinimalistic = 'solar-outline-mouse_minimalistic';
    case OutlineSmartphoneRotateAngle = 'solar-outline-smartphone_rotate_angle';
    case OutlineRadioMinimalistic = 'solar-outline-radio_minimalistic';
    case OutlineAirbuds = 'solar-outline-Airbuds';
    case OutlineSmartphoneRotateOrientation = 'solar-outline-smartphone_rotate_orientation';
    case OutlineIPhone = 'solar-outline-i_phone';
    case OutlineSimCard = 'solar-outline-sim_card';
    case OutlineFlashDrive = 'solar-outline-flash_drive';
    case OutlineDevices = 'solar-outline-Devices';
    case OutlineSimCards = 'solar-outline-sim_cards';
    case OutlineAirbudsCaseOpen = 'solar-outline-airbuds_case_open';
    case OutlineTurntableMusicNote = 'solar-outline-turntable_music_note';
    case OutlineKeyboard = 'solar-outline-Keyboard';
    case OutlineGamepadCharge = 'solar-outline-gamepad_charge';
    case OutlineBoombox = 'solar-outline-Boombox';
    case OutlineSmartSpeakerMinimalistic = 'solar-outline-smart_speaker_minimalistic';
    case OutlineTelescope = 'solar-outline-Telescope';
    case OutlineMonitorCamera = 'solar-outline-monitor_camera';
    case OutlineLaptopMinimalistic = 'solar-outline-laptop_minimalistic';
    case OutlineServer2 = 'solar-outline-server2';
    case OutlineSmartSpeaker = 'solar-outline-smart_speaker';
    case OutlineProjector = 'solar-outline-Projector';
    case OutlineServer = 'solar-outline-Server';
    case OutlineTV = 'solar-outline-TV';
    case OutlineCassette2 = 'solar-outline-cassette2';
    case OutlineRadio = 'solar-outline-Radio';
    case OutlineSmartphoneVibration = 'solar-outline-smartphone_vibration';
    case OutlineAirbudsLeft = 'solar-outline-airbuds_left';
    case OutlineHeadphonesRound = 'solar-outline-headphones_round';
    case OutlineGameboy = 'solar-outline-Gameboy';
    case OutlineHeadphonesRoundSound = 'solar-outline-headphones_round_sound';
    case OutlineCPU = 'solar-outline-CPU';
    case OutlinePrinter2 = 'solar-outline-printer2';
    case OutlineHeadphonesSquare = 'solar-outline-headphones_square';
    case OutlineServerSquareUpdate = 'solar-outline-server_square_update';
    case OutlinePrinterMinimalistic = 'solar-outline-printer_minimalistic';
    case OutlineBluetooth = 'solar-outline-Bluetooth';
    case OutlineWirelessCharge = 'solar-outline-wireless_charge';
    case OutlineBluetoothCircle = 'solar-outline-bluetooth_circle';
    case OutlineAirbudsCaseMinimalistic = 'solar-outline-airbuds_case_minimalistic';
    case OutlineLightbulb = 'solar-outline-Lightbulb';
    case OutlineAirbudsRemove = 'solar-outline-airbuds_remove';
    case OutlineSmartphoneRotate2 = 'solar-outline-smartphone_rotate2';
    case OutlineSsdSquare = 'solar-outline-ssd_square';
    case OutlinePrinter = 'solar-outline-Printer';
    case OutlineSmartphone2 = 'solar-outline-smartphone2';
    case OutlineServerMinimalistic = 'solar-outline-server_minimalistic';
    case OutlineHeadphonesSquareSound = 'solar-outline-headphones_square_sound';
    case OutlineDiskette = 'solar-outline-Diskette';
    case OutlineBluetoothWave = 'solar-outline-bluetooth_wave';
    case OutlineSmartSpeaker2 = 'solar-outline-smart_speaker2';
    case OutlineLaptop3 = 'solar-outline-laptop3';
    case OutlineLaptop2 = 'solar-outline-laptop2';
    case OutlineMouseCircle = 'solar-outline-mouse_circle';
    case OutlineTurntableMinimalistic = 'solar-outline-turntable_minimalistic';
    case OutlineSmartphoneUpdate = 'solar-outline-smartphone_update';
    case OutlineGamepadMinimalistic = 'solar-outline-gamepad_minimalistic';
    case OutlineSdCard = 'solar-outline-sd_card';
    case OutlinePlugCircle = 'solar-outline-plug_circle';
    case OutlineAirbudsCase = 'solar-outline-airbuds_case';
    case OutlineSsdRound = 'solar-outline-ssd_round';
    case OutlineLaptop = 'solar-outline-Laptop';
    case OutlineAirbudsRight = 'solar-outline-airbuds_right';
    case OutlineDisplay = 'solar-outline-Display';
    case OutlineMonitorSmartphone = 'solar-outline-monitor_smartphone';
    case OutlineSocket = 'solar-outline-Socket';
    case OutlineGamepadOld = 'solar-outline-gamepad_old';
    case OutlineCpuBolt = 'solar-outline-cpu_bolt';
    case OutlineAirbudsCaseCharge = 'solar-outline-airbuds_case_charge';
    case OutlineTablet = 'solar-outline-Tablet';
    case OutlineWeigher = 'solar-outline-Weigher';
    case OutlineServerSquare = 'solar-outline-server_square';
    case OutlineMouse = 'solar-outline-Mouse';
    case OutlineGamepadNoCharge = 'solar-outline-gamepad_no_charge';
    case OutlineBluetoothSquare = 'solar-outline-bluetooth_square';
    case OutlineCloudStorage = 'solar-outline-cloud_storage';
    case OutlineGamepad = 'solar-outline-Gamepad';
    case OutlineMonitor = 'solar-outline-Monitor';
    case OutlineCassette = 'solar-outline-Cassette';
    // Linear Style (1235 icons)
    case LinearFacemaskCircle = 'solar-linear-facemask_circle';
    case LinearConfoundedCircle = 'solar-linear-confounded_circle';
    case LinearSadSquare = 'solar-linear-sad_square';
    case LinearSleepingCircle = 'solar-linear-sleeping_circle';
    case LinearFaceScanCircle = 'solar-linear-face_scan_circle';
    case LinearSmileCircle = 'solar-linear-smile_circle';
    case LinearStickerSmileCircle = 'solar-linear-sticker_smile_circle';
    case LinearStickerSquare = 'solar-linear-sticker_square';
    case LinearEmojiFunnyCircle = 'solar-linear-emoji_funny_circle';
    case LinearExpressionlessSquare = 'solar-linear-expressionless_square';
    case LinearSleepingSquare = 'solar-linear-sleeping_square';
    case LinearSadCircle = 'solar-linear-sad_circle';
    case LinearFacemaskSquare = 'solar-linear-facemask_square';
    case LinearConfoundedSquare = 'solar-linear-confounded_square';
    case LinearFaceScanSquare = 'solar-linear-face_scan_square';
    case LinearSmileSquare = 'solar-linear-smile_square';
    case LinearStickerSmileCircle2 = 'solar-linear-sticker_smile_circle2';
    case LinearStickerSmileSquare = 'solar-linear-sticker_smile_square';
    case LinearEmojiFunnySquare = 'solar-linear-emoji_funny_square';
    case LinearStickerCircle = 'solar-linear-sticker_circle';
    case LinearExpressionlessCircle = 'solar-linear-expressionless_circle';
    case LinearLike = 'solar-linear-Like';
    case LinearMedalStarSquare = 'solar-linear-medal_star_square';
    case LinearDislike = 'solar-linear-Dislike';
    case LinearStarShine = 'solar-linear-star_shine';
    case LinearHeartAngle = 'solar-linear-heart_angle';
    case LinearMedalRibbon = 'solar-linear-medal_ribbon';
    case LinearHeartShine = 'solar-linear-heart_shine';
    case LinearMedalStarCircle = 'solar-linear-medal_star_circle';
    case LinearMedalRibbonsStar = 'solar-linear-medal_ribbons_star';
    case LinearStar = 'solar-linear-Star';
    case LinearHeartUnlock = 'solar-linear-heart_unlock';
    case LinearMedalRibbonStar = 'solar-linear-medal_ribbon_star';
    case LinearHeartLock = 'solar-linear-heart_lock';
    case LinearHeartBroken = 'solar-linear-heart_broken';
    case LinearHearts = 'solar-linear-Hearts';
    case LinearMedalStar = 'solar-linear-medal_star';
    case LinearHeart = 'solar-linear-Heart';
    case LinearCloset = 'solar-linear-Closet';
    case LinearBed = 'solar-linear-Bed';
    case LinearWashingMachine = 'solar-linear-washing_machine';
    case LinearBedsideTable = 'solar-linear-bedside_table';
    case LinearSofa3 = 'solar-linear-sofa3';
    case LinearSofa2 = 'solar-linear-sofa2';
    case LinearChair2 = 'solar-linear-chair2';
    case LinearBath = 'solar-linear-Bath';
    case LinearSmartVacuumCleaner2 = 'solar-linear-smart_vacuum_cleaner2';
    case LinearCondicioner = 'solar-linear-Condicioner';
    case LinearSmartVacuumCleaner = 'solar-linear-smart_vacuum_cleaner';
    case LinearRemoteController2 = 'solar-linear-remote_controller2';
    case LinearFloorLampMinimalistic = 'solar-linear-floor_lamp_minimalistic';
    case LinearLamp = 'solar-linear-Lamp';
    case LinearBarChair = 'solar-linear-bar_chair';
    case LinearBedsideTable2 = 'solar-linear-bedside_table2';
    case LinearCloset2 = 'solar-linear-closet2';
    case LinearBedsideTable3 = 'solar-linear-bedside_table3';
    case LinearSpeaker = 'solar-linear-Speaker';
    case LinearVolumeKnob = 'solar-linear-volume_knob';
    case LinearArmchair = 'solar-linear-Armchair';
    case LinearSpeakerMinimalistic = 'solar-linear-speaker_minimalistic';
    case LinearRemoteController = 'solar-linear-remote_controller';
    case LinearTrellis = 'solar-linear-Trellis';
    case LinearFloorLamp = 'solar-linear-floor_lamp';
    case LinearCondicioner2 = 'solar-linear-condicioner2';
    case LinearBedsideTable4 = 'solar-linear-bedside_table4';
    case LinearArmchair2 = 'solar-linear-armchair2';
    case LinearWashingMachineMinimalistic = 'solar-linear-washing_machine_minimalistic';
    case LinearChair = 'solar-linear-Chair';
    case LinearRemoteControllerMinimalistic = 'solar-linear-remote_controller_minimalistic';
    case LinearChandelier = 'solar-linear-Chandelier';
    case LinearFridge = 'solar-linear-Fridge';
    case LinearMirror = 'solar-linear-Mirror';
    case LinearSofa = 'solar-linear-Sofa';
    case LinearEarth = 'solar-linear-Earth';
    case LinearStarsLine = 'solar-linear-stars_line';
    case LinearStarFall2 = 'solar-linear-star_fall2';
    case LinearStarFall = 'solar-linear-star_fall';
    case LinearBlackHole3 = 'solar-linear-black_hole3';
    case LinearWomen = 'solar-linear-Women';
    case LinearBlackHole = 'solar-linear-black_hole';
    case LinearStarRings = 'solar-linear-star_rings';
    case LinearBlackHole2 = 'solar-linear-black_hole2';
    case LinearStarFallMinimalistic2 = 'solar-linear-star_fall_minimalistic2';
    case LinearPlanet = 'solar-linear-Planet';
    case LinearSatellite = 'solar-linear-Satellite';
    case LinearMen = 'solar-linear-Men';
    case LinearRocket2 = 'solar-linear-rocket2';
    case LinearStars = 'solar-linear-Stars';
    case LinearStarAngle = 'solar-linear-star_angle';
    case LinearInfinity = 'solar-linear-Infinity';
    case LinearUfo2 = 'solar-linear-ufo2';
    case LinearUfo3 = 'solar-linear-ufo3';
    case LinearStarRing = 'solar-linear-star_ring';
    case LinearPlanet2 = 'solar-linear-planet2';
    case LinearPlanet3 = 'solar-linear-planet3';
    case LinearAsteroid = 'solar-linear-Asteroid';
    case LinearStarsMinimalistic = 'solar-linear-stars_minimalistic';
    case LinearUFO = 'solar-linear-UFO';
    case LinearPlanet4 = 'solar-linear-planet4';
    case LinearRocket = 'solar-linear-Rocket';
    case LinearStarFallMinimalistic = 'solar-linear-star_fall_minimalistic';
    case LinearStarRainbow = 'solar-linear-star_rainbow';
    case LinearAtom = 'solar-linear-Atom';
    case LinearStarCircle = 'solar-linear-star_circle';
    case LinearCompassBig = 'solar-linear-compass_big';
    case LinearMapPointSchool = 'solar-linear-map_point_school';
    case LinearSignpost = 'solar-linear-Signpost';
    case LinearMapArrowDown = 'solar-linear-map_arrow_down';
    case LinearMap = 'solar-linear-Map';
    case LinearMapArrowUp = 'solar-linear-map_arrow_up';
    case LinearPointOnMapPerspective = 'solar-linear-point_on_map_perspective';
    case LinearRadar = 'solar-linear-Radar';
    case LinearStreets = 'solar-linear-Streets';
    case LinearMapPointWave = 'solar-linear-map_point_wave';
    case LinearPeopleNearby = 'solar-linear-people_nearby';
    case LinearStreetsMapPoint = 'solar-linear-streets_map_point';
    case LinearMapPointSearch = 'solar-linear-map_point_search';
    case LinearGPS = 'solar-linear-GPS';
    case LinearMapArrowSquare = 'solar-linear-map_arrow_square';
    case LinearBranchingPathsDown = 'solar-linear-branching_paths_down';
    case LinearMapPointRotate = 'solar-linear-map_point_rotate';
    case LinearGlobal = 'solar-linear-Global';
    case LinearCompassSquare = 'solar-linear-compass_square';
    case LinearRouting3 = 'solar-linear-routing3';
    case LinearRouting2 = 'solar-linear-routing2';
    case LinearMapPointRemove = 'solar-linear-map_point_remove';
    case LinearGlobus = 'solar-linear-Globus';
    case LinearSignpost2 = 'solar-linear-signpost2';
    case LinearRadar2 = 'solar-linear-radar2';
    case LinearStreetsNavigation = 'solar-linear-streets_navigation';
    case LinearMapPoint = 'solar-linear-map_point';
    case LinearMapPointHospital = 'solar-linear-map_point_hospital';
    case LinearCompass = 'solar-linear-Compass';
    case LinearMapPointAdd = 'solar-linear-map_point_add';
    case LinearBranchingPathsUp = 'solar-linear-branching_paths_up';
    case LinearMapPointFavourite = 'solar-linear-map_point_favourite';
    case LinearRoute = 'solar-linear-Route';
    case LinearPointOnMap = 'solar-linear-point_on_map';
    case LinearMapArrowRight = 'solar-linear-map_arrow_right';
    case LinearRouting = 'solar-linear-Routing';
    case LinearMapArrowLeft = 'solar-linear-map_arrow_left';
    case LinearIncognito = 'solar-linear-Incognito';
    case LinearLockPassword = 'solar-linear-lock_password';
    case LinearShieldNetwork = 'solar-linear-shield_network';
    case LinearKeyMinimalisticSquare = 'solar-linear-key_minimalistic_square';
    case LinearLockKeyholeUnlocked = 'solar-linear-lock_keyhole_unlocked';
    case LinearLock = 'solar-linear-Lock';
    case LinearShieldKeyhole = 'solar-linear-shield_keyhole';
    case LinearEyeClosed = 'solar-linear-eye_closed';
    case LinearKey = 'solar-linear-Key';
    case LinearShieldMinus = 'solar-linear-shield_minus';
    case LinearShield = 'solar-linear-Shield';
    case LinearLockUnlocked = 'solar-linear-lock_unlocked';
    case LinearBombMinimalistic = 'solar-linear-bomb_minimalistic';
    case LinearShieldStar = 'solar-linear-shield_star';
    case LinearBomb = 'solar-linear-Bomb';
    case LinearKeySquare = 'solar-linear-key_square';
    case LinearLockKeyholeMinimalisticUnlocked = 'solar-linear-lock_keyhole_minimalistic_unlocked';
    case LinearShieldCross = 'solar-linear-shield_cross';
    case LinearObjectScan = 'solar-linear-object_scan';
    case LinearPasswordMinimalisticInput = 'solar-linear-password_minimalistic_input';
    case LinearLockPasswordUnlocked = 'solar-linear-lock_password_unlocked';
    case LinearSiren = 'solar-linear-Siren';
    case LinearShieldMinimalistic = 'solar-linear-shield_minimalistic';
    case LinearEyeScan = 'solar-linear-eye_scan';
    case LinearKeyMinimalisticSquare2 = 'solar-linear-key_minimalistic_square2';
    case LinearScanner2 = 'solar-linear-scanner2';
    case LinearKeyMinimalisticSquare3 = 'solar-linear-key_minimalistic_square3';
    case LinearKeyMinimalistic2 = 'solar-linear-key_minimalistic2';
    case LinearCodeScan = 'solar-linear-code_scan';
    case LinearShieldPlus = 'solar-linear-shield_plus';
    case LinearPasswordMinimalistic = 'solar-linear-password_minimalistic';
    case LinearEye = 'solar-linear-Eye';
    case LinearQrCode = 'solar-linear-qr_code';
    case LinearShieldCheck = 'solar-linear-shield_check';
    case LinearKeyMinimalistic = 'solar-linear-key_minimalistic';
    case LinearLockKeyhole = 'solar-linear-lock_keyhole';
    case LinearShieldUser = 'solar-linear-shield_user';
    case LinearKeySquare2 = 'solar-linear-key_square2';
    case LinearBombEmoji = 'solar-linear-bomb_emoji';
    case LinearScanner = 'solar-linear-Scanner';
    case LinearShieldUp = 'solar-linear-shield_up';
    case LinearSirenRounded = 'solar-linear-siren_rounded';
    case LinearLockKeyholeMinimalistic = 'solar-linear-lock_keyhole_minimalistic';
    case LinearPassword = 'solar-linear-Password';
    case LinearShieldKeyholeMinimalistic = 'solar-linear-shield_keyhole_minimalistic';
    case LinearShieldWarning = 'solar-linear-shield_warning';
    case LinearPallete2 = 'solar-linear-pallete2';
    case LinearAlignVerticalSpacing = 'solar-linear-align_vertical_spacing';
    case LinearAlignVerticalCenter = 'solar-linear-align_vertical_center';
    case LinearCropMinimalistic = 'solar-linear-crop_minimalistic';
    case LinearMirrorRight = 'solar-linear-mirror_right';
    case LinearAlignBottom = 'solar-linear-align_bottom';
    case LinearRadialBlur = 'solar-linear-radial_blur';
    case LinearCrop = 'solar-linear-Crop';
    case LinearAlignHorizontaSpacing = 'solar-linear-align_horizonta_spacing';
    case LinearRulerPen = 'solar-linear-ruler_pen';
    case LinearThreeSquares = 'solar-linear-three_squares';
    case LinearPaintRoller = 'solar-linear-paint_roller';
    case LinearLayers = 'solar-linear-Layers';
    case LinearFilters = 'solar-linear-Filters';
    case LinearRulerCrossPen = 'solar-linear-ruler_cross_pen';
    case LinearFlipHorizontal = 'solar-linear-flip_horizontal';
    case LinearAlignLeft = 'solar-linear-align_left';
    case LinearRuler = 'solar-linear-Ruler';
    case LinearPalette = 'solar-linear-Palette';
    case LinearAlignTop = 'solar-linear-align_top';
    case LinearAlignHorizontalCenter = 'solar-linear-align_horizontal_center';
    case LinearAlignRight = 'solar-linear-align_right';
    case LinearRulerAngular = 'solar-linear-ruler_angular';
    case LinearPipette = 'solar-linear-Pipette';
    case LinearFlipVertical = 'solar-linear-flip_vertical';
    case LinearMirrorLeft = 'solar-linear-mirror_left';
    case LinearLayersMinimalistic = 'solar-linear-layers_minimalistic';
    case LinearColourTuneing = 'solar-linear-colour_tuneing';
    case LinearPaletteRound = 'solar-linear-palette_round';
    case LinearEraser = 'solar-linear-Eraser';
    case LinearTextItalicCircle = 'solar-linear-text_italic_circle';
    case LinearLinkRound = 'solar-linear-link_round';
    case LinearTextItalic = 'solar-linear-text_italic';
    case LinearLinkBrokenMinimalistic = 'solar-linear-link_broken_minimalistic';
    case LinearTextUnderlineCross = 'solar-linear-text_underline_cross';
    case LinearLink = 'solar-linear-Link';
    case LinearEraserCircle = 'solar-linear-eraser_circle';
    case LinearLinkCircle = 'solar-linear-link_circle';
    case LinearTextBoldCircle = 'solar-linear-text_bold_circle';
    case LinearTextField = 'solar-linear-text_field';
    case LinearTextSquare = 'solar-linear-text_square';
    case LinearTextSquare2 = 'solar-linear-text_square2';
    case LinearLinkRoundAngle = 'solar-linear-link_round_angle';
    case LinearTextUnderlineCircle = 'solar-linear-text_underline_circle';
    case LinearTextCrossCircle = 'solar-linear-text_cross_circle';
    case LinearTextItalicSquare = 'solar-linear-text_italic_square';
    case LinearParagraphSpacing = 'solar-linear-paragraph_spacing';
    case LinearText = 'solar-linear-Text';
    case LinearLinkBroken = 'solar-linear-link_broken';
    case LinearTextCross = 'solar-linear-text_cross';
    case LinearTextUnderline = 'solar-linear-text_underline';
    case LinearLinkMinimalistic = 'solar-linear-link_minimalistic';
    case LinearLinkMinimalistic2 = 'solar-linear-link_minimalistic2';
    case LinearTextBold = 'solar-linear-text_bold';
    case LinearTextSelection = 'solar-linear-text_selection';
    case LinearTextFieldFocus = 'solar-linear-text_field_focus';
    case LinearTextBoldSquare = 'solar-linear-text_bold_square';
    case LinearEraserSquare = 'solar-linear-eraser_square';
    case LinearLinkSquare = 'solar-linear-link_square';
    case LinearTextCircle = 'solar-linear-text_circle';
    case LinearBackspace = 'solar-linear-Backspace';
    case LinearTextCrossSquare = 'solar-linear-text_cross_square';
    case LinearInboxUnread = 'solar-linear-inbox_unread';
    case LinearChatUnread = 'solar-linear-chat_unread';
    case LinearChatRound = 'solar-linear-chat_round';
    case LinearUnread = 'solar-linear-Unread';
    case LinearMailbox = 'solar-linear-Mailbox';
    case LinearLetter = 'solar-linear-Letter';
    case LinearPenNewRound = 'solar-linear-pen_new_round';
    case LinearMultipleForwardRight = 'solar-linear-multiple_forward_right';
    case LinearMultipleForwardLeft = 'solar-linear-multiple_forward_left';
    case LinearInboxArchive = 'solar-linear-inbox_archive';
    case LinearInbox = 'solar-linear-Inbox';
    case LinearPen2 = 'solar-linear-pen2';
    case LinearPenNewSquare = 'solar-linear-pen_new_square';
    case LinearPen = 'solar-linear-Pen';
    case LinearChatDots = 'solar-linear-chat_dots';
    case LinearChatSquareCall = 'solar-linear-chat_square_call';
    case LinearSquareShareLine = 'solar-linear-square_share_line';
    case LinearChatRoundCheck = 'solar-linear-chat_round_check';
    case LinearInboxOut = 'solar-linear-inbox_out';
    case LinearPlain3 = 'solar-linear-plain3';
    case LinearChatRoundDots = 'solar-linear-chat_round_dots';
    case LinearChatRoundLike = 'solar-linear-chat_round_like';
    case LinearPlain2 = 'solar-linear-plain2';
    case LinearChatRoundUnread = 'solar-linear-chat_round_unread';
    case LinearChatSquareLike = 'solar-linear-chat_square_like';
    case LinearPaperclip = 'solar-linear-Paperclip';
    case LinearChatSquareCheck = 'solar-linear-chat_square_check';
    case LinearChatSquare = 'solar-linear-chat_square';
    case LinearLetterOpened = 'solar-linear-letter_opened';
    case LinearSquareForward = 'solar-linear-square_forward';
    case LinearLetterUnread = 'solar-linear-letter_unread';
    case LinearPaperclipRounded2 = 'solar-linear-paperclip_rounded2';
    case LinearChatRoundCall = 'solar-linear-chat_round_call';
    case LinearInboxLine = 'solar-linear-inbox_line';
    case LinearChatRoundVideo = 'solar-linear-chat_round_video';
    case LinearChatRoundMoney = 'solar-linear-chat_round_money';
    case LinearInboxIn = 'solar-linear-inbox_in';
    case LinearCheckRead = 'solar-linear-check_read';
    case LinearChatRoundLine = 'solar-linear-chat_round_line';
    case LinearForward = 'solar-linear-Forward';
    case LinearPaperclip2 = 'solar-linear-paperclip2';
    case LinearDialog2 = 'solar-linear-dialog2';
    case LinearDialog = 'solar-linear-Dialog';
    case LinearPaperclipRounded = 'solar-linear-paperclip_rounded';
    case LinearPlain = 'solar-linear-Plain';
    case LinearChatSquareArrow = 'solar-linear-chat_square_arrow';
    case LinearChatSquareCode = 'solar-linear-chat_square_code';
    case LinearChatLine = 'solar-linear-chat_line';
    case LinearTennis = 'solar-linear-Tennis';
    case LinearBicyclingRound = 'solar-linear-bicycling_round';
    case LinearBalls = 'solar-linear-Balls';
    case LinearMeditationRound = 'solar-linear-meditation_round';
    case LinearStretchingRound = 'solar-linear-stretching_round';
    case LinearDumbbells2 = 'solar-linear-dumbbells2';
    case LinearMeditation = 'solar-linear-Meditation';
    case LinearRunning2 = 'solar-linear-running2';
    case LinearRugby = 'solar-linear-Rugby';
    case LinearBodyShapeMinimalistic = 'solar-linear-body_shape_minimalistic';
    case LinearStretching = 'solar-linear-Stretching';
    case LinearBowling = 'solar-linear-Bowling';
    case LinearRanking = 'solar-linear-Ranking';
    case LinearTreadmillRound = 'solar-linear-treadmill_round';
    case LinearVolleyball = 'solar-linear-Volleyball';
    case LinearDumbbellLargeMinimalistic = 'solar-linear-dumbbell_large_minimalistic';
    case LinearRunningRound = 'solar-linear-running_round';
    case LinearHiking = 'solar-linear-Hiking';
    case LinearHikingMinimalistic = 'solar-linear-hiking_minimalistic';
    case LinearWaterSun = 'solar-linear-water_sun';
    case LinearGolf = 'solar-linear-Golf';
    case LinearSkateboarding = 'solar-linear-Skateboarding';
    case LinearDumbbells = 'solar-linear-Dumbbells';
    case LinearWalkingRound = 'solar-linear-walking_round';
    case LinearRunning = 'solar-linear-Running';
    case LinearTreadmill = 'solar-linear-Treadmill';
    case LinearSkateboard = 'solar-linear-Skateboard';
    case LinearDumbbellSmall = 'solar-linear-dumbbell_small';
    case LinearBasketball = 'solar-linear-Basketball';
    case LinearFootball = 'solar-linear-Football';
    case LinearDumbbell = 'solar-linear-Dumbbell';
    case LinearBodyShape = 'solar-linear-body_shape';
    case LinearWater = 'solar-linear-Water';
    case LinearSkateboardingRound = 'solar-linear-skateboarding_round';
    case LinearHikingRound = 'solar-linear-hiking_round';
    case LinearVolleyball2 = 'solar-linear-volleyball2';
    case LinearTennis2 = 'solar-linear-tennis2';
    case LinearSwimming = 'solar-linear-Swimming';
    case LinearBicycling = 'solar-linear-Bicycling';
    case LinearWalking = 'solar-linear-Walking';
    case LinearDumbbellLarge = 'solar-linear-dumbbell_large';
    case LinearCalendarMark = 'solar-linear-calendar_mark';
    case LinearHistory2 = 'solar-linear-history2';
    case LinearWatchSquareMinimalisticCharge = 'solar-linear-watch_square_minimalistic_charge';
    case LinearHistory3 = 'solar-linear-history3';
    case LinearHourglass = 'solar-linear-Hourglass';
    case LinearCalendarSearch = 'solar-linear-calendar_search';
    case LinearStopwatchPlay = 'solar-linear-stopwatch_play';
    case LinearWatchRound = 'solar-linear-watch_round';
    case LinearCalendarAdd = 'solar-linear-calendar_add';
    case LinearCalendarDate = 'solar-linear-calendar_date';
    case LinearStopwatch = 'solar-linear-Stopwatch';
    case LinearAlarmPause = 'solar-linear-alarm_pause';
    case LinearAlarmTurnOff = 'solar-linear-alarm_turn_off';
    case LinearClockSquare = 'solar-linear-clock_square';
    case LinearStopwatchPause = 'solar-linear-stopwatch_pause';
    case LinearCalendarMinimalistic = 'solar-linear-calendar_minimalistic';
    case LinearAlarmAdd = 'solar-linear-alarm_add';
    case LinearAlarmPlay = 'solar-linear-alarm_play';
    case LinearHourglassLine = 'solar-linear-hourglass_line';
    case LinearAlarmSleep = 'solar-linear-alarm_sleep';
    case LinearAlarmRemove = 'solar-linear-alarm_remove';
    case LinearCalendar = 'solar-linear-Calendar';
    case LinearClockCircle = 'solar-linear-clock_circle';
    case LinearHistory = 'solar-linear-History';
    case LinearAlarm = 'solar-linear-Alarm';
    case LinearWatchSquare = 'solar-linear-watch_square';
    case LinearWatchSquareMinimalistic = 'solar-linear-watch_square_minimalistic';
    case LinearMagniferBug = 'solar-linear-magnifer_bug';
    case LinearMagnifer = 'solar-linear-Magnifer';
    case LinearMagniferZoomIn = 'solar-linear-magnifer_zoom_in';
    case LinearRoundedMagnifer = 'solar-linear-rounded_magnifer';
    case LinearRoundedMagniferZoomIn = 'solar-linear-rounded_magnifer_zoom_in';
    case LinearMinimalisticMagniferBug = 'solar-linear-minimalistic_magnifer_bug';
    case LinearRoundedMagniferBug = 'solar-linear-rounded_magnifer_bug';
    case LinearMinimalisticMagniferZoomOut = 'solar-linear-minimalistic_magnifer_zoom_out';
    case LinearMinimalisticMagnifer = 'solar-linear-minimalistic_magnifer';
    case LinearRoundedMagniferZoomOut = 'solar-linear-rounded_magnifer_zoom_out';
    case LinearMinimalisticMagniferZoomIn = 'solar-linear-minimalistic_magnifer_zoom_in';
    case LinearMagniferZoomOut = 'solar-linear-magnifer_zoom_out';
    case LinearBagCheck = 'solar-linear-bag_check';
    case LinearShopMinimalistic = 'solar-linear-shop_minimalistic';
    case LinearShop = 'solar-linear-Shop';
    case LinearCartCheck = 'solar-linear-cart_check';
    case LinearCart = 'solar-linear-Cart';
    case LinearCart3 = 'solar-linear-cart3';
    case LinearCart2 = 'solar-linear-cart2';
    case LinearBagMusic = 'solar-linear-bag_music';
    case LinearCartLargeMinimalistic = 'solar-linear-cart_large_minimalistic';
    case LinearCart5 = 'solar-linear-cart5';
    case LinearCart4 = 'solar-linear-cart4';
    case LinearBag = 'solar-linear-Bag';
    case LinearBagHeart = 'solar-linear-bag_heart';
    case LinearCartPlus = 'solar-linear-cart_plus';
    case LinearCartLarge = 'solar-linear-cart_large';
    case LinearBagCross = 'solar-linear-bag_cross';
    case LinearBagMusic2 = 'solar-linear-bag_music2';
    case LinearBag5 = 'solar-linear-bag5';
    case LinearBag4 = 'solar-linear-bag4';
    case LinearCartLarge4 = 'solar-linear-cart_large4';
    case LinearCartLarge3 = 'solar-linear-cart_large3';
    case LinearBag3 = 'solar-linear-bag3';
    case LinearBag2 = 'solar-linear-bag2';
    case LinearShop2 = 'solar-linear-shop2';
    case LinearCartLarge2 = 'solar-linear-cart_large2';
    case LinearBagSmile = 'solar-linear-bag_smile';
    case LinearCartCross = 'solar-linear-cart_cross';
    case LinearInfoSquare = 'solar-linear-info_square';
    case LinearFlashlightOn = 'solar-linear-flashlight_on';
    case LinearXXX = 'solar-linear-XXX';
    case LinearFigma = 'solar-linear-Figma';
    case LinearFlashlight = 'solar-linear-Flashlight';
    case LinearGhost = 'solar-linear-Ghost';
    case LinearCupMusic = 'solar-linear-cup_music';
    case LinearBatteryFullMinimalistic = 'solar-linear-battery_full_minimalistic';
    case LinearDangerCircle = 'solar-linear-danger_circle';
    case LinearCheckSquare = 'solar-linear-check_square';
    case LinearGhostSmile = 'solar-linear-ghost_smile';
    case LinearTarget = 'solar-linear-Target';
    case LinearBatteryHalfMinimalistic = 'solar-linear-battery_half_minimalistic';
    case LinearScissors = 'solar-linear-Scissors';
    case LinearPinList = 'solar-linear-pin_list';
    case LinearBatteryCharge = 'solar-linear-battery_charge';
    case LinearUmbrella = 'solar-linear-Umbrella';
    case LinearHomeSmile = 'solar-linear-home_smile';
    case LinearHome = 'solar-linear-Home';
    case LinearCopyright = 'solar-linear-Copyright';
    case LinearHomeWifi = 'solar-linear-home_wifi';
    case LinearTShirt = 'solar-linear-t_shirt';
    case LinearBatteryChargeMinimalistic = 'solar-linear-battery_charge_minimalistic';
    case LinearCupStar = 'solar-linear-cup_star';
    case LinearSpecialEffects = 'solar-linear-special_effects';
    case LinearBody = 'solar-linear-Body';
    case LinearHamburgerMenu = 'solar-linear-hamburger_menu';
    case LinearPower = 'solar-linear-Power';
    case LinearDatabase = 'solar-linear-Database';
    case LinearCursorSquare = 'solar-linear-cursor_square';
    case LinearFuel = 'solar-linear-Fuel';
    case LinearMentionCircle = 'solar-linear-mention_circle';
    case LinearConfettiMinimalistic = 'solar-linear-confetti_minimalistic';
    case LinearMenuDotsCircle = 'solar-linear-menu_dots_circle';
    case LinearPaw = 'solar-linear-Paw';
    case LinearSubtitles = 'solar-linear-Subtitles';
    case LinearSliderVerticalMinimalistic = 'solar-linear-slider_vertical_minimalistic';
    case LinearCrownMinimalistic = 'solar-linear-crown_minimalistic';
    case LinearMenuDots = 'solar-linear-menu_dots';
    case LinearDelivery = 'solar-linear-Delivery';
    case LinearWaterdrop = 'solar-linear-Waterdrop';
    case LinearPerfume = 'solar-linear-Perfume';
    case LinearHomeAngle2 = 'solar-linear-home_angle2';
    case LinearHomeWifiAngle = 'solar-linear-home_wifi_angle';
    case LinearQuestionCircle = 'solar-linear-question_circle';
    case LinearTrashBinMinimalistic = 'solar-linear-trash_bin_minimalistic';
    case LinearMagicStick3 = 'solar-linear-magic_stick3';
    case LinearAddSquare = 'solar-linear-add_square';
    case LinearCrownStar = 'solar-linear-crown_star';
    case LinearMagnet = 'solar-linear-Magnet';
    case LinearConfetti = 'solar-linear-Confetti';
    case LinearPin = 'solar-linear-Pin';
    case LinearMinusSquare = 'solar-linear-minus_square';
    case LinearBolt = 'solar-linear-Bolt';
    case LinearCloseCircle = 'solar-linear-close_circle';
    case LinearForbiddenCircle = 'solar-linear-forbidden_circle';
    case LinearMagicStick2 = 'solar-linear-magic_stick2';
    case LinearCrownLine = 'solar-linear-crown_line';
    case LinearBoltCircle = 'solar-linear-bolt_circle';
    case LinearFlag = 'solar-linear-Flag';
    case LinearSliderHorizontal = 'solar-linear-slider_horizontal';
    case LinearHighDefinition = 'solar-linear-high_definition';
    case LinearCursor = 'solar-linear-Cursor';
    case LinearFeed = 'solar-linear-Feed';
    case LinearTrafficEconomy = 'solar-linear-traffic_economy';
    case LinearAugmentedReality = 'solar-linear-augmented_reality';
    case LinearIcon4K = 'solar-linear-4_k';
    case LinearMagnetWave = 'solar-linear-magnet_wave';
    case LinearHomeSmileAngle = 'solar-linear-home_smile_angle';
    case LinearSliderVertical = 'solar-linear-slider_vertical';
    case LinearCheckCircle = 'solar-linear-check_circle';
    case LinearCopy = 'solar-linear-Copy';
    case LinearDangerSquare = 'solar-linear-danger_square';
    case LinearSkirt = 'solar-linear-Skirt';
    case LinearGlasses = 'solar-linear-Glasses';
    case LinearHomeAdd = 'solar-linear-home_add';
    case LinearSledgehammer = 'solar-linear-Sledgehammer';
    case LinearInfoCircle = 'solar-linear-info_circle';
    case LinearDangerTriangle = 'solar-linear-danger_triangle';
    case LinearPinCircle = 'solar-linear-pin_circle';
    case LinearSmartHome = 'solar-linear-smart_home';
    case LinearScissorsSquare = 'solar-linear-scissors_square';
    case LinearSleeping = 'solar-linear-Sleeping';
    case LinearBox = 'solar-linear-Box';
    case LinearCrown = 'solar-linear-Crown';
    case LinearBroom = 'solar-linear-Broom';
    case LinearPostsCarouselHorizontal = 'solar-linear-posts_carousel_horizontal';
    case LinearFlag2 = 'solar-linear-flag2';
    case LinearPlate = 'solar-linear-Plate';
    case LinearTrashBinTrash = 'solar-linear-trash_bin_trash';
    case LinearCupFirst = 'solar-linear-cup_first';
    case LinearSmartHomeAngle = 'solar-linear-smart_home_angle';
    case LinearPaperBin = 'solar-linear-paper_bin';
    case LinearBoxMinimalistic = 'solar-linear-box_minimalistic';
    case LinearDanger = 'solar-linear-Danger';
    case LinearMenuDotsSquare = 'solar-linear-menu_dots_square';
    case LinearHanger2 = 'solar-linear-hanger2';
    case LinearBatteryHalf = 'solar-linear-battery_half';
    case LinearHome2 = 'solar-linear-home2';
    case LinearPostsCarouselVertical = 'solar-linear-posts_carousel_vertical';
    case LinearRevote = 'solar-linear-Revote';
    case LinearMentionSquare = 'solar-linear-mention_square';
    case LinearWinRar = 'solar-linear-win_rar';
    case LinearForbidden = 'solar-linear-Forbidden';
    case LinearQuestionSquare = 'solar-linear-question_square';
    case LinearHanger = 'solar-linear-Hanger';
    case LinearReorder = 'solar-linear-Reorder';
    case LinearHomeAddAngle = 'solar-linear-home_add_angle';
    case LinearMasks = 'solar-linear-Masks';
    case LinearGift = 'solar-linear-Gift';
    case LinearCreativeCommons = 'solar-linear-creative_commons';
    case LinearSliderMinimalisticHorizontal = 'solar-linear-slider_minimalistic_horizontal';
    case LinearHomeAngle = 'solar-linear-home_angle';
    case LinearBatteryLowMinimalistic = 'solar-linear-battery_low_minimalistic';
    case LinearShare = 'solar-linear-Share';
    case LinearTrashBin2 = 'solar-linear-trash_bin2';
    case LinearSort = 'solar-linear-Sort';
    case LinearMinusCircle = 'solar-linear-minus_circle';
    case LinearExplicit = 'solar-linear-Explicit';
    case LinearTraffic = 'solar-linear-Traffic';
    case LinearFilter = 'solar-linear-Filter';
    case LinearCloseSquare = 'solar-linear-close_square';
    case LinearAddCircle = 'solar-linear-add_circle';
    case LinearFerrisWheel = 'solar-linear-ferris_wheel';
    case LinearCup = 'solar-linear-Cup';
    case LinearBalloon = 'solar-linear-Balloon';
    case LinearHelp = 'solar-linear-Help';
    case LinearBatteryFull = 'solar-linear-battery_full';
    case LinearCat = 'solar-linear-Cat';
    case LinearMaskSad = 'solar-linear-mask_sad';
    case LinearHighQuality = 'solar-linear-high_quality';
    case LinearMagicStick = 'solar-linear-magic_stick';
    case LinearCosmetic = 'solar-linear-Cosmetic';
    case LinearBatteryLow = 'solar-linear-battery_low';
    case LinearShareCircle = 'solar-linear-share_circle';
    case LinearMaskHapply = 'solar-linear-mask_happly';
    case LinearAccessibility = 'solar-linear-Accessibility';
    case LinearTrashBinMinimalistic2 = 'solar-linear-trash_bin_minimalistic2';
    case LinearIncomingCallRounded = 'solar-linear-incoming_call_rounded';
    case LinearCallDropped = 'solar-linear-call_dropped';
    case LinearCallChat = 'solar-linear-call_chat';
    case LinearCallCancelRounded = 'solar-linear-call_cancel_rounded';
    case LinearCallMedicineRounded = 'solar-linear-call_medicine_rounded';
    case LinearCallDroppedRounded = 'solar-linear-call_dropped_rounded';
    case LinearRecordSquare = 'solar-linear-record_square';
    case LinearPhoneCalling = 'solar-linear-phone_calling';
    case LinearPhoneRounded = 'solar-linear-phone_rounded';
    case LinearCallMedicine = 'solar-linear-call_medicine';
    case LinearRecordMinimalistic = 'solar-linear-record_minimalistic';
    case LinearEndCall = 'solar-linear-end_call';
    case LinearOutgoingCall = 'solar-linear-outgoing_call';
    case LinearRecordCircle = 'solar-linear-record_circle';
    case LinearIncomingCall = 'solar-linear-incoming_call';
    case LinearCallChatRounded = 'solar-linear-call_chat_rounded';
    case LinearEndCallRounded = 'solar-linear-end_call_rounded';
    case LinearPhone = 'solar-linear-Phone';
    case LinearOutgoingCallRounded = 'solar-linear-outgoing_call_rounded';
    case LinearCallCancel = 'solar-linear-call_cancel';
    case LinearPhoneCallingRounded = 'solar-linear-phone_calling_rounded';
    case LinearStationMinimalistic = 'solar-linear-station_minimalistic';
    case LinearSidebarCode = 'solar-linear-sidebar_code';
    case LinearWiFiRouterMinimalistic = 'solar-linear-wi_fi_router_minimalistic';
    case LinearUSB = 'solar-linear-USB';
    case LinearSiderbar = 'solar-linear-Siderbar';
    case LinearCode2 = 'solar-linear-code2';
    case LinearSlashCircle = 'solar-linear-slash_circle';
    case LinearScreencast = 'solar-linear-Screencast';
    case LinearHashtagSquare = 'solar-linear-hashtag_square';
    case LinearSidebarMinimalistic = 'solar-linear-sidebar_minimalistic';
    case LinearCode = 'solar-linear-Code';
    case LinearUsbSquare = 'solar-linear-usb_square';
    case LinearWiFiRouter = 'solar-linear-wi_fi_router';
    case LinearCodeCircle = 'solar-linear-code_circle';
    case LinearTranslation = 'solar-linear-Translation';
    case LinearBugMinimalistic = 'solar-linear-bug_minimalistic';
    case LinearStation = 'solar-linear-Station';
    case LinearProgramming = 'solar-linear-Programming';
    case LinearWiFiRouterRound = 'solar-linear-wi_fi_router_round';
    case LinearHashtag = 'solar-linear-Hashtag';
    case LinearBug = 'solar-linear-Bug';
    case LinearHashtagChat = 'solar-linear-hashtag_chat';
    case LinearCommand = 'solar-linear-Command';
    case LinearTranslation2 = 'solar-linear-translation2';
    case LinearHashtagCircle = 'solar-linear-hashtag_circle';
    case LinearScreencast2 = 'solar-linear-screencast2';
    case LinearSlashSquare = 'solar-linear-slash_square';
    case LinearWindowFrame = 'solar-linear-window_frame';
    case LinearStructure = 'solar-linear-Structure';
    case LinearUsbCircle = 'solar-linear-usb_circle';
    case LinearCodeSquare = 'solar-linear-code_square';
    case LinearNotes = 'solar-linear-Notes';
    case LinearDocumentText = 'solar-linear-document_text';
    case LinearDocumentAdd = 'solar-linear-document_add';
    case LinearDocumentMedicine = 'solar-linear-document_medicine';
    case LinearArchiveMinimalistic = 'solar-linear-archive_minimalistic';
    case LinearClipboard = 'solar-linear-Clipboard';
    case LinearClipboardAdd = 'solar-linear-clipboard_add';
    case LinearArchive = 'solar-linear-Archive';
    case LinearClipboardHeart = 'solar-linear-clipboard_heart';
    case LinearClipboardRemove = 'solar-linear-clipboard_remove';
    case LinearClipboardText = 'solar-linear-clipboard_text';
    case LinearDocument = 'solar-linear-Document';
    case LinearNotesMinimalistic = 'solar-linear-notes_minimalistic';
    case LinearArchiveUp = 'solar-linear-archive_up';
    case LinearArchiveUpMinimlistic = 'solar-linear-archive_up_minimlistic';
    case LinearArchiveCheck = 'solar-linear-archive_check';
    case LinearArchiveDown = 'solar-linear-archive_down';
    case LinearArchiveDownMinimlistic = 'solar-linear-archive_down_minimlistic';
    case LinearDocumentsMinimalistic = 'solar-linear-documents_minimalistic';
    case LinearClipboardCheck = 'solar-linear-clipboard_check';
    case LinearClipboardList = 'solar-linear-clipboard_list';
    case LinearDocuments = 'solar-linear-Documents';
    case LinearNotebook = 'solar-linear-Notebook';
    case LinearGalleryRound = 'solar-linear-gallery_round';
    case LinearPlayCircle = 'solar-linear-play_circle';
    case LinearStream = 'solar-linear-Stream';
    case LinearGalleryRemove = 'solar-linear-gallery_remove';
    case LinearClapperboard = 'solar-linear-Clapperboard';
    case LinearPauseCircle = 'solar-linear-pause_circle';
    case LinearRewind5SecondsBack = 'solar-linear-rewind5_seconds_back';
    case LinearRepeat = 'solar-linear-Repeat';
    case LinearClapperboardEdit = 'solar-linear-clapperboard_edit';
    case LinearVideoFrameCut = 'solar-linear-video_frame_cut';
    case LinearPanorama = 'solar-linear-Panorama';
    case LinearPlayStream = 'solar-linear-play_stream';
    case LinearClapperboardOpen = 'solar-linear-clapperboard_open';
    case LinearClapperboardText = 'solar-linear-clapperboard_text';
    case LinearLibrary = 'solar-linear-Library';
    case LinearReel2 = 'solar-linear-reel2';
    case LinearVolumeSmall = 'solar-linear-volume_small';
    case LinearVideoFrame = 'solar-linear-video_frame';
    case LinearMicrophoneLarge = 'solar-linear-microphone_large';
    case LinearRewindForward = 'solar-linear-rewind_forward';
    case LinearRewindBackCircle = 'solar-linear-rewind_back_circle';
    case LinearMicrophone = 'solar-linear-Microphone';
    case LinearVideoFrameReplace = 'solar-linear-video_frame_replace';
    case LinearClapperboardPlay = 'solar-linear-clapperboard_play';
    case LinearGalleryDownload = 'solar-linear-gallery_download';
    case LinearMusicNote4 = 'solar-linear-music_note4';
    case LinearVideocameraRecord = 'solar-linear-videocamera_record';
    case LinearPlaybackSpeed = 'solar-linear-playback_speed';
    case LinearSoundwave = 'solar-linear-Soundwave';
    case LinearStopCircle = 'solar-linear-stop_circle';
    case LinearQuitFullScreenCircle = 'solar-linear-quit_full_screen_circle';
    case LinearRewindBack = 'solar-linear-rewind_back';
    case LinearRepeatOne = 'solar-linear-repeat_one';
    case LinearGalleryCheck = 'solar-linear-gallery_check';
    case LinearWallpaper = 'solar-linear-Wallpaper';
    case LinearRewindForwardCircle = 'solar-linear-rewind_forward_circle';
    case LinearGalleryEdit = 'solar-linear-gallery_edit';
    case LinearGallery = 'solar-linear-Gallery';
    case LinearGalleryMinimalistic = 'solar-linear-gallery_minimalistic';
    case LinearUploadTrack = 'solar-linear-upload_track';
    case LinearVolume = 'solar-linear-Volume';
    case LinearUploadTrack2 = 'solar-linear-upload_track2';
    case LinearMusicNotes = 'solar-linear-music_notes';
    case LinearMusicNote2 = 'solar-linear-music_note2';
    case LinearCameraAdd = 'solar-linear-camera_add';
    case LinearPodcast = 'solar-linear-Podcast';
    case LinearCameraRotate = 'solar-linear-camera_rotate';
    case LinearMusicNote3 = 'solar-linear-music_note3';
    case LinearStop = 'solar-linear-Stop';
    case LinearMuted = 'solar-linear-Muted';
    case LinearSkipNext = 'solar-linear-skip_next';
    case LinearGallerySend = 'solar-linear-gallery_send';
    case LinearRecord = 'solar-linear-Record';
    case LinearFullScreenCircle = 'solar-linear-full_screen_circle';
    case LinearVolumeCross = 'solar-linear-volume_cross';
    case LinearSoundwaveCircle = 'solar-linear-soundwave_circle';
    case LinearSkipPrevious = 'solar-linear-skip_previous';
    case LinearRewind5SecondsForward = 'solar-linear-rewind5_seconds_forward';
    case LinearPlay = 'solar-linear-Play';
    case LinearPIP = 'solar-linear-PIP';
    case LinearMusicLibrary = 'solar-linear-music_library';
    case LinearVideoFrame2 = 'solar-linear-video_frame2';
    case LinearCamera = 'solar-linear-Camera';
    case LinearQuitPip = 'solar-linear-quit_pip';
    case LinearClapperboardOpenPlay = 'solar-linear-clapperboard_open_play';
    case LinearRewind10SecondsBack = 'solar-linear-rewind10_seconds_back';
    case LinearRepeatOneMinimalistic = 'solar-linear-repeat_one_minimalistic';
    case LinearVinyl = 'solar-linear-Vinyl';
    case LinearVideoLibrary = 'solar-linear-video_library';
    case LinearGalleryWide = 'solar-linear-gallery_wide';
    case LinearReel = 'solar-linear-Reel';
    case LinearToPip = 'solar-linear-to_pip';
    case LinearPip2 = 'solar-linear-pip2';
    case LinearFullScreen = 'solar-linear-full_screen';
    case LinearCameraMinimalistic = 'solar-linear-camera_minimalistic';
    case LinearVideoFrameCut2 = 'solar-linear-video_frame_cut2';
    case LinearGalleryCircle = 'solar-linear-gallery_circle';
    case LinearVideoFramePlayHorizontal = 'solar-linear-video_frame_play_horizontal';
    case LinearMusicNoteSlider2 = 'solar-linear-music_note_slider2';
    case LinearMusicNoteSlider = 'solar-linear-music_note_slider';
    case LinearVideocameraAdd = 'solar-linear-videocamera_add';
    case LinearQuitFullScreenSquare = 'solar-linear-quit_full_screen_square';
    case LinearAlbum = 'solar-linear-Album';
    case LinearGalleryAdd = 'solar-linear-gallery_add';
    case LinearCameraSquare = 'solar-linear-camera_square';
    case LinearRewind15SecondsBack = 'solar-linear-rewind15_seconds_back';
    case LinearRewind15SecondsForward = 'solar-linear-rewind15_seconds_forward';
    case LinearVinylRecord = 'solar-linear-vinyl_record';
    case LinearShuffle = 'solar-linear-Shuffle';
    case LinearPause = 'solar-linear-Pause';
    case LinearMusicNote = 'solar-linear-music_note';
    case LinearQuitFullScreen = 'solar-linear-quit_full_screen';
    case LinearMicrophone2 = 'solar-linear-microphone2';
    case LinearVideocamera = 'solar-linear-Videocamera';
    case LinearGalleryFavourite = 'solar-linear-gallery_favourite';
    case LinearMusicLibrary2 = 'solar-linear-music_library2';
    case LinearVideoFramePlayVertical = 'solar-linear-video_frame_play_vertical';
    case LinearFullScreenSquare = 'solar-linear-full_screen_square';
    case LinearRewind10SecondsForward = 'solar-linear-rewind10_seconds_forward';
    case LinearVolumeLoud = 'solar-linear-volume_loud';
    case LinearMicrophone3 = 'solar-linear-microphone3';
    case LinearSoundwaveSquare = 'solar-linear-soundwave_square';
    case LinearCardholder = 'solar-linear-Cardholder';
    case LinearBillList = 'solar-linear-bill_list';
    case LinearSaleSquare = 'solar-linear-sale_square';
    case LinearDollar = 'solar-linear-Dollar';
    case LinearTicket = 'solar-linear-Ticket';
    case LinearTag = 'solar-linear-Tag';
    case LinearCashOut = 'solar-linear-cash_out';
    case LinearWallet2 = 'solar-linear-wallet2';
    case LinearRuble = 'solar-linear-Ruble';
    case LinearCardTransfer = 'solar-linear-card_transfer';
    case LinearEuro = 'solar-linear-Euro';
    case LinearSale = 'solar-linear-Sale';
    case LinearCardSearch = 'solar-linear-card_search';
    case LinearWallet = 'solar-linear-Wallet';
    case LinearBillCross = 'solar-linear-bill_cross';
    case LinearTicketSale = 'solar-linear-ticket_sale';
    case LinearSafeSquare = 'solar-linear-safe_square';
    case LinearCard = 'solar-linear-Card';
    case LinearSafe2 = 'solar-linear-safe2';
    case LinearDollarMinimalistic = 'solar-linear-dollar_minimalistic';
    case LinearTagPrice = 'solar-linear-tag_price';
    case LinearMoneyBag = 'solar-linear-money_bag';
    case LinearBill = 'solar-linear-Bill';
    case LinearCardSend = 'solar-linear-card_send';
    case LinearCardRecive = 'solar-linear-card_recive';
    case LinearBanknote2 = 'solar-linear-banknote2';
    case LinearTagHorizontal = 'solar-linear-tag_horizontal';
    case LinearBillCheck = 'solar-linear-bill_check';
    case LinearTickerStar = 'solar-linear-ticker_star';
    case LinearBanknote = 'solar-linear-Banknote';
    case LinearVerifiedCheck = 'solar-linear-verified_check';
    case LinearWadOfMoney = 'solar-linear-wad_of_money';
    case LinearCard2 = 'solar-linear-card2';
    case LinearSafeCircle = 'solar-linear-safe_circle';
    case LinearWalletMoney = 'solar-linear-wallet_money';
    case LinearList = 'solar-linear-List';
    case LinearListDownMinimalistic = 'solar-linear-list_down_minimalistic';
    case LinearPlaylist2 = 'solar-linear-playlist2';
    case LinearChecklistMinimalistic = 'solar-linear-checklist_minimalistic';
    case LinearPlaaylistMinimalistic = 'solar-linear-plaaylist_minimalistic';
    case LinearListHeart = 'solar-linear-list_heart';
    case LinearListArrowDown = 'solar-linear-list_arrow_down';
    case LinearListArrowUp = 'solar-linear-list_arrow_up';
    case LinearListUpMinimalistic = 'solar-linear-list_up_minimalistic';
    case LinearPlaylist = 'solar-linear-Playlist';
    case LinearListUp = 'solar-linear-list_up';
    case LinearListCrossMinimalistic = 'solar-linear-list_cross_minimalistic';
    case LinearListCross = 'solar-linear-list_cross';
    case LinearListArrowDownMinimalistic = 'solar-linear-list_arrow_down_minimalistic';
    case LinearSortByAlphabet = 'solar-linear-sort_by_alphabet';
    case LinearChecklist = 'solar-linear-Checklist';
    case LinearSortFromBottomToTop = 'solar-linear-sort_from_bottom_to_top';
    case LinearListCheck = 'solar-linear-list_check';
    case LinearPlaylistMinimalistic2 = 'solar-linear-playlist_minimalistic2';
    case LinearPlaylistMinimalistic3 = 'solar-linear-playlist_minimalistic3';
    case LinearList1 = 'solar-linear-list1';
    case LinearSortFromTopToBottom = 'solar-linear-sort_from_top_to_bottom';
    case LinearSortByTime = 'solar-linear-sort_by_time';
    case LinearListDown = 'solar-linear-list_down';
    case LinearListHeartMinimalistic = 'solar-linear-list_heart_minimalistic';
    case LinearListCheckMinimalistic = 'solar-linear-list_check_minimalistic';
    case LinearListArrowUpMinimalistic = 'solar-linear-list_arrow_up_minimalistic';
    case LinearUserCrossRounded = 'solar-linear-user_cross_rounded';
    case LinearUser = 'solar-linear-User';
    case LinearUsersGroupRounded = 'solar-linear-users_group_rounded';
    case LinearUserPlusRounded = 'solar-linear-user_plus_rounded';
    case LinearUserBlock = 'solar-linear-user_block';
    case LinearUserMinus = 'solar-linear-user_minus';
    case LinearUserHands = 'solar-linear-user_hands';
    case LinearUserHeart = 'solar-linear-user_heart';
    case LinearUserMinusRounded = 'solar-linear-user_minus_rounded';
    case LinearUserCross = 'solar-linear-user_cross';
    case LinearUserSpeakRounded = 'solar-linear-user_speak_rounded';
    case LinearUserId = 'solar-linear-user_id';
    case LinearUserBlockRounded = 'solar-linear-user_block_rounded';
    case LinearUserHeartRounded = 'solar-linear-user_heart_rounded';
    case LinearUsersGroupTwoRounded = 'solar-linear-users_group_two_rounded';
    case LinearUserHandUp = 'solar-linear-user_hand_up';
    case LinearUserCircle = 'solar-linear-user_circle';
    case LinearUserRounded = 'solar-linear-user_rounded';
    case LinearUserCheck = 'solar-linear-user_check';
    case LinearUserPlus = 'solar-linear-user_plus';
    case LinearUserCheckRounded = 'solar-linear-user_check_rounded';
    case LinearUserSpeak = 'solar-linear-user_speak';
    case LinearVirus = 'solar-linear-Virus';
    case LinearAdhesivePlaster2 = 'solar-linear-adhesive_plaster2';
    case LinearDropper = 'solar-linear-Dropper';
    case LinearPulse2 = 'solar-linear-pulse2';
    case LinearBoneBroken = 'solar-linear-bone_broken';
    case LinearHeartPulse2 = 'solar-linear-heart_pulse2';
    case LinearMedicalKit = 'solar-linear-medical_kit';
    case LinearTestTube = 'solar-linear-test_tube';
    case LinearHealth = 'solar-linear-Health';
    case LinearDropperMinimalistic2 = 'solar-linear-dropper_minimalistic2';
    case LinearDNA = 'solar-linear-DNA';
    case LinearDropper3 = 'solar-linear-dropper3';
    case LinearThermometer = 'solar-linear-Thermometer';
    case LinearDropper2 = 'solar-linear-dropper2';
    case LinearJarOfPills2 = 'solar-linear-jar_of_pills2';
    case LinearBoneCrack = 'solar-linear-bone_crack';
    case LinearJarOfPills = 'solar-linear-jar_of_pills';
    case LinearSyringe = 'solar-linear-Syringe';
    case LinearStethoscope = 'solar-linear-Stethoscope';
    case LinearBenzeneRing = 'solar-linear-benzene_ring';
    case LinearBacteria = 'solar-linear-Bacteria';
    case LinearAdhesivePlaster = 'solar-linear-adhesive_plaster';
    case LinearBone = 'solar-linear-Bone';
    case LinearBones = 'solar-linear-Bones';
    case LinearPill = 'solar-linear-Pill';
    case LinearPills = 'solar-linear-Pills';
    case LinearHeartPulse = 'solar-linear-heart_pulse';
    case LinearTestTubeMinimalistic = 'solar-linear-test_tube_minimalistic';
    case LinearPills2 = 'solar-linear-pills2';
    case LinearPulse = 'solar-linear-Pulse';
    case LinearDropperMinimalistic = 'solar-linear-dropper_minimalistic';
    case LinearPills3 = 'solar-linear-pills3';
    case LinearWhisk = 'solar-linear-Whisk';
    case LinearBottle = 'solar-linear-Bottle';
    case LinearOvenMittsMinimalistic = 'solar-linear-oven_mitts_minimalistic';
    case LinearChefHatMinimalistic = 'solar-linear-chef_hat_minimalistic';
    case LinearTeaCup = 'solar-linear-tea_cup';
    case LinearWineglassTriangle = 'solar-linear-wineglass_triangle';
    case LinearOvenMitts = 'solar-linear-oven_mitts';
    case LinearCupPaper = 'solar-linear-cup_paper';
    case LinearLadle = 'solar-linear-Ladle';
    case LinearCorkscrew = 'solar-linear-Corkscrew';
    case LinearDonutBitten = 'solar-linear-donut_bitten';
    case LinearWineglass = 'solar-linear-Wineglass';
    case LinearDonut = 'solar-linear-Donut';
    case LinearCupHot = 'solar-linear-cup_hot';
    case LinearChefHatHeart = 'solar-linear-chef_hat_heart';
    case LinearChefHat = 'solar-linear-chef_hat';
    case LinearRollingPin = 'solar-linear-rolling_pin';
    case LinearCodeFile = 'solar-linear-code_file';
    case LinearFileCorrupted = 'solar-linear-file_corrupted';
    case LinearFile = 'solar-linear-File';
    case LinearFileRight = 'solar-linear-file_right';
    case LinearFileFavourite = 'solar-linear-file_favourite';
    case LinearFileDownload = 'solar-linear-file_download';
    case LinearZipFile = 'solar-linear-zip_file';
    case LinearFileText = 'solar-linear-file_text';
    case LinearFileSmile = 'solar-linear-file_smile_)';
    case LinearFileCheck = 'solar-linear-file_check';
    case LinearFileSend = 'solar-linear-file_send';
    case LinearFileLeft = 'solar-linear-file_left';
    case LinearFigmaFile = 'solar-linear-figma_file';
    case LinearFileRemove = 'solar-linear-file_remove';
    case LinearCloudFile = 'solar-linear-cloud_file';
    case LinearSuspension = 'solar-linear-Suspension';
    case LinearSpedometerMax = 'solar-linear-spedometer_max';
    case LinearTransmissionCircle = 'solar-linear-transmission_circle';
    case LinearGasStation = 'solar-linear-gas_station';
    case LinearWheel = 'solar-linear-Wheel';
    case LinearTransmission = 'solar-linear-Transmission';
    case LinearKickScooter = 'solar-linear-kick_scooter';
    case LinearSpedometerLow = 'solar-linear-spedometer_low';
    case LinearSpedometerMiddle = 'solar-linear-spedometer_middle';
    case LinearWheelAngle = 'solar-linear-wheel_angle';
    case LinearTram = 'solar-linear-Tram';
    case LinearTransmissionSquare = 'solar-linear-transmission_square';
    case LinearScooter = 'solar-linear-Scooter';
    case LinearShockAbsorber = 'solar-linear-shock_absorber';
    case LinearBus = 'solar-linear-Bus';
    case LinearSuspensionCross = 'solar-linear-suspension_cross';
    case LinearSuspensionBolt = 'solar-linear-suspension_bolt';
    case LinearElectricRefueling = 'solar-linear-electric_refueling';
    case LinearAccumulator = 'solar-linear-Accumulator';
    case LinearHandPills = 'solar-linear-hand_pills';
    case LinearHandMoney = 'solar-linear-hand_money';
    case LinearHandShake = 'solar-linear-hand_shake';
    case LinearHandHeart = 'solar-linear-hand_heart';
    case LinearHandStars = 'solar-linear-hand_stars';
    case LinearRemoveFolder = 'solar-linear-remove_folder';
    case LinearFolderFavouritestar = 'solar-linear-folder_favourite(star)';
    case LinearAddFolder = 'solar-linear-add_folder';
    case LinearFolderCheck = 'solar-linear-folder_check';
    case LinearFolderFavouritebookmark = 'solar-linear-folder_favourite(bookmark)';
    case LinearFolder2 = 'solar-linear-folder2';
    case LinearFolderSecurity = 'solar-linear-folder_security';
    case LinearFolderCloud = 'solar-linear-folder_cloud';
    case LinearMoveToFolder = 'solar-linear-move_to_folder';
    case LinearFolderError = 'solar-linear-folder_error';
    case LinearFolderPathConnect = 'solar-linear-folder_path_connect';
    case LinearFolderOpen = 'solar-linear-folder_open';
    case LinearFolder = 'solar-linear-Folder';
    case LinearFolderWithFiles = 'solar-linear-folder_with_files';
    case LinearCloudCheck = 'solar-linear-cloud_check';
    case LinearTemperature = 'solar-linear-Temperature';
    case LinearWind = 'solar-linear-Wind';
    case LinearCloudSnowfall = 'solar-linear-cloud_snowfall';
    case LinearSunrise = 'solar-linear-Sunrise';
    case LinearSun2 = 'solar-linear-sun2';
    case LinearCloudSun = 'solar-linear-cloud_sun';
    case LinearCloudBoltMinimalistic = 'solar-linear-cloud_bolt_minimalistic';
    case LinearCloudDownload = 'solar-linear-cloud_download';
    case LinearClouds = 'solar-linear-Clouds';
    case LinearTornado = 'solar-linear-Tornado';
    case LinearMoonSleep = 'solar-linear-moon_sleep';
    case LinearCloudUpload = 'solar-linear-cloud_upload';
    case LinearCloudRain = 'solar-linear-cloud_rain';
    case LinearFog = 'solar-linear-Fog';
    case LinearSnowflake = 'solar-linear-Snowflake';
    case LinearMoonFog = 'solar-linear-moon_fog';
    case LinearCloudMinus = 'solar-linear-cloud_minus';
    case LinearCloudBolt = 'solar-linear-cloud_bolt';
    case LinearCloudWaterdrop = 'solar-linear-cloud_waterdrop';
    case LinearSunset = 'solar-linear-Sunset';
    case LinearWaterdrops = 'solar-linear-Waterdrops';
    case LinearMoonStars = 'solar-linear-moon_stars';
    case LinearCloudPlus = 'solar-linear-cloud_plus';
    case LinearSun = 'solar-linear-Sun';
    case LinearCloudWaterdrops = 'solar-linear-cloud_waterdrops';
    case LinearCloudSun2 = 'solar-linear-cloud_sun2';
    case LinearCloudyMoon = 'solar-linear-cloudy_moon';
    case LinearTornadoSmall = 'solar-linear-tornado_small';
    case LinearCloud = 'solar-linear-Cloud';
    case LinearSunFog = 'solar-linear-sun_fog';
    case LinearCloundCross = 'solar-linear-clound_cross';
    case LinearCloudSnowfallMinimalistic = 'solar-linear-cloud_snowfall_minimalistic';
    case LinearCloudStorm = 'solar-linear-cloud_storm';
    case LinearMoon = 'solar-linear-Moon';
    case LinearRefreshCircle = 'solar-linear-refresh_circle';
    case LinearSquareArrowRightDown = 'solar-linear-square_arrow_right_down';
    case LinearRoundArrowLeftDown = 'solar-linear-round_arrow_left_down';
    case LinearRestart = 'solar-linear-Restart';
    case LinearRoundAltArrowDown = 'solar-linear-round_alt_arrow_down';
    case LinearRoundSortVertical = 'solar-linear-round_sort_vertical';
    case LinearSquareAltArrowUp = 'solar-linear-square_alt_arrow_up';
    case LinearArrowLeftUp = 'solar-linear-arrow_left_up';
    case LinearSortHorizontal = 'solar-linear-sort_horizontal';
    case LinearTransferHorizontal = 'solar-linear-transfer_horizontal';
    case LinearSquareDoubleAltArrowUp = 'solar-linear-square_double_alt_arrow_up';
    case LinearRoundArrowLeftUp = 'solar-linear-round_arrow_left_up';
    case LinearAltArrowRight = 'solar-linear-alt_arrow_right';
    case LinearRoundDoubleAltArrowUp = 'solar-linear-round_double_alt_arrow_up';
    case LinearRestartCircle = 'solar-linear-restart_circle';
    case LinearSquareArrowDown = 'solar-linear-square_arrow_down';
    case LinearSortVertical = 'solar-linear-sort_vertical';
    case LinearSquareSortHorizontal = 'solar-linear-square_sort_horizontal';
    case LinearDoubleAltArrowLeft = 'solar-linear-double_alt_arrow_left';
    case LinearSquareAltArrowDown = 'solar-linear-square_alt_arrow_down';
    case LinearSquareAltArrowRight = 'solar-linear-square_alt_arrow_right';
    case LinearSquareArrowUp = 'solar-linear-square_arrow_up';
    case LinearDoubleAltArrowRight = 'solar-linear-double_alt_arrow_right';
    case LinearRoundTransferVertical = 'solar-linear-round_transfer_vertical';
    case LinearArrowLeft = 'solar-linear-arrow_left';
    case LinearRoundDoubleAltArrowRight = 'solar-linear-round_double_alt_arrow_right';
    case LinearSquareDoubleAltArrowLeft = 'solar-linear-square_double_alt_arrow_left';
    case LinearAltArrowDown = 'solar-linear-alt_arrow_down';
    case LinearRoundTransferHorizontal = 'solar-linear-round_transfer_horizontal';
    case LinearRoundArrowRightDown = 'solar-linear-round_arrow_right_down';
    case LinearArrowUp = 'solar-linear-arrow_up';
    case LinearRoundArrowLeft = 'solar-linear-round_arrow_left';
    case LinearDoubleAltArrowUp = 'solar-linear-double_alt_arrow_up';
    case LinearRoundArrowRight = 'solar-linear-round_arrow_right';
    case LinearSquareTransferHorizontal = 'solar-linear-square_transfer_horizontal';
    case LinearArrowRight = 'solar-linear-arrow_right';
    case LinearRoundDoubleAltArrowLeft = 'solar-linear-round_double_alt_arrow_left';
    case LinearRoundArrowUp = 'solar-linear-round_arrow_up';
    case LinearSquareSortVertical = 'solar-linear-square_sort_vertical';
    case LinearAltArrowLeft = 'solar-linear-alt_arrow_left';
    case LinearSquareDoubleAltArrowRight = 'solar-linear-square_double_alt_arrow_right';
    case LinearRefresh = 'solar-linear-Refresh';
    case LinearTransferVertical = 'solar-linear-transfer_vertical';
    case LinearRefreshSquare = 'solar-linear-refresh_square';
    case LinearSquareTransferVertical = 'solar-linear-square_transfer_vertical';
    case LinearSquareDoubleAltArrowDown = 'solar-linear-square_double_alt_arrow_down';
    case LinearRoundArrowRightUp = 'solar-linear-round_arrow_right_up';
    case LinearArrowDown = 'solar-linear-arrow_down';
    case LinearRestartSquare = 'solar-linear-restart_square';
    case LinearSquareArrowRight = 'solar-linear-square_arrow_right';
    case LinearRoundDoubleAltArrowDown = 'solar-linear-round_double_alt_arrow_down';
    case LinearSquareArrowLeftUp = 'solar-linear-square_arrow_left_up';
    case LinearRoundArrowDown = 'solar-linear-round_arrow_down';
    case LinearSquareArrowRightUp = 'solar-linear-square_arrow_right_up';
    case LinearRoundTransferDiagonal = 'solar-linear-round_transfer_diagonal';
    case LinearArrowRightDown = 'solar-linear-arrow_right_down';
    case LinearArrowLeftDown = 'solar-linear-arrow_left_down';
    case LinearRoundAltArrowLeft = 'solar-linear-round_alt_arrow_left';
    case LinearArrowRightUp = 'solar-linear-arrow_right_up';
    case LinearSquareArrowLeftDown = 'solar-linear-square_arrow_left_down';
    case LinearRoundAltArrowUp = 'solar-linear-round_alt_arrow_up';
    case LinearAltArrowUp = 'solar-linear-alt_arrow_up';
    case LinearSquareAltArrowLeft = 'solar-linear-square_alt_arrow_left';
    case LinearRoundSortHorizontal = 'solar-linear-round_sort_horizontal';
    case LinearDoubleAltArrowDown = 'solar-linear-double_alt_arrow_down';
    case LinearRoundAltArrowRight = 'solar-linear-round_alt_arrow_right';
    case LinearSquareArrowLeft = 'solar-linear-square_arrow_left';
    case LinearTuningSquare2 = 'solar-linear-tuning_square2';
    case LinearWidgetAdd = 'solar-linear-widget_add';
    case LinearTuningSquare = 'solar-linear-tuning_square';
    case LinearSettingsMinimalistic = 'solar-linear-settings_minimalistic';
    case LinearWidget6 = 'solar-linear-widget6';
    case LinearWidget4 = 'solar-linear-widget4';
    case LinearSettings = 'solar-linear-Settings';
    case LinearWidget5 = 'solar-linear-widget5';
    case LinearWidget2 = 'solar-linear-widget2';
    case LinearWidget3 = 'solar-linear-widget3';
    case LinearTuning2 = 'solar-linear-tuning2';
    case LinearTuning3 = 'solar-linear-tuning3';
    case LinearWidget = 'solar-linear-Widget';
    case LinearTuning4 = 'solar-linear-tuning4';
    case LinearTuning = 'solar-linear-Tuning';
    case LinearDiagramDown = 'solar-linear-diagram_down';
    case LinearChart2 = 'solar-linear-chart2';
    case LinearChart = 'solar-linear-Chart';
    case LinearDiagramUp = 'solar-linear-diagram_up';
    case LinearGraphNew = 'solar-linear-graph_new';
    case LinearCourseUp = 'solar-linear-course_up';
    case LinearGraphDownNew = 'solar-linear-graph_down_new';
    case LinearPieChart3 = 'solar-linear-pie_chart3';
    case LinearPieChart2 = 'solar-linear-pie_chart2';
    case LinearGraphNewUp = 'solar-linear-graph_new_up';
    case LinearPieChart = 'solar-linear-pie_chart';
    case LinearRoundGraph = 'solar-linear-round_graph';
    case LinearGraphUp = 'solar-linear-graph_up';
    case LinearChartSquare = 'solar-linear-chart_square';
    case LinearCourseDown = 'solar-linear-course_down';
    case LinearChatSquare2 = 'solar-linear-chat_square2';
    case LinearGraphDown = 'solar-linear-graph_down';
    case LinearGraph = 'solar-linear-Graph';
    case LinearPresentationGraph = 'solar-linear-presentation_graph';
    case LinearMaximizeSquare3 = 'solar-linear-maximize_square3';
    case LinearMaximizeSquareMinimalistic = 'solar-linear-maximize_square_minimalistic';
    case LinearMaximizeSquare2 = 'solar-linear-maximize_square2';
    case LinearMinimizeSquare = 'solar-linear-minimize_square';
    case LinearDownloadSquare = 'solar-linear-download_square';
    case LinearUndoLeftRoundSquare = 'solar-linear-undo_left_round_square';
    case LinearReply = 'solar-linear-Reply';
    case LinearLogout = 'solar-linear-Logout';
    case LinearReciveSquare = 'solar-linear-recive_square';
    case LinearExport = 'solar-linear-Export';
    case LinearSendTwiceSquare = 'solar-linear-send_twice_square';
    case LinearUndoLeftRound = 'solar-linear-undo_left_round';
    case LinearForward2 = 'solar-linear-forward2';
    case LinearMaximize = 'solar-linear-Maximize';
    case LinearUndoRightRound = 'solar-linear-undo_right_round';
    case LinearMinimizeSquare2 = 'solar-linear-minimize_square2';
    case LinearMinimizeSquare3 = 'solar-linear-minimize_square3';
    case LinearUploadTwiceSquare = 'solar-linear-upload_twice_square';
    case LinearMinimize = 'solar-linear-Minimize';
    case LinearCircleTopUp = 'solar-linear-circle_top_up';
    case LinearUploadMinimalistic = 'solar-linear-upload_minimalistic';
    case LinearDownload = 'solar-linear-Download';
    case LinearImport = 'solar-linear-Import';
    case LinearLogin = 'solar-linear-Login';
    case LinearUndoLeft = 'solar-linear-undo_left';
    case LinearSquareTopUp = 'solar-linear-square_top_up';
    case LinearDownloadTwiceSquare = 'solar-linear-download_twice_square';
    case LinearCircleBottomDown = 'solar-linear-circle_bottom_down';
    case LinearMaximizeSquare = 'solar-linear-maximize_square';
    case LinearUploadSquare = 'solar-linear-upload_square';
    case LinearUndoRightSquare = 'solar-linear-undo_right_square';
    case LinearReciveTwiceSquare = 'solar-linear-recive_twice_square';
    case LinearCircleTopDown = 'solar-linear-circle_top_down';
    case LinearArrowToDownLeft = 'solar-linear-arrow_to_down_left';
    case LinearLogout2 = 'solar-linear-logout2';
    case LinearLogout3 = 'solar-linear-logout3';
    case LinearScale = 'solar-linear-Scale';
    case LinearArrowToDownRight = 'solar-linear-arrow_to_down_right';
    case LinearDownloadMinimalistic = 'solar-linear-download_minimalistic';
    case LinearMinimizeSquareMinimalistic = 'solar-linear-minimize_square_minimalistic';
    case LinearReply2 = 'solar-linear-reply2';
    case LinearSquareBottomUp = 'solar-linear-square_bottom_up';
    case LinearUndoRight = 'solar-linear-undo_right';
    case LinearUndoLeftSquare = 'solar-linear-undo_left_square';
    case LinearSendSquare = 'solar-linear-send_square';
    case LinearExit = 'solar-linear-Exit';
    case LinearSquareBottomDown = 'solar-linear-square_bottom_down';
    case LinearUndoRightRoundSquare = 'solar-linear-undo_right_round_square';
    case LinearArrowToTopLeft = 'solar-linear-arrow_to_top_left';
    case LinearCircleBottomUp = 'solar-linear-circle_bottom_up';
    case LinearScreenShare = 'solar-linear-screen_share';
    case LinearUpload = 'solar-linear-Upload';
    case LinearSquareTopDown = 'solar-linear-square_top_down';
    case LinearArrowToTopRight = 'solar-linear-arrow_to_top_right';
    case LinearLogin3 = 'solar-linear-login3';
    case LinearLogin2 = 'solar-linear-login2';
    case LinearCity = 'solar-linear-City';
    case LinearBuildings = 'solar-linear-Buildings';
    case LinearBuildings3 = 'solar-linear-buildings3';
    case LinearBuildings2 = 'solar-linear-buildings2';
    case LinearHospital = 'solar-linear-Hospital';
    case LinearGarage = 'solar-linear-Garage';
    case LinearPassport = 'solar-linear-Passport';
    case LinearDiplomaVerified = 'solar-linear-diploma_verified';
    case LinearCaseRound = 'solar-linear-case_round';
    case LinearBackpack = 'solar-linear-Backpack';
    case LinearBook2 = 'solar-linear-book2';
    case LinearSquareAcademicCap2 = 'solar-linear-square_academic_cap2';
    case LinearCaseRoundMinimalistic = 'solar-linear-case_round_minimalistic';
    case LinearCase = 'solar-linear-Case';
    case LinearBookBookmarkMinimalistic = 'solar-linear-book_bookmark_minimalistic';
    case LinearBookmarkOpened = 'solar-linear-bookmark_opened';
    case LinearDiploma = 'solar-linear-Diploma';
    case LinearBook = 'solar-linear-Book';
    case LinearSquareAcademicCap = 'solar-linear-square_academic_cap';
    case LinearBookmarkCircle = 'solar-linear-bookmark_circle';
    case LinearCalculatorMinimalistic = 'solar-linear-calculator_minimalistic';
    case LinearNotebookSquare = 'solar-linear-notebook_square';
    case LinearBookMinimalistic = 'solar-linear-book_minimalistic';
    case LinearCaseMinimalistic = 'solar-linear-case_minimalistic';
    case LinearNotebookBookmark = 'solar-linear-notebook_bookmark';
    case LinearPassportMinimalistic = 'solar-linear-passport_minimalistic';
    case LinearBookBookmark = 'solar-linear-book_bookmark';
    case LinearBookmarkSquareMinimalistic = 'solar-linear-bookmark_square_minimalistic';
    case LinearBookmark = 'solar-linear-Bookmark';
    case LinearPlusMinus = 'solar-linear-plus,_minus';
    case LinearCalculator = 'solar-linear-Calculator';
    case LinearBookmarkSquare = 'solar-linear-bookmark_square';
    case LinearNotebookMinimalistic = 'solar-linear-notebook_minimalistic';
    case LinearFireSquare = 'solar-linear-fire_square';
    case LinearSuitcaseLines = 'solar-linear-suitcase_lines';
    case LinearFire = 'solar-linear-Fire';
    case LinearBonfire = 'solar-linear-Bonfire';
    case LinearSuitcaseTag = 'solar-linear-suitcase_tag';
    case LinearLeaf = 'solar-linear-Leaf';
    case LinearSuitcase = 'solar-linear-Suitcase';
    case LinearFlame = 'solar-linear-Flame';
    case LinearFireMinimalistic = 'solar-linear-fire_minimalistic';
    case LinearBellBing = 'solar-linear-bell_bing';
    case LinearNotificationLinesRemove = 'solar-linear-notification_lines_remove';
    case LinearNotificationUnread = 'solar-linear-notification_unread';
    case LinearBell = 'solar-linear-Bell';
    case LinearNotificationRemove = 'solar-linear-notification_remove';
    case LinearNotificationUnreadLines = 'solar-linear-notification_unread_lines';
    case LinearBellOff = 'solar-linear-bell_off';
    case LinearLightning = 'solar-linear-Lightning';
    case LinearLightbulbMinimalistic = 'solar-linear-lightbulb_minimalistic';
    case LinearServerSquareCloud = 'solar-linear-server_square_cloud';
    case LinearLightbulbBolt = 'solar-linear-lightbulb_bolt';
    case LinearAirbudsCharge = 'solar-linear-airbuds_charge';
    case LinearServerPath = 'solar-linear-server_path';
    case LinearSimCardMinimalistic = 'solar-linear-sim_card_minimalistic';
    case LinearSmartphone = 'solar-linear-Smartphone';
    case LinearTurntable = 'solar-linear-Turntable';
    case LinearAirbudsCheck = 'solar-linear-airbuds_check';
    case LinearMouseMinimalistic = 'solar-linear-mouse_minimalistic';
    case LinearSmartphoneRotateAngle = 'solar-linear-smartphone_rotate_angle';
    case LinearRadioMinimalistic = 'solar-linear-radio_minimalistic';
    case LinearAirbuds = 'solar-linear-Airbuds';
    case LinearSmartphoneRotateOrientation = 'solar-linear-smartphone_rotate_orientation';
    case LinearIPhone = 'solar-linear-i_phone';
    case LinearSimCard = 'solar-linear-sim_card';
    case LinearFlashDrive = 'solar-linear-flash_drive';
    case LinearDevices = 'solar-linear-Devices';
    case LinearSimCards = 'solar-linear-sim_cards';
    case LinearAirbudsCaseOpen = 'solar-linear-airbuds_case_open';
    case LinearTurntableMusicNote = 'solar-linear-turntable_music_note';
    case LinearKeyboard = 'solar-linear-Keyboard';
    case LinearGamepadCharge = 'solar-linear-gamepad_charge';
    case LinearBoombox = 'solar-linear-Boombox';
    case LinearSmartSpeakerMinimalistic = 'solar-linear-smart_speaker_minimalistic';
    case LinearTelescope = 'solar-linear-Telescope';
    case LinearMonitorCamera = 'solar-linear-monitor_camera';
    case LinearLaptopMinimalistic = 'solar-linear-laptop_minimalistic';
    case LinearServer2 = 'solar-linear-server2';
    case LinearSmartSpeaker = 'solar-linear-smart_speaker';
    case LinearProjector = 'solar-linear-Projector';
    case LinearServer = 'solar-linear-Server';
    case LinearTV = 'solar-linear-TV';
    case LinearCassette2 = 'solar-linear-cassette2';
    case LinearRadio = 'solar-linear-Radio';
    case LinearSmartphoneVibration = 'solar-linear-smartphone_vibration';
    case LinearAirbudsLeft = 'solar-linear-airbuds_left';
    case LinearHeadphonesRound = 'solar-linear-headphones_round';
    case LinearGameboy = 'solar-linear-Gameboy';
    case LinearHeadphonesRoundSound = 'solar-linear-headphones_round_sound';
    case LinearCPU = 'solar-linear-CPU';
    case LinearPrinter2 = 'solar-linear-printer2';
    case LinearHeadphonesSquare = 'solar-linear-headphones_square';
    case LinearServerSquareUpdate = 'solar-linear-server_square_update';
    case LinearPrinterMinimalistic = 'solar-linear-printer_minimalistic';
    case LinearBluetooth = 'solar-linear-Bluetooth';
    case LinearWirelessCharge = 'solar-linear-wireless_charge';
    case LinearBluetoothCircle = 'solar-linear-bluetooth_circle';
    case LinearAirbudsCaseMinimalistic = 'solar-linear-airbuds_case_minimalistic';
    case LinearLightbulb = 'solar-linear-Lightbulb';
    case LinearAirbudsRemove = 'solar-linear-airbuds_remove';
    case LinearSmartphoneRotate2 = 'solar-linear-smartphone_rotate2';
    case LinearSsdSquare = 'solar-linear-ssd_square';
    case LinearPrinter = 'solar-linear-Printer';
    case LinearSmartphone2 = 'solar-linear-smartphone2';
    case LinearServerMinimalistic = 'solar-linear-server_minimalistic';
    case LinearHeadphonesSquareSound = 'solar-linear-headphones_square_sound';
    case LinearDiskette = 'solar-linear-Diskette';
    case LinearBluetoothWave = 'solar-linear-bluetooth_wave';
    case LinearSmartSpeaker2 = 'solar-linear-smart_speaker2';
    case LinearLaptop3 = 'solar-linear-laptop3';
    case LinearLaptop2 = 'solar-linear-laptop2';
    case LinearMouseCircle = 'solar-linear-mouse_circle';
    case LinearTurntableMinimalistic = 'solar-linear-turntable_minimalistic';
    case LinearSmartphoneUpdate = 'solar-linear-smartphone_update';
    case LinearGamepadMinimalistic = 'solar-linear-gamepad_minimalistic';
    case LinearSdCard = 'solar-linear-sd_card';
    case LinearPlugCircle = 'solar-linear-plug_circle';
    case LinearAirbudsCase = 'solar-linear-airbuds_case';
    case LinearSsdRound = 'solar-linear-ssd_round';
    case LinearLaptop = 'solar-linear-Laptop';
    case LinearAirbudsRight = 'solar-linear-airbuds_right';
    case LinearDisplay = 'solar-linear-Display';
    case LinearMonitorSmartphone = 'solar-linear-monitor_smartphone';
    case LinearSocket = 'solar-linear-Socket';
    case LinearGamepadOld = 'solar-linear-gamepad_old';
    case LinearCpuBolt = 'solar-linear-cpu_bolt';
    case LinearAirbudsCaseCharge = 'solar-linear-airbuds_case_charge';
    case LinearTablet = 'solar-linear-Tablet';
    case LinearWeigher = 'solar-linear-Weigher';
    case LinearServerSquare = 'solar-linear-server_square';
    case LinearMouse = 'solar-linear-Mouse';
    case LinearGamepadNoCharge = 'solar-linear-gamepad_no_charge';
    case LinearBluetoothSquare = 'solar-linear-bluetooth_square';
    case LinearCloudStorage = 'solar-linear-cloud_storage';
    case LinearGamepad = 'solar-linear-Gamepad';
    case LinearMonitor = 'solar-linear-Monitor';
    case LinearCassette = 'solar-linear-Cassette';
    // Broken Style (1183 icons)
    case BrokenFacemaskCircle = 'solar-broken-facemask_circle';
    case BrokenConfoundedCircle = 'solar-broken-confounded_circle';
    case BrokenSadSquare = 'solar-broken-sad_square';
    case BrokenSleepingCircle = 'solar-broken-sleeping_circle';
    case BrokenFaceScanCircle = 'solar-broken-face_scan_circle';
    case BrokenSmileCircle = 'solar-broken-smile_circle';
    case BrokenStickerSmileCircle = 'solar-broken-sticker_smile_circle';
    case BrokenStickerSquare = 'solar-broken-sticker_square';
    case BrokenEmojiFunnyCircle = 'solar-broken-emoji_funny_circle';
    case BrokenExpressionlessSquare = 'solar-broken-expressionless_square';
    case BrokenSleepingSquare = 'solar-broken-sleeping_square';
    case BrokenSadCircle = 'solar-broken-sad_circle';
    case BrokenFacemaskSquare = 'solar-broken-facemask_square';
    case BrokenConfoundedSquare = 'solar-broken-confounded_square';
    case BrokenFaceScanSquare = 'solar-broken-face_scan_square';
    case BrokenSmileSquare = 'solar-broken-smile_square';
    case BrokenStickerSmileCircle2 = 'solar-broken-sticker_smile_circle2';
    case BrokenStickerSmileSquare = 'solar-broken-sticker_smile_square';
    case BrokenEmojiFunnySquare = 'solar-broken-emoji_funny_square';
    case BrokenStickerCircle = 'solar-broken-sticker_circle';
    case BrokenExpressionlessCircle = 'solar-broken-expressionless_circle';
    case BrokenLike = 'solar-broken-Like';
    case BrokenMedalStarSquare = 'solar-broken-medal_star_square';
    case BrokenDislike = 'solar-broken-Dislike';
    case BrokenStarShine = 'solar-broken-star_shine';
    case BrokenHeartAngle = 'solar-broken-heart_angle';
    case BrokenMedalRibbon = 'solar-broken-medal_ribbon';
    case BrokenHeartShine = 'solar-broken-heart_shine';
    case BrokenMedalStarCircle = 'solar-broken-medal_star_circle';
    case BrokenMedalRibbonsStar = 'solar-broken-medal_ribbons_star';
    case BrokenStar = 'solar-broken-Star';
    case BrokenHeartUnlock = 'solar-broken-heart_unlock';
    case BrokenMedalRibbonStar = 'solar-broken-medal_ribbon_star';
    case BrokenHeartLock = 'solar-broken-heart_lock';
    case BrokenHeartBroken = 'solar-broken-heart_broken';
    case BrokenHearts = 'solar-broken-Hearts';
    case BrokenMedalStar = 'solar-broken-medal_star';
    case BrokenHeart = 'solar-broken-Heart';
    case BrokenCloset = 'solar-broken-Closet';
    case BrokenBed = 'solar-broken-Bed';
    case BrokenWashingMachine = 'solar-broken-washing_machine';
    case BrokenBedsideTable = 'solar-broken-bedside_table';
    case BrokenSofa3 = 'solar-broken-sofa3';
    case BrokenSofa2 = 'solar-broken-sofa2';
    case BrokenChair2 = 'solar-broken-chair2';
    case BrokenBath = 'solar-broken-Bath';
    case BrokenSmartVacuumCleaner2 = 'solar-broken-smart_vacuum_cleaner2';
    case BrokenCondicioner = 'solar-broken-Condicioner';
    case BrokenSmartVacuumCleaner = 'solar-broken-smart_vacuum_cleaner';
    case BrokenRemoteController2 = 'solar-broken-remote_controller2';
    case BrokenFloorLampMinimalistic = 'solar-broken-floor_lamp_minimalistic';
    case BrokenLamp = 'solar-broken-Lamp';
    case BrokenBarChair = 'solar-broken-bar_chair';
    case BrokenBedsideTable2 = 'solar-broken-bedside_table2';
    case BrokenCloset2 = 'solar-broken-closet2';
    case BrokenBedsideTable3 = 'solar-broken-bedside_table3';
    case BrokenSpeaker = 'solar-broken-Speaker';
    case BrokenVolumeKnob = 'solar-broken-volume_knob';
    case BrokenArmchair = 'solar-broken-Armchair';
    case BrokenSpeakerMinimalistic = 'solar-broken-speaker_minimalistic';
    case BrokenRemoteController = 'solar-broken-remote_controller';
    case BrokenTrellis = 'solar-broken-Trellis';
    case BrokenFloorLamp = 'solar-broken-floor_lamp';
    case BrokenCondicioner2 = 'solar-broken-condicioner2';
    case BrokenBedsideTable4 = 'solar-broken-bedside_table4';
    case BrokenArmchair2 = 'solar-broken-armchair2';
    case BrokenWashingMachineMinimalistic = 'solar-broken-washing_machine_minimalistic';
    case BrokenChair = 'solar-broken-Chair';
    case BrokenRemoteControllerMinimalistic = 'solar-broken-remote_controller_minimalistic';
    case BrokenChandelier = 'solar-broken-Chandelier';
    case BrokenFridge = 'solar-broken-Fridge';
    case BrokenMirror = 'solar-broken-Mirror';
    case BrokenSofa = 'solar-broken-Sofa';
    case BrokenEarth = 'solar-broken-Earth';
    case BrokenStarsLine = 'solar-broken-stars_line';
    case BrokenStarFall2 = 'solar-broken-star_fall2';
    case BrokenStarFall = 'solar-broken-star_fall';
    case BrokenBlackHole3 = 'solar-broken-black_hole3';
    case BrokenWomen = 'solar-broken-Women';
    case BrokenBlackHole = 'solar-broken-black_hole';
    case BrokenStarRings = 'solar-broken-star_rings';
    case BrokenBlackHole2 = 'solar-broken-black_hole2';
    case BrokenStarFallMinimalistic2 = 'solar-broken-star_fall_minimalistic2';
    case BrokenPlanet = 'solar-broken-Planet';
    case BrokenSatellite = 'solar-broken-Satellite';
    case BrokenMen = 'solar-broken-Men';
    case BrokenRocket2 = 'solar-broken-rocket2';
    case BrokenStars = 'solar-broken-Stars';
    case BrokenStarAngle = 'solar-broken-star_angle';
    case BrokenInfinity = 'solar-broken-Infinity';
    case BrokenUfo2 = 'solar-broken-ufo2';
    case BrokenUfo3 = 'solar-broken-ufo3';
    case BrokenStarRing = 'solar-broken-star_ring';
    case BrokenPlanet2 = 'solar-broken-planet2';
    case BrokenPlanet3 = 'solar-broken-planet3';
    case BrokenAsteroid = 'solar-broken-Asteroid';
    case BrokenStarsMinimalistic = 'solar-broken-stars_minimalistic';
    case BrokenUFO = 'solar-broken-UFO';
    case BrokenPlanet4 = 'solar-broken-planet4';
    case BrokenRocket = 'solar-broken-Rocket';
    case BrokenStarFallMinimalistic = 'solar-broken-star_fall_minimalistic';
    case BrokenStarRainbow = 'solar-broken-star_rainbow';
    case BrokenAtom = 'solar-broken-Atom';
    case BrokenStarCircle = 'solar-broken-star_circle';
    case BrokenCompassBig = 'solar-broken-compass_big';
    case BrokenMapPointSchool = 'solar-broken-map_point_school';
    case BrokenSignpost = 'solar-broken-Signpost';
    case BrokenMapArrowDown = 'solar-broken-map_arrow_down';
    case BrokenMap = 'solar-broken-Map';
    case BrokenMapArrowUp = 'solar-broken-map_arrow_up';
    case BrokenPointOnMapPerspective = 'solar-broken-point_on_map_perspective';
    case BrokenRadar = 'solar-broken-Radar';
    case BrokenStreets = 'solar-broken-Streets';
    case BrokenMapPointWave = 'solar-broken-map_point_wave';
    case BrokenPeopleNearby = 'solar-broken-people_nearby';
    case BrokenStreetsMapPoint = 'solar-broken-streets_map_point';
    case BrokenMapPointSearch = 'solar-broken-map_point_search';
    case BrokenGPS = 'solar-broken-GPS';
    case BrokenMapArrowSquare = 'solar-broken-map_arrow_square';
    case BrokenBranchingPathsDown = 'solar-broken-branching_paths_down';
    case BrokenMapPointRotate = 'solar-broken-map_point_rotate';
    case BrokenGlobal = 'solar-broken-Global';
    case BrokenCompassSquare = 'solar-broken-compass_square';
    case BrokenRouting3 = 'solar-broken-routing3';
    case BrokenRouting2 = 'solar-broken-routing2';
    case BrokenMapPointRemove = 'solar-broken-map_point_remove';
    case BrokenGlobus = 'solar-broken-Globus';
    case BrokenSignpost2 = 'solar-broken-signpost2';
    case BrokenRadar2 = 'solar-broken-radar2';
    case BrokenStreetsNavigation = 'solar-broken-streets_navigation';
    case BrokenMapPoint = 'solar-broken-map_point';
    case BrokenMapPointHospital = 'solar-broken-map_point_hospital';
    case BrokenCompass = 'solar-broken-Compass';
    case BrokenMapPointAdd = 'solar-broken-map_point_add';
    case BrokenBranchingPathsUp = 'solar-broken-branching_paths_up';
    case BrokenMapPointFavourite = 'solar-broken-map_point_favourite';
    case BrokenRoute = 'solar-broken-Route';
    case BrokenPointOnMap = 'solar-broken-point_on_map';
    case BrokenMapArrowRight = 'solar-broken-map_arrow_right';
    case BrokenRouting = 'solar-broken-Routing';
    case BrokenMapArrowLeft = 'solar-broken-map_arrow_left';
    case BrokenIncognito = 'solar-broken-Incognito';
    case BrokenLockPassword = 'solar-broken-lock_password';
    case BrokenShieldNetwork = 'solar-broken-shield_network';
    case BrokenKeyMinimalisticSquare = 'solar-broken-key_minimalistic_square';
    case BrokenLockKeyholeUnlocked = 'solar-broken-lock_keyhole_unlocked';
    case BrokenLock = 'solar-broken-Lock';
    case BrokenShieldKeyhole = 'solar-broken-shield_keyhole';
    case BrokenEyeClosed = 'solar-broken-eye_closed';
    case BrokenKey = 'solar-broken-Key';
    case BrokenShieldMinus = 'solar-broken-shield_minus';
    case BrokenShield = 'solar-broken-Shield';
    case BrokenLockUnlocked = 'solar-broken-lock_unlocked';
    case BrokenBombMinimalistic = 'solar-broken-bomb_minimalistic';
    case BrokenShieldStar = 'solar-broken-shield_star';
    case BrokenBomb = 'solar-broken-Bomb';
    case BrokenKeySquare = 'solar-broken-key_square';
    case BrokenLockKeyholeMinimalisticUnlocked = 'solar-broken-lock_keyhole_minimalistic_unlocked';
    case BrokenShieldCross = 'solar-broken-shield_cross';
    case BrokenObjectScan = 'solar-broken-object_scan';
    case BrokenPasswordMinimalisticInput = 'solar-broken-password_minimalistic_input';
    case BrokenLockPasswordUnlocked = 'solar-broken-lock_password_unlocked';
    case BrokenSiren = 'solar-broken-Siren';
    case BrokenShieldMinimalistic = 'solar-broken-shield_minimalistic';
    case BrokenEyeScan = 'solar-broken-eye_scan';
    case BrokenKeyMinimalisticSquare2 = 'solar-broken-key_minimalistic_square2';
    case BrokenScanner2 = 'solar-broken-scanner2';
    case BrokenKeyMinimalisticSquare3 = 'solar-broken-key_minimalistic_square3';
    case BrokenKeyMinimalistic2 = 'solar-broken-key_minimalistic2';
    case BrokenCodeScan = 'solar-broken-code_scan';
    case BrokenShieldPlus = 'solar-broken-shield_plus';
    case BrokenPasswordMinimalistic = 'solar-broken-password_minimalistic';
    case BrokenEye = 'solar-broken-Eye';
    case BrokenQrCode = 'solar-broken-qr_code';
    case BrokenShieldCheck = 'solar-broken-shield_check';
    case BrokenKeyMinimalistic = 'solar-broken-key_minimalistic';
    case BrokenLockKeyhole = 'solar-broken-lock_keyhole';
    case BrokenShieldUser = 'solar-broken-shield_user';
    case BrokenKeySquare2 = 'solar-broken-key_square2';
    case BrokenBombEmoji = 'solar-broken-bomb_emoji';
    case BrokenScanner = 'solar-broken-Scanner';
    case BrokenShieldUp = 'solar-broken-shield_up';
    case BrokenSirenRounded = 'solar-broken-siren_rounded';
    case BrokenLockKeyholeMinimalistic = 'solar-broken-lock_keyhole_minimalistic';
    case BrokenPassword = 'solar-broken-Password';
    case BrokenShieldKeyholeMinimalistic = 'solar-broken-shield_keyhole_minimalistic';
    case BrokenShieldWarning = 'solar-broken-shield_warning';
    case BrokenPallete2 = 'solar-broken-pallete2';
    case BrokenAlignVerticalSpacing = 'solar-broken-align_vertical_spacing';
    case BrokenAlignVerticalCenter = 'solar-broken-align_vertical_center';
    case BrokenCropMinimalistic = 'solar-broken-crop_minimalistic';
    case BrokenMirrorRight = 'solar-broken-mirror_right';
    case BrokenAlignBottom = 'solar-broken-align_bottom';
    case BrokenRadialBlur = 'solar-broken-radial_blur';
    case BrokenCrop = 'solar-broken-Crop';
    case BrokenAlignHorizontaSpacing = 'solar-broken-align_horizonta_spacing';
    case BrokenRulerPen = 'solar-broken-ruler_pen';
    case BrokenThreeSquares = 'solar-broken-three_squares';
    case BrokenPaintRoller = 'solar-broken-paint_roller';
    case BrokenLayers = 'solar-broken-Layers';
    case BrokenFilters = 'solar-broken-Filters';
    case BrokenRulerCrossPen = 'solar-broken-ruler_cross_pen';
    case BrokenFlipHorizontal = 'solar-broken-flip_horizontal';
    case BrokenAlignLeft = 'solar-broken-align_left';
    case BrokenRuler = 'solar-broken-Ruler';
    case BrokenPalette = 'solar-broken-Palette';
    case BrokenAlignTop = 'solar-broken-align_top';
    case BrokenAlignHorizontalCenter = 'solar-broken-align_horizontal_center';
    case BrokenAlignRight = 'solar-broken-align_right';
    case BrokenRulerAngular = 'solar-broken-ruler_angular';
    case BrokenPipette = 'solar-broken-Pipette';
    case BrokenFlipVertical = 'solar-broken-flip_vertical';
    case BrokenMirrorLeft = 'solar-broken-mirror_left';
    case BrokenLayersMinimalistic = 'solar-broken-layers_minimalistic';
    case BrokenColourTuneing = 'solar-broken-colour_tuneing';
    case BrokenPaletteRound = 'solar-broken-palette_round';
    case BrokenEraser = 'solar-broken-Eraser';
    case BrokenTextItalicCircle = 'solar-broken-text_italic_circle';
    case BrokenLinkRound = 'solar-broken-link_round';
    case BrokenTextItalic = 'solar-broken-text_italic';
    case BrokenLinkBrokenMinimalistic = 'solar-broken-link_broken_minimalistic';
    case BrokenTextUnderlineCross = 'solar-broken-text_underline_cross';
    case BrokenLink = 'solar-broken-Link';
    case BrokenEraserCircle = 'solar-broken-eraser_circle';
    case BrokenLinkCircle = 'solar-broken-link_circle';
    case BrokenTextBoldCircle = 'solar-broken-text_bold_circle';
    case BrokenTextField = 'solar-broken-text_field';
    case BrokenTextSquare = 'solar-broken-text_square';
    case BrokenTextSquare2 = 'solar-broken-text_square2';
    case BrokenLinkRoundAngle = 'solar-broken-link_round_angle';
    case BrokenTextUnderlineCircle = 'solar-broken-text_underline_circle';
    case BrokenTextCrossCircle = 'solar-broken-text_cross_circle';
    case BrokenTextItalicSquare = 'solar-broken-text_italic_square';
    case BrokenParagraphSpacing = 'solar-broken-paragraph_spacing';
    case BrokenText = 'solar-broken-Text';
    case BrokenLinkBroken = 'solar-broken-link_broken';
    case BrokenTextCross = 'solar-broken-text_cross';
    case BrokenTextUnderline = 'solar-broken-text_underline';
    case BrokenLinkMinimalistic = 'solar-broken-link_minimalistic';
    case BrokenLinkMinimalistic2 = 'solar-broken-link_minimalistic2';
    case BrokenTextBold = 'solar-broken-text_bold';
    case BrokenTextSelection = 'solar-broken-text_selection';
    case BrokenTextFieldFocus = 'solar-broken-text_field_focus';
    case BrokenTextBoldSquare = 'solar-broken-text_bold_square';
    case BrokenEraserSquare = 'solar-broken-eraser_square';
    case BrokenLinkSquare = 'solar-broken-link_square';
    case BrokenTextCircle = 'solar-broken-text_circle';
    case BrokenBackspace = 'solar-broken-Backspace';
    case BrokenTextCrossSquare = 'solar-broken-text_cross_square';
    case BrokenInboxUnread = 'solar-broken-inbox_unread';
    case BrokenChatUnread = 'solar-broken-chat_unread';
    case BrokenChatRound = 'solar-broken-chat_round';
    case BrokenUnread = 'solar-broken-Unread';
    case BrokenMailbox = 'solar-broken-Mailbox';
    case BrokenLetter = 'solar-broken-Letter';
    case BrokenPenNewRound = 'solar-broken-pen_new_round';
    case BrokenMultipleForwardRight = 'solar-broken-multiple_forward_right';
    case BrokenMultipleForwardLeft = 'solar-broken-multiple_forward_left';
    case BrokenInboxArchive = 'solar-broken-inbox_archive';
    case BrokenInbox = 'solar-broken-Inbox';
    case BrokenPen2 = 'solar-broken-pen2';
    case BrokenPenNewSquare = 'solar-broken-pen_new_square';
    case BrokenPen = 'solar-broken-Pen';
    case BrokenChatDots = 'solar-broken-chat_dots';
    case BrokenChatSquareCall = 'solar-broken-chat_square_call';
    case BrokenSquareShareLine = 'solar-broken-square_share_line';
    case BrokenChatRoundCheck = 'solar-broken-chat_round_check';
    case BrokenInboxOut = 'solar-broken-inbox_out';
    case BrokenPlain3 = 'solar-broken-plain3';
    case BrokenChatRoundDots = 'solar-broken-chat_round_dots';
    case BrokenChatRoundLike = 'solar-broken-chat_round_like';
    case BrokenPlain2 = 'solar-broken-plain2';
    case BrokenChatRoundUnread = 'solar-broken-chat_round_unread';
    case BrokenChatSquareLike = 'solar-broken-chat_square_like';
    case BrokenPaperclip = 'solar-broken-Paperclip';
    case BrokenChatSquareCheck = 'solar-broken-chat_square_check';
    case BrokenChatSquare = 'solar-broken-chat_square';
    case BrokenLetterOpened = 'solar-broken-letter_opened';
    case BrokenSquareForward = 'solar-broken-square_forward';
    case BrokenLetterUnread = 'solar-broken-letter_unread';
    case BrokenPaperclipRounded2 = 'solar-broken-paperclip_rounded2';
    case BrokenChatRoundCall = 'solar-broken-chat_round_call';
    case BrokenInboxLine = 'solar-broken-inbox_line';
    case BrokenChatRoundVideo = 'solar-broken-chat_round_video';
    case BrokenChatRoundMoney = 'solar-broken-chat_round_money';
    case BrokenInboxIn = 'solar-broken-inbox_in';
    case BrokenCheckRead = 'solar-broken-check_read';
    case BrokenChatRoundLine = 'solar-broken-chat_round_line';
    case BrokenForward = 'solar-broken-Forward';
    case BrokenPaperclip2 = 'solar-broken-paperclip2';
    case BrokenDialog2 = 'solar-broken-dialog2';
    case BrokenDialog = 'solar-broken-Dialog';
    case BrokenPaperclipRounded = 'solar-broken-paperclip_rounded';
    case BrokenPlain = 'solar-broken-Plain';
    case BrokenChatSquareArrow = 'solar-broken-chat_square_arrow';
    case BrokenChatSquareCode = 'solar-broken-chat_square_code';
    case BrokenChatLine = 'solar-broken-chat_line';
    case BrokenTennis = 'solar-broken-Tennis';
    case BrokenBicyclingRound = 'solar-broken-bicycling_round';
    case BrokenBalls = 'solar-broken-Balls';
    case BrokenMeditationRound = 'solar-broken-meditation_round';
    case BrokenStretchingRound = 'solar-broken-stretching_round';
    case BrokenDumbbells2 = 'solar-broken-dumbbells2';
    case BrokenMeditation = 'solar-broken-Meditation';
    case BrokenRunning2 = 'solar-broken-running2';
    case BrokenRugby = 'solar-broken-Rugby';
    case BrokenBodyShapeMinimalistic = 'solar-broken-body_shape_minimalistic';
    case BrokenStretching = 'solar-broken-Stretching';
    case BrokenBowling = 'solar-broken-Bowling';
    case BrokenRanking = 'solar-broken-Ranking';
    case BrokenTreadmillRound = 'solar-broken-treadmill_round';
    case BrokenVolleyball = 'solar-broken-Volleyball';
    case BrokenDumbbellLargeMinimalistic = 'solar-broken-dumbbell_large_minimalistic';
    case BrokenRunningRound = 'solar-broken-running_round';
    case BrokenHiking = 'solar-broken-Hiking';
    case BrokenHikingMinimalistic = 'solar-broken-hiking_minimalistic';
    case BrokenWaterSun = 'solar-broken-water_sun';
    case BrokenGolf = 'solar-broken-Golf';
    case BrokenSkateboarding = 'solar-broken-Skateboarding';
    case BrokenDumbbells = 'solar-broken-Dumbbells';
    case BrokenWalkingRound = 'solar-broken-walking_round';
    case BrokenRunning = 'solar-broken-Running';
    case BrokenTreadmill = 'solar-broken-Treadmill';
    case BrokenSkateboard = 'solar-broken-Skateboard';
    case BrokenDumbbellSmall = 'solar-broken-dumbbell_small';
    case BrokenBasketball = 'solar-broken-Basketball';
    case BrokenFootball = 'solar-broken-Football';
    case BrokenDumbbell = 'solar-broken-Dumbbell';
    case BrokenBodyShape = 'solar-broken-body_shape';
    case BrokenWater = 'solar-broken-Water';
    case BrokenSkateboardingRound = 'solar-broken-skateboarding_round';
    case BrokenHikingRound = 'solar-broken-hiking_round';
    case BrokenVolleyball2 = 'solar-broken-volleyball2';
    case BrokenTennis2 = 'solar-broken-tennis2';
    case BrokenSwimming = 'solar-broken-Swimming';
    case BrokenBicycling = 'solar-broken-Bicycling';
    case BrokenWalking = 'solar-broken-Walking';
    case BrokenDumbbellLarge = 'solar-broken-dumbbell_large';
    case BrokenCalendarMark = 'solar-broken-calendar_mark';
    case BrokenHistory2 = 'solar-broken-history2';
    case BrokenWatchSquareMinimalisticCharge = 'solar-broken-watch_square_minimalistic_charge';
    case BrokenHistory3 = 'solar-broken-history3';
    case BrokenHourglass = 'solar-broken-Hourglass';
    case BrokenCalendarSearch = 'solar-broken-calendar_search';
    case BrokenStopwatchPlay = 'solar-broken-stopwatch_play';
    case BrokenWatchRound = 'solar-broken-watch_round';
    case BrokenCalendarAdd = 'solar-broken-calendar_add';
    case BrokenCalendarDate = 'solar-broken-calendar_date';
    case BrokenStopwatch = 'solar-broken-Stopwatch';
    case BrokenAlarmPause = 'solar-broken-alarm_pause';
    case BrokenAlarmTurnOff = 'solar-broken-alarm_turn_off';
    case BrokenClockSquare = 'solar-broken-clock_square';
    case BrokenStopwatchPause = 'solar-broken-stopwatch_pause';
    case BrokenCalendarMinimalistic = 'solar-broken-calendar_minimalistic';
    case BrokenAlarmAdd = 'solar-broken-alarm_add';
    case BrokenAlarmPlay = 'solar-broken-alarm_play';
    case BrokenHourglassLine = 'solar-broken-hourglass_line';
    case BrokenAlarmSleep = 'solar-broken-alarm_sleep';
    case BrokenAlarmRemove = 'solar-broken-alarm_remove';
    case BrokenCalendar = 'solar-broken-Calendar';
    case BrokenClockCircle = 'solar-broken-clock_circle';
    case BrokenHistory = 'solar-broken-History';
    case BrokenAlarm = 'solar-broken-Alarm';
    case BrokenWatchSquare = 'solar-broken-watch_square';
    case BrokenWatchSquareMinimalistic = 'solar-broken-watch_square_minimalistic';
    case BrokenMagniferBug = 'solar-broken-magnifer_bug';
    case BrokenMagnifer = 'solar-broken-Magnifer';
    case BrokenMagniferZoomIn = 'solar-broken-magnifer_zoom_in';
    case BrokenRoundedMagnifer = 'solar-broken-rounded_magnifer';
    case BrokenRoundedMagniferZoomIn = 'solar-broken-rounded_magnifer_zoom_in';
    case BrokenMinimalisticMagniferBug = 'solar-broken-minimalistic_magnifer_bug';
    case BrokenRoundedMagniferBug = 'solar-broken-rounded_magnifer_bug';
    case BrokenMinimalisticMagniferZoomOut = 'solar-broken-minimalistic_magnifer_zoom_out';
    case BrokenMinimalisticMagnifer = 'solar-broken-minimalistic_magnifer';
    case BrokenRoundedMagniferZoomOut = 'solar-broken-rounded_magnifer_zoom_out';
    case BrokenMinimalisticMagniferZoomIn = 'solar-broken-minimalistic_magnifer_zoom_in';
    case BrokenMagniferZoomOut = 'solar-broken-magnifer_zoom_out';
    case BrokenBagCheck = 'solar-broken-bag_check';
    case BrokenShopMinimalistic = 'solar-broken-shop_minimalistic';
    case BrokenShop = 'solar-broken-Shop';
    case BrokenCartCheck = 'solar-broken-cart_check';
    case BrokenCart = 'solar-broken-Cart';
    case BrokenCart3 = 'solar-broken-cart3';
    case BrokenCart2 = 'solar-broken-cart2';
    case BrokenBagMusic = 'solar-broken-bag_music';
    case BrokenCartLargeMinimalistic = 'solar-broken-cart_large_minimalistic';
    case BrokenCart5 = 'solar-broken-cart5';
    case BrokenCart4 = 'solar-broken-cart4';
    case BrokenBag = 'solar-broken-Bag';
    case BrokenBagHeart = 'solar-broken-bag_heart';
    case BrokenCartPlus = 'solar-broken-cart_plus';
    case BrokenCartLarge = 'solar-broken-cart_large';
    case BrokenBagCross = 'solar-broken-bag_cross';
    case BrokenBagMusic2 = 'solar-broken-bag_music2';
    case BrokenBag5 = 'solar-broken-bag5';
    case BrokenBag4 = 'solar-broken-bag4';
    case BrokenCartLarge4 = 'solar-broken-cart_large4';
    case BrokenCartLarge3 = 'solar-broken-cart_large3';
    case BrokenBag3 = 'solar-broken-bag3';
    case BrokenBag2 = 'solar-broken-bag2';
    case BrokenShop2 = 'solar-broken-shop2';
    case BrokenCartLarge2 = 'solar-broken-cart_large2';
    case BrokenBagSmile = 'solar-broken-bag_smile';
    case BrokenCartCross = 'solar-broken-cart_cross';
    case BrokenInfoSquare = 'solar-broken-info_square';
    case BrokenFlashlightOn = 'solar-broken-flashlight_on';
    case BrokenXXX = 'solar-broken-XXX';
    case BrokenFigma = 'solar-broken-Figma';
    case BrokenFlashlight = 'solar-broken-Flashlight';
    case BrokenGhost = 'solar-broken-Ghost';
    case BrokenCupMusic = 'solar-broken-cup_music';
    case BrokenBatteryFullMinimalistic = 'solar-broken-battery_full_minimalistic';
    case BrokenDangerCircle = 'solar-broken-danger_circle';
    case BrokenCheckSquare = 'solar-broken-check_square';
    case BrokenGhostSmile = 'solar-broken-ghost_smile';
    case BrokenTarget = 'solar-broken-Target';
    case BrokenBatteryHalfMinimalistic = 'solar-broken-battery_half_minimalistic';
    case BrokenScissors = 'solar-broken-Scissors';
    case BrokenPinList = 'solar-broken-pin_list';
    case BrokenBatteryCharge = 'solar-broken-battery_charge';
    case BrokenUmbrella = 'solar-broken-Umbrella';
    case BrokenHomeSmile = 'solar-broken-home_smile';
    case BrokenHome = 'solar-broken-Home';
    case BrokenCopyright = 'solar-broken-Copyright';
    case BrokenHomeWifi = 'solar-broken-home_wifi';
    case BrokenTShirt = 'solar-broken-t_shirt';
    case BrokenBatteryChargeMinimalistic = 'solar-broken-battery_charge_minimalistic';
    case BrokenCupStar = 'solar-broken-cup_star';
    case BrokenSpecialEffects = 'solar-broken-special_effects';
    case BrokenBody = 'solar-broken-Body';
    case BrokenHamburgerMenu = 'solar-broken-hamburger_menu';
    case BrokenPower = 'solar-broken-Power';
    case BrokenDatabase = 'solar-broken-Database';
    case BrokenCursorSquare = 'solar-broken-cursor_square';
    case BrokenFuel = 'solar-broken-Fuel';
    case BrokenMentionCircle = 'solar-broken-mention_circle';
    case BrokenConfettiMinimalistic = 'solar-broken-confetti_minimalistic';
    case BrokenMenuDotsCircle = 'solar-broken-menu_dots_circle';
    case BrokenPaw = 'solar-broken-Paw';
    case BrokenSubtitles = 'solar-broken-Subtitles';
    case BrokenSliderVerticalMinimalistic = 'solar-broken-slider_vertical_minimalistic';
    case BrokenCrownMinimalistic = 'solar-broken-crown_minimalistic';
    case BrokenMenuDots = 'solar-broken-menu_dots';
    case BrokenDelivery = 'solar-broken-Delivery';
    case BrokenWaterdrop = 'solar-broken-Waterdrop';
    case BrokenPerfume = 'solar-broken-Perfume';
    case BrokenHomeAngle2 = 'solar-broken-home_angle2';
    case BrokenHomeWifiAngle = 'solar-broken-home_wifi_angle';
    case BrokenQuestionCircle = 'solar-broken-question_circle';
    case BrokenTrashBinMinimalistic = 'solar-broken-trash_bin_minimalistic';
    case BrokenMagicStick3 = 'solar-broken-magic_stick3';
    case BrokenAddSquare = 'solar-broken-add_square';
    case BrokenCrownStar = 'solar-broken-crown_star';
    case BrokenMagnet = 'solar-broken-Magnet';
    case BrokenConfetti = 'solar-broken-Confetti';
    case BrokenPin = 'solar-broken-Pin';
    case BrokenMinusSquare = 'solar-broken-minus_square';
    case BrokenBolt = 'solar-broken-Bolt';
    case BrokenCloseCircle = 'solar-broken-close_circle';
    case BrokenForbiddenCircle = 'solar-broken-forbidden_circle';
    case BrokenMagicStick2 = 'solar-broken-magic_stick2';
    case BrokenCrownLine = 'solar-broken-crown_line';
    case BrokenBoltCircle = 'solar-broken-bolt_circle';
    case BrokenFlag = 'solar-broken-Flag';
    case BrokenSliderHorizontal = 'solar-broken-slider_horizontal';
    case BrokenHighDefinition = 'solar-broken-high_definition';
    case BrokenCursor = 'solar-broken-Cursor';
    case BrokenFeed = 'solar-broken-Feed';
    case BrokenTrafficEconomy = 'solar-broken-traffic_economy';
    case BrokenAugmentedReality = 'solar-broken-augmented_reality';
    case BrokenIcon4K = 'solar-broken-4_k';
    case BrokenMagnetWave = 'solar-broken-magnet_wave';
    case BrokenHomeSmileAngle = 'solar-broken-home_smile_angle';
    case BrokenSliderVertical = 'solar-broken-slider_vertical';
    case BrokenCheckCircle = 'solar-broken-check_circle';
    case BrokenCopy = 'solar-broken-Copy';
    case BrokenDangerSquare = 'solar-broken-danger_square';
    case BrokenSkirt = 'solar-broken-Skirt';
    case BrokenGlasses = 'solar-broken-Glasses';
    case BrokenHomeAdd = 'solar-broken-home_add';
    case BrokenSledgehammer = 'solar-broken-Sledgehammer';
    case BrokenInfoCircle = 'solar-broken-info_circle';
    case BrokenDangerTriangle = 'solar-broken-danger_triangle';
    case BrokenPinCircle = 'solar-broken-pin_circle';
    case BrokenSmartHome = 'solar-broken-smart_home';
    case BrokenScissorsSquare = 'solar-broken-scissors_square';
    case BrokenSleeping = 'solar-broken-Sleeping';
    case BrokenBox = 'solar-broken-Box';
    case BrokenCrown = 'solar-broken-Crown';
    case BrokenBroom = 'solar-broken-Broom';
    case BrokenPostsCarouselHorizontal = 'solar-broken-posts_carousel_horizontal';
    case BrokenFlag2 = 'solar-broken-flag2';
    case BrokenPlate = 'solar-broken-Plate';
    case BrokenTrashBinTrash = 'solar-broken-trash_bin_trash';
    case BrokenCupFirst = 'solar-broken-cup_first';
    case BrokenSmartHomeAngle = 'solar-broken-smart_home_angle';
    case BrokenPaperBin = 'solar-broken-paper_bin';
    case BrokenBoxMinimalistic = 'solar-broken-box_minimalistic';
    case BrokenDanger = 'solar-broken-Danger';
    case BrokenMenuDotsSquare = 'solar-broken-menu_dots_square';
    case BrokenHanger2 = 'solar-broken-hanger2';
    case BrokenBatteryHalf = 'solar-broken-battery_half';
    case BrokenHome2 = 'solar-broken-home2';
    case BrokenPostsCarouselVertical = 'solar-broken-posts_carousel_vertical';
    case BrokenRevote = 'solar-broken-Revote';
    case BrokenMentionSquare = 'solar-broken-mention_square';
    case BrokenWinRar = 'solar-broken-win_rar';
    case BrokenForbidden = 'solar-broken-Forbidden';
    case BrokenQuestionSquare = 'solar-broken-question_square';
    case BrokenHanger = 'solar-broken-Hanger';
    case BrokenReorder = 'solar-broken-Reorder';
    case BrokenHomeAddAngle = 'solar-broken-home_add_angle';
    case BrokenMasks = 'solar-broken-Masks';
    case BrokenGift = 'solar-broken-Gift';
    case BrokenCreativeCommons = 'solar-broken-creative_commons';
    case BrokenSliderMinimalisticHorizontal = 'solar-broken-slider_minimalistic_horizontal';
    case BrokenHomeAngle = 'solar-broken-home_angle';
    case BrokenBatteryLowMinimalistic = 'solar-broken-battery_low_minimalistic';
    case BrokenShare = 'solar-broken-Share';
    case BrokenTrashBin2 = 'solar-broken-trash_bin2';
    case BrokenSort = 'solar-broken-Sort';
    case BrokenMinusCircle = 'solar-broken-minus_circle';
    case BrokenExplicit = 'solar-broken-Explicit';
    case BrokenTraffic = 'solar-broken-Traffic';
    case BrokenFilter = 'solar-broken-Filter';
    case BrokenCloseSquare = 'solar-broken-close_square';
    case BrokenAddCircle = 'solar-broken-add_circle';
    case BrokenFerrisWheel = 'solar-broken-ferris_wheel';
    case BrokenCup = 'solar-broken-Cup';
    case BrokenBalloon = 'solar-broken-Balloon';
    case BrokenHelp = 'solar-broken-Help';
    case BrokenBatteryFull = 'solar-broken-battery_full';
    case BrokenCat = 'solar-broken-Cat';
    case BrokenMaskSad = 'solar-broken-mask_sad';
    case BrokenHighQuality = 'solar-broken-high_quality';
    case BrokenMagicStick = 'solar-broken-magic_stick';
    case BrokenCosmetic = 'solar-broken-Cosmetic';
    case BrokenBatteryLow = 'solar-broken-battery_low';
    case BrokenShareCircle = 'solar-broken-share_circle';
    case BrokenMaskHapply = 'solar-broken-mask_happly';
    case BrokenAccessibility = 'solar-broken-Accessibility';
    case BrokenTrashBinMinimalistic2 = 'solar-broken-trash_bin_minimalistic2';
    case BrokenIncomingCallRounded = 'solar-broken-incoming_call_rounded';
    case BrokenCallDropped = 'solar-broken-call_dropped';
    case BrokenCallChat = 'solar-broken-call_chat';
    case BrokenCallCancelRounded = 'solar-broken-call_cancel_rounded';
    case BrokenCallMedicineRounded = 'solar-broken-call_medicine_rounded';
    case BrokenCallDroppedRounded = 'solar-broken-call_dropped_rounded';
    case BrokenRecordSquare = 'solar-broken-record_square';
    case BrokenPhoneCalling = 'solar-broken-phone_calling';
    case BrokenPhoneRounded = 'solar-broken-phone_rounded';
    case BrokenCallMedicine = 'solar-broken-call_medicine';
    case BrokenRecordMinimalistic = 'solar-broken-record_minimalistic';
    case BrokenEndCall = 'solar-broken-end_call';
    case BrokenOutgoingCall = 'solar-broken-outgoing_call';
    case BrokenRecordCircle = 'solar-broken-record_circle';
    case BrokenIncomingCall = 'solar-broken-incoming_call';
    case BrokenCallChatRounded = 'solar-broken-call_chat_rounded';
    case BrokenEndCallRounded = 'solar-broken-end_call_rounded';
    case BrokenPhone = 'solar-broken-Phone';
    case BrokenOutgoingCallRounded = 'solar-broken-outgoing_call_rounded';
    case BrokenCallCancel = 'solar-broken-call_cancel';
    case BrokenPhoneCallingRounded = 'solar-broken-phone_calling_rounded';
    case BrokenStationMinimalistic = 'solar-broken-station_minimalistic';
    case BrokenSidebarCode = 'solar-broken-sidebar_code';
    case BrokenWiFiRouterMinimalistic = 'solar-broken-wi_fi_router_minimalistic';
    case BrokenUSB = 'solar-broken-USB';
    case BrokenSiderbar = 'solar-broken-Siderbar';
    case BrokenCode2 = 'solar-broken-code2';
    case BrokenSlashCircle = 'solar-broken-slash_circle';
    case BrokenScreencast = 'solar-broken-Screencast';
    case BrokenHashtagSquare = 'solar-broken-hashtag_square';
    case BrokenSidebarMinimalistic = 'solar-broken-sidebar_minimalistic';
    case BrokenCode = 'solar-broken-Code';
    case BrokenUsbSquare = 'solar-broken-usb_square';
    case BrokenWiFiRouter = 'solar-broken-wi_fi_router';
    case BrokenCodeCircle = 'solar-broken-code_circle';
    case BrokenTranslation = 'solar-broken-Translation';
    case BrokenBugMinimalistic = 'solar-broken-bug_minimalistic';
    case BrokenStation = 'solar-broken-Station';
    case BrokenProgramming = 'solar-broken-Programming';
    case BrokenWiFiRouterRound = 'solar-broken-wi_fi_router_round';
    case BrokenHashtag = 'solar-broken-Hashtag';
    case BrokenBug = 'solar-broken-Bug';
    case BrokenHashtagChat = 'solar-broken-hashtag_chat';
    case BrokenCommand = 'solar-broken-Command';
    case BrokenTranslation2 = 'solar-broken-translation2';
    case BrokenHashtagCircle = 'solar-broken-hashtag_circle';
    case BrokenScreencast2 = 'solar-broken-screencast2';
    case BrokenSlashSquare = 'solar-broken-slash_square';
    case BrokenWindowFrame = 'solar-broken-window_frame';
    case BrokenStructure = 'solar-broken-Structure';
    case BrokenUsbCircle = 'solar-broken-usb_circle';
    case BrokenCodeSquare = 'solar-broken-code_square';
    case BrokenNotes = 'solar-broken-Notes';
    case BrokenDocumentText = 'solar-broken-document_text';
    case BrokenDocumentAdd = 'solar-broken-document_add';
    case BrokenDocumentMedicine = 'solar-broken-document_medicine';
    case BrokenArchiveMinimalistic = 'solar-broken-archive_minimalistic';
    case BrokenClipboard = 'solar-broken-Clipboard';
    case BrokenClipboardAdd = 'solar-broken-clipboard_add';
    case BrokenArchive = 'solar-broken-Archive';
    case BrokenClipboardHeart = 'solar-broken-clipboard_heart';
    case BrokenClipboardRemove = 'solar-broken-clipboard_remove';
    case BrokenClipboardText = 'solar-broken-clipboard_text';
    case BrokenDocument = 'solar-broken-Document';
    case BrokenNotesMinimalistic = 'solar-broken-notes_minimalistic';
    case BrokenArchiveUp = 'solar-broken-archive_up';
    case BrokenArchiveUpMinimlistic = 'solar-broken-archive_up_minimlistic';
    case BrokenArchiveCheck = 'solar-broken-archive_check';
    case BrokenArchiveDown = 'solar-broken-archive_down';
    case BrokenArchiveDownMinimlistic = 'solar-broken-archive_down_minimlistic';
    case BrokenDocumentsMinimalistic = 'solar-broken-documents_minimalistic';
    case BrokenClipboardCheck = 'solar-broken-clipboard_check';
    case BrokenClipboardList = 'solar-broken-clipboard_list';
    case BrokenDocuments = 'solar-broken-Documents';
    case BrokenNotebook = 'solar-broken-Notebook';
    case BrokenGalleryRound = 'solar-broken-gallery_round';
    case BrokenPlayCircle = 'solar-broken-play_circle';
    case BrokenStream = 'solar-broken-Stream';
    case BrokenGalleryRemove = 'solar-broken-gallery_remove';
    case BrokenClapperboard = 'solar-broken-Clapperboard';
    case BrokenPauseCircle = 'solar-broken-pause_circle';
    case BrokenRewind5SecondsBack = 'solar-broken-rewind5_seconds_back';
    case BrokenRepeat = 'solar-broken-Repeat';
    case BrokenClapperboardEdit = 'solar-broken-clapperboard_edit';
    case BrokenVideoFrameCut = 'solar-broken-video_frame_cut';
    case BrokenPanorama = 'solar-broken-Panorama';
    case BrokenPlayStream = 'solar-broken-play_stream';
    case BrokenClapperboardOpen = 'solar-broken-clapperboard_open';
    case BrokenClapperboardText = 'solar-broken-clapperboard_text';
    case BrokenLibrary = 'solar-broken-Library';
    case BrokenReel2 = 'solar-broken-reel2';
    case BrokenVolumeSmall = 'solar-broken-volume_small';
    case BrokenVideoFrame = 'solar-broken-video_frame';
    case BrokenMicrophoneLarge = 'solar-broken-microphone_large';
    case BrokenRewindForward = 'solar-broken-rewind_forward';
    case BrokenRewindBackCircle = 'solar-broken-rewind_back_circle';
    case BrokenMicrophone = 'solar-broken-Microphone';
    case BrokenVideoFrameReplace = 'solar-broken-video_frame_replace';
    case BrokenClapperboardPlay = 'solar-broken-clapperboard_play';
    case BrokenGalleryDownload = 'solar-broken-gallery_download';
    case BrokenMusicNote4 = 'solar-broken-music_note4';
    case BrokenVideocameraRecord = 'solar-broken-videocamera_record';
    case BrokenPlaybackSpeed = 'solar-broken-playback_speed';
    case BrokenSoundwave = 'solar-broken-Soundwave';
    case BrokenStopCircle = 'solar-broken-stop_circle';
    case BrokenQuitFullScreenCircle = 'solar-broken-quit_full_screen_circle';
    case BrokenRewindBack = 'solar-broken-rewind_back';
    case BrokenRepeatOne = 'solar-broken-repeat_one';
    case BrokenGalleryCheck = 'solar-broken-gallery_check';
    case BrokenWallpaper = 'solar-broken-Wallpaper';
    case BrokenRewindForwardCircle = 'solar-broken-rewind_forward_circle';
    case BrokenGalleryEdit = 'solar-broken-gallery_edit';
    case BrokenGallery = 'solar-broken-Gallery';
    case BrokenGalleryMinimalistic = 'solar-broken-gallery_minimalistic';
    case BrokenUploadTrack = 'solar-broken-upload_track';
    case BrokenVolume = 'solar-broken-Volume';
    case BrokenUploadTrack2 = 'solar-broken-upload_track2';
    case BrokenMusicNotes = 'solar-broken-music_notes';
    case BrokenMusicNote2 = 'solar-broken-music_note2';
    case BrokenCameraAdd = 'solar-broken-camera_add';
    case BrokenPodcast = 'solar-broken-Podcast';
    case BrokenCameraRotate = 'solar-broken-camera_rotate';
    case BrokenMusicNote3 = 'solar-broken-music_note3';
    case BrokenStop = 'solar-broken-Stop';
    case BrokenMuted = 'solar-broken-Muted';
    case BrokenSkipNext = 'solar-broken-skip_next';
    case BrokenGallerySend = 'solar-broken-gallery_send';
    case BrokenRecord = 'solar-broken-Record';
    case BrokenFullScreenCircle = 'solar-broken-full_screen_circle';
    case BrokenVolumeCross = 'solar-broken-volume_cross';
    case BrokenSoundwaveCircle = 'solar-broken-soundwave_circle';
    case BrokenSkipPrevious = 'solar-broken-skip_previous';
    case BrokenRewind5SecondsForward = 'solar-broken-rewind5_seconds_forward';
    case BrokenPlay = 'solar-broken-Play';
    case BrokenPIP = 'solar-broken-PIP';
    case BrokenMusicLibrary = 'solar-broken-music_library';
    case BrokenVideoFrame2 = 'solar-broken-video_frame2';
    case BrokenCamera = 'solar-broken-Camera';
    case BrokenQuitPip = 'solar-broken-quit_pip';
    case BrokenClapperboardOpenPlay = 'solar-broken-clapperboard_open_play';
    case BrokenRewind10SecondsBack = 'solar-broken-rewind10_seconds_back';
    case BrokenRepeatOneMinimalistic = 'solar-broken-repeat_one_minimalistic';
    case BrokenVinyl = 'solar-broken-Vinyl';
    case BrokenVideoLibrary = 'solar-broken-video_library';
    case BrokenGalleryWide = 'solar-broken-gallery_wide';
    case BrokenReel = 'solar-broken-Reel';
    case BrokenToPip = 'solar-broken-to_pip';
    case BrokenPip2 = 'solar-broken-pip2';
    case BrokenFullScreen = 'solar-broken-full_screen';
    case BrokenCameraMinimalistic = 'solar-broken-camera_minimalistic';
    case BrokenVideoFrameCut2 = 'solar-broken-video_frame_cut2';
    case BrokenGalleryCircle = 'solar-broken-gallery_circle';
    case BrokenVideoFramePlayHorizontal = 'solar-broken-video_frame_play_horizontal';
    case BrokenMusicNoteSlider2 = 'solar-broken-music_note_slider2';
    case BrokenMusicNoteSlider = 'solar-broken-music_note_slider';
    case BrokenVideocameraAdd = 'solar-broken-videocamera_add';
    case BrokenQuitFullScreenSquare = 'solar-broken-quit_full_screen_square';
    case BrokenAlbum = 'solar-broken-Album';
    case BrokenGalleryAdd = 'solar-broken-gallery_add';
    case BrokenCameraSquare = 'solar-broken-camera_square';
    case BrokenRewind15SecondsBack = 'solar-broken-rewind15_seconds_back';
    case BrokenRewind15SecondsForward = 'solar-broken-rewind15_seconds_forward';
    case BrokenVinylRecord = 'solar-broken-vinyl_record';
    case BrokenShuffle = 'solar-broken-Shuffle';
    case BrokenPause = 'solar-broken-Pause';
    case BrokenMusicNote = 'solar-broken-music_note';
    case BrokenQuitFullScreen = 'solar-broken-quit_full_screen';
    case BrokenMicrophone2 = 'solar-broken-microphone2';
    case BrokenVideocamera = 'solar-broken-Videocamera';
    case BrokenGalleryFavourite = 'solar-broken-gallery_favourite';
    case BrokenMusicLibrary2 = 'solar-broken-music_library2';
    case BrokenVideoFramePlayVertical = 'solar-broken-video_frame_play_vertical';
    case BrokenFullScreenSquare = 'solar-broken-full_screen_square';
    case BrokenRewind10SecondsForward = 'solar-broken-rewind10_seconds_forward';
    case BrokenVolumeLoud = 'solar-broken-volume_loud';
    case BrokenMicrophone3 = 'solar-broken-microphone3';
    case BrokenSoundwaveSquare = 'solar-broken-soundwave_square';
    case BrokenCardholder = 'solar-broken-Cardholder';
    case BrokenBillList = 'solar-broken-bill_list';
    case BrokenSaleSquare = 'solar-broken-sale_square';
    case BrokenDollar = 'solar-broken-Dollar';
    case BrokenTicket = 'solar-broken-Ticket';
    case BrokenTag = 'solar-broken-Tag';
    case BrokenCashOut = 'solar-broken-cash_out';
    case BrokenWallet2 = 'solar-broken-wallet2';
    case BrokenRuble = 'solar-broken-Ruble';
    case BrokenCardTransfer = 'solar-broken-card_transfer';
    case BrokenEuro = 'solar-broken-Euro';
    case BrokenSale = 'solar-broken-Sale';
    case BrokenCardSearch = 'solar-broken-card_search';
    case BrokenWallet = 'solar-broken-Wallet';
    case BrokenBillCross = 'solar-broken-bill_cross';
    case BrokenTicketSale = 'solar-broken-ticket_sale';
    case BrokenSafeSquare = 'solar-broken-safe_square';
    case BrokenCard = 'solar-broken-Card';
    case BrokenSafe2 = 'solar-broken-safe2';
    case BrokenDollarMinimalistic = 'solar-broken-dollar_minimalistic';
    case BrokenTagPrice = 'solar-broken-tag_price';
    case BrokenMoneyBag = 'solar-broken-money_bag';
    case BrokenBill = 'solar-broken-Bill';
    case BrokenCardSend = 'solar-broken-card_send';
    case BrokenCardRecive = 'solar-broken-card_recive';
    case BrokenBanknote2 = 'solar-broken-banknote2';
    case BrokenTagHorizontal = 'solar-broken-tag_horizontal';
    case BrokenBillCheck = 'solar-broken-bill_check';
    case BrokenTickerStar = 'solar-broken-ticker_star';
    case BrokenBanknote = 'solar-broken-Banknote';
    case BrokenVerifiedCheck = 'solar-broken-verified_check';
    case BrokenWadOfMoney = 'solar-broken-wad_of_money';
    case BrokenCard2 = 'solar-broken-card2';
    case BrokenSafeCircle = 'solar-broken-safe_circle';
    case BrokenWalletMoney = 'solar-broken-wallet_money';
    case BrokenList = 'solar-broken-List';
    case BrokenListDownMinimalistic = 'solar-broken-list_down_minimalistic';
    case BrokenPlaylist2 = 'solar-broken-playlist2';
    case BrokenChecklistMinimalistic = 'solar-broken-checklist_minimalistic';
    case BrokenPlaaylistMinimalistic = 'solar-broken-plaaylist_minimalistic';
    case BrokenListHeart = 'solar-broken-list_heart';
    case BrokenListArrowDown = 'solar-broken-list_arrow_down';
    case BrokenListArrowUp = 'solar-broken-list_arrow_up';
    case BrokenListUpMinimalistic = 'solar-broken-list_up_minimalistic';
    case BrokenPlaylist = 'solar-broken-Playlist';
    case BrokenListUp = 'solar-broken-list_up';
    case BrokenListCrossMinimalistic = 'solar-broken-list_cross_minimalistic';
    case BrokenListCross = 'solar-broken-list_cross';
    case BrokenListArrowDownMinimalistic = 'solar-broken-list_arrow_down_minimalistic';
    case BrokenSortByAlphabet = 'solar-broken-sort_by_alphabet';
    case BrokenChecklist = 'solar-broken-Checklist';
    case BrokenSortFromBottomToTop = 'solar-broken-sort_from_bottom_to_top';
    case BrokenListCheck = 'solar-broken-list_check';
    case BrokenPlaylistMinimalistic2 = 'solar-broken-playlist_minimalistic2';
    case BrokenPlaylistMinimalistic3 = 'solar-broken-playlist_minimalistic3';
    case BrokenList1 = 'solar-broken-list1';
    case BrokenSortFromTopToBottom = 'solar-broken-sort_from_top_to_bottom';
    case BrokenSortByTime = 'solar-broken-sort_by_time';
    case BrokenListDown = 'solar-broken-list_down';
    case BrokenListHeartMinimalistic = 'solar-broken-list_heart_minimalistic';
    case BrokenListCheckMinimalistic = 'solar-broken-list_check_minimalistic';
    case BrokenListArrowUpMinimalistic = 'solar-broken-list_arrow_up_minimalistic';
    case BrokenVirus = 'solar-broken-Virus';
    case BrokenAdhesivePlaster2 = 'solar-broken-adhesive_plaster2';
    case BrokenDropper = 'solar-broken-Dropper';
    case BrokenPulse2 = 'solar-broken-pulse2';
    case BrokenBoneBroken = 'solar-broken-bone_broken';
    case BrokenHeartPulse2 = 'solar-broken-heart_pulse2';
    case BrokenMedicalKit = 'solar-broken-medical_kit';
    case BrokenTestTube = 'solar-broken-test_tube';
    case BrokenHealth = 'solar-broken-Health';
    case BrokenDropperMinimalistic2 = 'solar-broken-dropper_minimalistic2';
    case BrokenDNA = 'solar-broken-DNA';
    case BrokenDropper3 = 'solar-broken-dropper3';
    case BrokenThermometer = 'solar-broken-Thermometer';
    case BrokenDropper2 = 'solar-broken-dropper2';
    case BrokenJarOfPills2 = 'solar-broken-jar_of_pills2';
    case BrokenBoneCrack = 'solar-broken-bone_crack';
    case BrokenJarOfPills = 'solar-broken-jar_of_pills';
    case BrokenSyringe = 'solar-broken-Syringe';
    case BrokenStethoscope = 'solar-broken-Stethoscope';
    case BrokenBenzeneRing = 'solar-broken-benzene_ring';
    case BrokenBacteria = 'solar-broken-Bacteria';
    case BrokenAdhesivePlaster = 'solar-broken-adhesive_plaster';
    case BrokenBone = 'solar-broken-Bone';
    case BrokenBones = 'solar-broken-Bones';
    case BrokenPill = 'solar-broken-Pill';
    case BrokenPills = 'solar-broken-Pills';
    case BrokenHeartPulse = 'solar-broken-heart_pulse';
    case BrokenTestTubeMinimalistic = 'solar-broken-test_tube_minimalistic';
    case BrokenPills2 = 'solar-broken-pills2';
    case BrokenPulse = 'solar-broken-Pulse';
    case BrokenDropperMinimalistic = 'solar-broken-dropper_minimalistic';
    case BrokenPills3 = 'solar-broken-pills3';
    case BrokenWhisk = 'solar-broken-Whisk';
    case BrokenBottle = 'solar-broken-Bottle';
    case BrokenOvenMittsMinimalistic = 'solar-broken-oven_mitts_minimalistic';
    case BrokenChefHatMinimalistic = 'solar-broken-chef_hat_minimalistic';
    case BrokenTeaCup = 'solar-broken-tea_cup';
    case BrokenWineglassTriangle = 'solar-broken-wineglass_triangle';
    case BrokenOvenMitts = 'solar-broken-oven_mitts';
    case BrokenCupPaper = 'solar-broken-cup_paper';
    case BrokenLadle = 'solar-broken-Ladle';
    case BrokenCorkscrew = 'solar-broken-Corkscrew';
    case BrokenDonutBitten = 'solar-broken-donut_bitten';
    case BrokenWineglass = 'solar-broken-Wineglass';
    case BrokenDonut = 'solar-broken-Donut';
    case BrokenCupHot = 'solar-broken-cup_hot';
    case BrokenChefHatHeart = 'solar-broken-chef_hat_heart';
    case BrokenChefHat = 'solar-broken-chef_hat';
    case BrokenRollingPin = 'solar-broken-rolling_pin';
    case BrokenCodeFile = 'solar-broken-code_file';
    case BrokenFileCorrupted = 'solar-broken-file_corrupted';
    case BrokenFile = 'solar-broken-File';
    case BrokenFileRight = 'solar-broken-file_right';
    case BrokenFileFavourite = 'solar-broken-file_favourite';
    case BrokenFileDownload = 'solar-broken-file_download';
    case BrokenZipFile = 'solar-broken-zip_file';
    case BrokenFileText = 'solar-broken-file_text';
    case BrokenFileSmile = 'solar-broken-file_smile_)';
    case BrokenFileCheck = 'solar-broken-file_check';
    case BrokenFileSend = 'solar-broken-file_send';
    case BrokenFileLeft = 'solar-broken-file_left';
    case BrokenFigmaFile = 'solar-broken-figma_file';
    case BrokenFileRemove = 'solar-broken-file_remove';
    case BrokenCloudFile = 'solar-broken-cloud_file';
    case BrokenRemoveFolder = 'solar-broken-remove_folder';
    case BrokenFolderFavouritestar = 'solar-broken-folder_favourite(star)';
    case BrokenAddFolder = 'solar-broken-add_folder';
    case BrokenFolderCheck = 'solar-broken-folder_check';
    case BrokenFolderFavouritebookmark = 'solar-broken-folder_favourite(bookmark)';
    case BrokenFolder2 = 'solar-broken-folder2';
    case BrokenFolderSecurity = 'solar-broken-folder_security';
    case BrokenFolderCloud = 'solar-broken-folder_cloud';
    case BrokenMoveToFolder = 'solar-broken-move_to_folder';
    case BrokenFolderError = 'solar-broken-folder_error';
    case BrokenFolderPathConnect = 'solar-broken-folder_path_connect';
    case BrokenFolderOpen = 'solar-broken-folder_open';
    case BrokenFolder = 'solar-broken-Folder';
    case BrokenFolderWithFiles = 'solar-broken-folder_with_files';
    case BrokenCloudCheck = 'solar-broken-cloud_check';
    case BrokenTemperature = 'solar-broken-Temperature';
    case BrokenWind = 'solar-broken-Wind';
    case BrokenCloudSnowfall = 'solar-broken-cloud_snowfall';
    case BrokenSunrise = 'solar-broken-Sunrise';
    case BrokenSun2 = 'solar-broken-sun2';
    case BrokenCloudSun = 'solar-broken-cloud_sun';
    case BrokenCloudBoltMinimalistic = 'solar-broken-cloud_bolt_minimalistic';
    case BrokenCloudDownload = 'solar-broken-cloud_download';
    case BrokenClouds = 'solar-broken-Clouds';
    case BrokenTornado = 'solar-broken-Tornado';
    case BrokenMoonSleep = 'solar-broken-moon_sleep';
    case BrokenCloudUpload = 'solar-broken-cloud_upload';
    case BrokenCloudRain = 'solar-broken-cloud_rain';
    case BrokenFog = 'solar-broken-Fog';
    case BrokenSnowflake = 'solar-broken-Snowflake';
    case BrokenMoonFog = 'solar-broken-moon_fog';
    case BrokenCloudMinus = 'solar-broken-cloud_minus';
    case BrokenCloudBolt = 'solar-broken-cloud_bolt';
    case BrokenCloudWaterdrop = 'solar-broken-cloud_waterdrop';
    case BrokenSunset = 'solar-broken-Sunset';
    case BrokenWaterdrops = 'solar-broken-Waterdrops';
    case BrokenMoonStars = 'solar-broken-moon_stars';
    case BrokenCloudPlus = 'solar-broken-cloud_plus';
    case BrokenSun = 'solar-broken-Sun';
    case BrokenCloudWaterdrops = 'solar-broken-cloud_waterdrops';
    case BrokenCloudSun2 = 'solar-broken-cloud_sun2';
    case BrokenCloudyMoon = 'solar-broken-cloudy_moon';
    case BrokenTornadoSmall = 'solar-broken-tornado_small';
    case BrokenCloud = 'solar-broken-Cloud';
    case BrokenSunFog = 'solar-broken-sun_fog';
    case BrokenCloundCross = 'solar-broken-clound_cross';
    case BrokenCloudSnowfallMinimalistic = 'solar-broken-cloud_snowfall_minimalistic';
    case BrokenCloudStorm = 'solar-broken-cloud_storm';
    case BrokenMoon = 'solar-broken-Moon';
    case BrokenRefreshCircle = 'solar-broken-refresh_circle';
    case BrokenSquareArrowRightDown = 'solar-broken-square_arrow_right_down';
    case BrokenRoundArrowLeftDown = 'solar-broken-round_arrow_left_down';
    case BrokenRestart = 'solar-broken-Restart';
    case BrokenRoundAltArrowDown = 'solar-broken-round_alt_arrow_down';
    case BrokenRoundSortVertical = 'solar-broken-round_sort_vertical';
    case BrokenSquareAltArrowUp = 'solar-broken-square_alt_arrow_up';
    case BrokenArrowLeftUp = 'solar-broken-arrow_left_up';
    case BrokenSortHorizontal = 'solar-broken-sort_horizontal';
    case BrokenTransferHorizontal = 'solar-broken-transfer_horizontal';
    case BrokenSquareDoubleAltArrowUp = 'solar-broken-square_double_alt_arrow_up';
    case BrokenRoundArrowLeftUp = 'solar-broken-round_arrow_left_up';
    case BrokenAltArrowRight = 'solar-broken-alt_arrow_right';
    case BrokenRoundDoubleAltArrowUp = 'solar-broken-round_double_alt_arrow_up';
    case BrokenRestartCircle = 'solar-broken-restart_circle';
    case BrokenSquareArrowDown = 'solar-broken-square_arrow_down';
    case BrokenSortVertical = 'solar-broken-sort_vertical';
    case BrokenSquareSortHorizontal = 'solar-broken-square_sort_horizontal';
    case BrokenDoubleAltArrowLeft = 'solar-broken-double_alt_arrow_left';
    case BrokenSquareAltArrowDown = 'solar-broken-square_alt_arrow_down';
    case BrokenSquareAltArrowRight = 'solar-broken-square_alt_arrow_right';
    case BrokenSquareArrowUp = 'solar-broken-square_arrow_up';
    case BrokenDoubleAltArrowRight = 'solar-broken-double_alt_arrow_right';
    case BrokenRoundTransferVertical = 'solar-broken-round_transfer_vertical';
    case BrokenArrowLeft = 'solar-broken-arrow_left';
    case BrokenRoundDoubleAltArrowRight = 'solar-broken-round_double_alt_arrow_right';
    case BrokenSquareDoubleAltArrowLeft = 'solar-broken-square_double_alt_arrow_left';
    case BrokenAltArrowDown = 'solar-broken-alt_arrow_down';
    case BrokenRoundTransferHorizontal = 'solar-broken-round_transfer_horizontal';
    case BrokenRoundArrowRightDown = 'solar-broken-round_arrow_right_down';
    case BrokenArrowUp = 'solar-broken-arrow_up';
    case BrokenRoundArrowLeft = 'solar-broken-round_arrow_left';
    case BrokenDoubleAltArrowUp = 'solar-broken-double_alt_arrow_up';
    case BrokenRoundArrowRight = 'solar-broken-round_arrow_right';
    case BrokenSquareTransferHorizontal = 'solar-broken-square_transfer_horizontal';
    case BrokenArrowRight = 'solar-broken-arrow_right';
    case BrokenRoundDoubleAltArrowLeft = 'solar-broken-round_double_alt_arrow_left';
    case BrokenRoundArrowUp = 'solar-broken-round_arrow_up';
    case BrokenSquareSortVertical = 'solar-broken-square_sort_vertical';
    case BrokenAltArrowLeft = 'solar-broken-alt_arrow_left';
    case BrokenSquareDoubleAltArrowRight = 'solar-broken-square_double_alt_arrow_right';
    case BrokenRefresh = 'solar-broken-Refresh';
    case BrokenTransferVertical = 'solar-broken-transfer_vertical';
    case BrokenRefreshSquare = 'solar-broken-refresh_square';
    case BrokenSquareTransferVertical = 'solar-broken-square_transfer_vertical';
    case BrokenSquareDoubleAltArrowDown = 'solar-broken-square_double_alt_arrow_down';
    case BrokenRoundArrowRightUp = 'solar-broken-round_arrow_right_up';
    case BrokenArrowDown = 'solar-broken-arrow_down';
    case BrokenRestartSquare = 'solar-broken-restart_square';
    case BrokenSquareArrowRight = 'solar-broken-square_arrow_right';
    case BrokenRoundDoubleAltArrowDown = 'solar-broken-round_double_alt_arrow_down';
    case BrokenSquareArrowLeftUp = 'solar-broken-square_arrow_left_up';
    case BrokenRoundArrowDown = 'solar-broken-round_arrow_down';
    case BrokenSquareArrowRightUp = 'solar-broken-square_arrow_right_up';
    case BrokenRoundTransferDiagonal = 'solar-broken-round_transfer_diagonal';
    case BrokenArrowRightDown = 'solar-broken-arrow_right_down';
    case BrokenArrowLeftDown = 'solar-broken-arrow_left_down';
    case BrokenRoundAltArrowLeft = 'solar-broken-round_alt_arrow_left';
    case BrokenArrowRightUp = 'solar-broken-arrow_right_up';
    case BrokenSquareArrowLeftDown = 'solar-broken-square_arrow_left_down';
    case BrokenRoundAltArrowUp = 'solar-broken-round_alt_arrow_up';
    case BrokenAltArrowUp = 'solar-broken-alt_arrow_up';
    case BrokenSquareAltArrowLeft = 'solar-broken-square_alt_arrow_left';
    case BrokenRoundSortHorizontal = 'solar-broken-round_sort_horizontal';
    case BrokenDoubleAltArrowDown = 'solar-broken-double_alt_arrow_down';
    case BrokenRoundAltArrowRight = 'solar-broken-round_alt_arrow_right';
    case BrokenSquareArrowLeft = 'solar-broken-square_arrow_left';
    case BrokenTuningSquare2 = 'solar-broken-tuning_square2';
    case BrokenWidgetAdd = 'solar-broken-widget_add';
    case BrokenTuningSquare = 'solar-broken-tuning_square';
    case BrokenSettingsMinimalistic = 'solar-broken-settings_minimalistic';
    case BrokenWidget6 = 'solar-broken-widget6';
    case BrokenWidget4 = 'solar-broken-widget4';
    case BrokenSettings = 'solar-broken-Settings';
    case BrokenWidget5 = 'solar-broken-widget5';
    case BrokenWidget2 = 'solar-broken-widget2';
    case BrokenWidget3 = 'solar-broken-widget3';
    case BrokenTuning2 = 'solar-broken-tuning2';
    case BrokenTuning3 = 'solar-broken-tuning3';
    case BrokenWidget = 'solar-broken-Widget';
    case BrokenTuning4 = 'solar-broken-tuning4';
    case BrokenTuning = 'solar-broken-Tuning';
    case BrokenDiagramDown = 'solar-broken-diagram_down';
    case BrokenChart2 = 'solar-broken-chart2';
    case BrokenChart = 'solar-broken-Chart';
    case BrokenDiagramUp = 'solar-broken-diagram_up';
    case BrokenGraphNew = 'solar-broken-graph_new';
    case BrokenCourseUp = 'solar-broken-course_up';
    case BrokenGraphDownNew = 'solar-broken-graph_down_new';
    case BrokenPieChart3 = 'solar-broken-pie_chart3';
    case BrokenPieChart2 = 'solar-broken-pie_chart2';
    case BrokenGraphNewUp = 'solar-broken-graph_new_up';
    case BrokenPieChart = 'solar-broken-pie_chart';
    case BrokenRoundGraph = 'solar-broken-round_graph';
    case BrokenGraphUp = 'solar-broken-graph_up';
    case BrokenChartSquare = 'solar-broken-chart_square';
    case BrokenCourseDown = 'solar-broken-course_down';
    case BrokenChatSquare2 = 'solar-broken-chat_square2';
    case BrokenGraphDown = 'solar-broken-graph_down';
    case BrokenGraph = 'solar-broken-Graph';
    case BrokenPresentationGraph = 'solar-broken-presentation_graph';
    case BrokenMaximizeSquare3 = 'solar-broken-maximize_square3';
    case BrokenMaximizeSquareMinimalistic = 'solar-broken-maximize_square_minimalistic';
    case BrokenMaximizeSquare2 = 'solar-broken-maximize_square2';
    case BrokenMinimizeSquare = 'solar-broken-minimize_square';
    case BrokenDownloadSquare = 'solar-broken-download_square';
    case BrokenUndoLeftRoundSquare = 'solar-broken-undo_left_round_square';
    case BrokenReply = 'solar-broken-Reply';
    case BrokenLogout = 'solar-broken-Logout';
    case BrokenReciveSquare = 'solar-broken-recive_square';
    case BrokenExport = 'solar-broken-Export';
    case BrokenSendTwiceSquare = 'solar-broken-send_twice_square';
    case BrokenUndoLeftRound = 'solar-broken-undo_left_round';
    case BrokenForward2 = 'solar-broken-forward2';
    case BrokenMaximize = 'solar-broken-Maximize';
    case BrokenUndoRightRound = 'solar-broken-undo_right_round';
    case BrokenMinimizeSquare2 = 'solar-broken-minimize_square2';
    case BrokenMinimizeSquare3 = 'solar-broken-minimize_square3';
    case BrokenUploadTwiceSquare = 'solar-broken-upload_twice_square';
    case BrokenMinimize = 'solar-broken-Minimize';
    case BrokenCircleTopUp = 'solar-broken-circle_top_up';
    case BrokenUploadMinimalistic = 'solar-broken-upload_minimalistic';
    case BrokenDownload = 'solar-broken-Download';
    case BrokenImport = 'solar-broken-Import';
    case BrokenLogin = 'solar-broken-Login';
    case BrokenUndoLeft = 'solar-broken-undo_left';
    case BrokenSquareTopUp = 'solar-broken-square_top_up';
    case BrokenDownloadTwiceSquare = 'solar-broken-download_twice_square';
    case BrokenCircleBottomDown = 'solar-broken-circle_bottom_down';
    case BrokenMaximizeSquare = 'solar-broken-maximize_square';
    case BrokenUploadSquare = 'solar-broken-upload_square';
    case BrokenUndoRightSquare = 'solar-broken-undo_right_square';
    case BrokenReciveTwiceSquare = 'solar-broken-recive_twice_square';
    case BrokenCircleTopDown = 'solar-broken-circle_top_down';
    case BrokenArrowToDownLeft = 'solar-broken-arrow_to_down_left';
    case BrokenLogout2 = 'solar-broken-logout2';
    case BrokenLogout3 = 'solar-broken-logout3';
    case BrokenScale = 'solar-broken-Scale';
    case BrokenArrowToDownRight = 'solar-broken-arrow_to_down_right';
    case BrokenDownloadMinimalistic = 'solar-broken-download_minimalistic';
    case BrokenMinimizeSquareMinimalistic = 'solar-broken-minimize_square_minimalistic';
    case BrokenReply2 = 'solar-broken-reply2';
    case BrokenSquareBottomUp = 'solar-broken-square_bottom_up';
    case BrokenUndoRight = 'solar-broken-undo_right';
    case BrokenUndoLeftSquare = 'solar-broken-undo_left_square';
    case BrokenSendSquare = 'solar-broken-send_square';
    case BrokenExit = 'solar-broken-Exit';
    case BrokenSquareBottomDown = 'solar-broken-square_bottom_down';
    case BrokenUndoRightRoundSquare = 'solar-broken-undo_right_round_square';
    case BrokenArrowToTopLeft = 'solar-broken-arrow_to_top_left';
    case BrokenCircleBottomUp = 'solar-broken-circle_bottom_up';
    case BrokenScreenShare = 'solar-broken-screen_share';
    case BrokenUpload = 'solar-broken-Upload';
    case BrokenSquareTopDown = 'solar-broken-square_top_down';
    case BrokenArrowToTopRight = 'solar-broken-arrow_to_top_right';
    case BrokenLogin3 = 'solar-broken-login3';
    case BrokenLogin2 = 'solar-broken-login2';
    case BrokenPassport = 'solar-broken-Passport';
    case BrokenDiplomaVerified = 'solar-broken-diploma_verified';
    case BrokenCaseRound = 'solar-broken-case_round';
    case BrokenBackpack = 'solar-broken-Backpack';
    case BrokenBook2 = 'solar-broken-book2';
    case BrokenSquareAcademicCap2 = 'solar-broken-square_academic_cap2';
    case BrokenCaseRoundMinimalistic = 'solar-broken-case_round_minimalistic';
    case BrokenCase = 'solar-broken-Case';
    case BrokenBookBookmarkMinimalistic = 'solar-broken-book_bookmark_minimalistic';
    case BrokenBookmarkOpened = 'solar-broken-bookmark_opened';
    case BrokenDiploma = 'solar-broken-Diploma';
    case BrokenBook = 'solar-broken-Book';
    case BrokenSquareAcademicCap = 'solar-broken-square_academic_cap';
    case BrokenBookmarkCircle = 'solar-broken-bookmark_circle';
    case BrokenCalculatorMinimalistic = 'solar-broken-calculator_minimalistic';
    case BrokenNotebookSquare = 'solar-broken-notebook_square';
    case BrokenBookMinimalistic = 'solar-broken-book_minimalistic';
    case BrokenCaseMinimalistic = 'solar-broken-case_minimalistic';
    case BrokenNotebookBookmark = 'solar-broken-notebook_bookmark';
    case BrokenPassportMinimalistic = 'solar-broken-passport_minimalistic';
    case BrokenBookBookmark = 'solar-broken-book_bookmark';
    case BrokenBookmarkSquareMinimalistic = 'solar-broken-bookmark_square_minimalistic';
    case BrokenBookmark = 'solar-broken-Bookmark';
    case BrokenPlusMinus = 'solar-broken-plus,_minus';
    case BrokenCalculator = 'solar-broken-Calculator';
    case BrokenBookmarkSquare = 'solar-broken-bookmark_square';
    case BrokenNotebookMinimalistic = 'solar-broken-notebook_minimalistic';
    case BrokenFireSquare = 'solar-broken-fire_square';
    case BrokenSuitcaseLines = 'solar-broken-suitcase_lines';
    case BrokenFire = 'solar-broken-Fire';
    case BrokenBonfire = 'solar-broken-Bonfire';
    case BrokenSuitcaseTag = 'solar-broken-suitcase_tag';
    case BrokenLeaf = 'solar-broken-Leaf';
    case BrokenSuitcase = 'solar-broken-Suitcase';
    case BrokenFlame = 'solar-broken-Flame';
    case BrokenFireMinimalistic = 'solar-broken-fire_minimalistic';
    case BrokenBellBing = 'solar-broken-bell_bing';
    case BrokenNotificationLinesRemove = 'solar-broken-notification_lines_remove';
    case BrokenNotificationUnread = 'solar-broken-notification_unread';
    case BrokenBell = 'solar-broken-Bell';
    case BrokenNotificationRemove = 'solar-broken-notification_remove';
    case BrokenNotificationUnreadLines = 'solar-broken-notification_unread_lines';
    case BrokenBellOff = 'solar-broken-bell_off';
    case BrokenLightning = 'solar-broken-Lightning';
    case BrokenLightbulbMinimalistic = 'solar-broken-lightbulb_minimalistic';
    case BrokenServerSquareCloud = 'solar-broken-server_square_cloud';
    case BrokenLightbulbBolt = 'solar-broken-lightbulb_bolt';
    case BrokenAirbudsCharge = 'solar-broken-airbuds_charge';
    case BrokenServerPath = 'solar-broken-server_path';
    case BrokenSimCardMinimalistic = 'solar-broken-sim_card_minimalistic';
    case BrokenSmartphone = 'solar-broken-Smartphone';
    case BrokenTurntable = 'solar-broken-Turntable';
    case BrokenAirbudsCheck = 'solar-broken-airbuds_check';
    case BrokenMouseMinimalistic = 'solar-broken-mouse_minimalistic';
    case BrokenSmartphoneRotateAngle = 'solar-broken-smartphone_rotate_angle';
    case BrokenRadioMinimalistic = 'solar-broken-radio_minimalistic';
    case BrokenAirbuds = 'solar-broken-Airbuds';
    case BrokenSmartphoneRotateOrientation = 'solar-broken-smartphone_rotate_orientation';
    case BrokenIPhone = 'solar-broken-i_phone';
    case BrokenSimCard = 'solar-broken-sim_card';
    case BrokenFlashDrive = 'solar-broken-flash_drive';
    case BrokenDevices = 'solar-broken-Devices';
    case BrokenSimCards = 'solar-broken-sim_cards';
    case BrokenAirbudsCaseOpen = 'solar-broken-airbuds_case_open';
    case BrokenTurntableMusicNote = 'solar-broken-turntable_music_note';
    case BrokenKeyboard = 'solar-broken-Keyboard';
    case BrokenGamepadCharge = 'solar-broken-gamepad_charge';
    case BrokenBoombox = 'solar-broken-Boombox';
    case BrokenSmartSpeakerMinimalistic = 'solar-broken-smart_speaker_minimalistic';
    case BrokenTelescope = 'solar-broken-Telescope';
    case BrokenMonitorCamera = 'solar-broken-monitor_camera';
    case BrokenLaptopMinimalistic = 'solar-broken-laptop_minimalistic';
    case BrokenServer2 = 'solar-broken-server2';
    case BrokenSmartSpeaker = 'solar-broken-smart_speaker';
    case BrokenProjector = 'solar-broken-Projector';
    case BrokenServer = 'solar-broken-Server';
    case BrokenTV = 'solar-broken-TV';
    case BrokenCassette2 = 'solar-broken-cassette2';
    case BrokenRadio = 'solar-broken-Radio';
    case BrokenSmartphoneVibration = 'solar-broken-smartphone_vibration';
    case BrokenAirbudsLeft = 'solar-broken-airbuds_left';
    case BrokenHeadphonesRound = 'solar-broken-headphones_round';
    case BrokenGameboy = 'solar-broken-Gameboy';
    case BrokenHeadphonesRoundSound = 'solar-broken-headphones_round_sound';
    case BrokenCPU = 'solar-broken-CPU';
    case BrokenPrinter2 = 'solar-broken-printer2';
    case BrokenHeadphonesSquare = 'solar-broken-headphones_square';
    case BrokenServerSquareUpdate = 'solar-broken-server_square_update';
    case BrokenPrinterMinimalistic = 'solar-broken-printer_minimalistic';
    case BrokenBluetooth = 'solar-broken-Bluetooth';
    case BrokenWirelessCharge = 'solar-broken-wireless_charge';
    case BrokenBluetoothCircle = 'solar-broken-bluetooth_circle';
    case BrokenAirbudsCaseMinimalistic = 'solar-broken-airbuds_case_minimalistic';
    case BrokenLightbulb = 'solar-broken-Lightbulb';
    case BrokenAirbudsRemove = 'solar-broken-airbuds_remove';
    case BrokenSmartphoneRotate2 = 'solar-broken-smartphone_rotate2';
    case BrokenSsdSquare = 'solar-broken-ssd_square';
    case BrokenPrinter = 'solar-broken-Printer';
    case BrokenSmartphone2 = 'solar-broken-smartphone2';
    case BrokenServerMinimalistic = 'solar-broken-server_minimalistic';
    case BrokenHeadphonesSquareSound = 'solar-broken-headphones_square_sound';
    case BrokenDiskette = 'solar-broken-Diskette';
    case BrokenBluetoothWave = 'solar-broken-bluetooth_wave';
    case BrokenSmartSpeaker2 = 'solar-broken-smart_speaker2';
    case BrokenLaptop3 = 'solar-broken-laptop3';
    case BrokenLaptop2 = 'solar-broken-laptop2';
    case BrokenMouseCircle = 'solar-broken-mouse_circle';
    case BrokenTurntableMinimalistic = 'solar-broken-turntable_minimalistic';
    case BrokenSmartphoneUpdate = 'solar-broken-smartphone_update';
    case BrokenGamepadMinimalistic = 'solar-broken-gamepad_minimalistic';
    case BrokenSdCard = 'solar-broken-sd_card';
    case BrokenPlugCircle = 'solar-broken-plug_circle';
    case BrokenAirbudsCase = 'solar-broken-airbuds_case';
    case BrokenSsdRound = 'solar-broken-ssd_round';
    case BrokenLaptop = 'solar-broken-Laptop';
    case BrokenAirbudsRight = 'solar-broken-airbuds_right';
    case BrokenDisplay = 'solar-broken-Display';
    case BrokenMonitorSmartphone = 'solar-broken-monitor_smartphone';
    case BrokenSocket = 'solar-broken-Socket';
    case BrokenGamepadOld = 'solar-broken-gamepad_old';
    case BrokenCpuBolt = 'solar-broken-cpu_bolt';
    case BrokenAirbudsCaseCharge = 'solar-broken-airbuds_case_charge';
    case BrokenTablet = 'solar-broken-Tablet';
    case BrokenWeigher = 'solar-broken-Weigher';
    case BrokenServerSquare = 'solar-broken-server_square';
    case BrokenMouse = 'solar-broken-Mouse';
    case BrokenGamepadNoCharge = 'solar-broken-gamepad_no_charge';
    case BrokenBluetoothSquare = 'solar-broken-bluetooth_square';
    case BrokenCloudStorage = 'solar-broken-cloud_storage';
    case BrokenGamepad = 'solar-broken-Gamepad';
    case BrokenMonitor = 'solar-broken-Monitor';
    case BrokenCassette = 'solar-broken-Cassette';
    // Bold Duotone Style (1205 icons)
    case BoldduotoneFacemaskCircle = 'solar-bold-duotone-facemask_circle';
    case BoldduotoneConfoundedCircle = 'solar-bold-duotone-confounded_circle';
    case BoldduotoneSadSquare = 'solar-bold-duotone-sad_square';
    case BoldduotoneSleepingCircle = 'solar-bold-duotone-sleeping_circle';
    case BoldduotoneFaceScanCircle = 'solar-bold-duotone-face_scan_circle';
    case BoldduotoneSmileCircle = 'solar-bold-duotone-smile_circle';
    case BoldduotoneStickerSmileCircle = 'solar-bold-duotone-sticker_smile_circle';
    case BoldduotoneStickerSquare = 'solar-bold-duotone-sticker_square';
    case BoldduotoneEmojiFunnyCircle = 'solar-bold-duotone-emoji_funny_circle';
    case BoldduotoneExpressionlessSquare = 'solar-bold-duotone-expressionless_square';
    case BoldduotoneSleepingSquare = 'solar-bold-duotone-sleeping_square';
    case BoldduotoneSadCircle = 'solar-bold-duotone-sad_circle';
    case BoldduotoneFacemaskSquare = 'solar-bold-duotone-facemask_square';
    case BoldduotoneConfoundedSquare = 'solar-bold-duotone-confounded_square';
    case BoldduotoneFaceScanSquare = 'solar-bold-duotone-face_scan_square';
    case BoldduotoneSmileSquare = 'solar-bold-duotone-smile_square';
    case BoldduotoneStickerSmileCircle2 = 'solar-bold-duotone-sticker_smile_circle2';
    case BoldduotoneStickerSmileSquare = 'solar-bold-duotone-sticker_smile_square';
    case BoldduotoneEmojiFunnySquare = 'solar-bold-duotone-emoji_funny_square';
    case BoldduotoneStickerCircle = 'solar-bold-duotone-sticker_circle';
    case BoldduotoneExpressionlessCircle = 'solar-bold-duotone-expressionless_circle';
    case BoldduotoneLike = 'solar-bold-duotone-Like';
    case BoldduotoneMedalStarSquare = 'solar-bold-duotone-medal_star_square';
    case BoldduotoneDislike = 'solar-bold-duotone-Dislike';
    case BoldduotoneStarShine = 'solar-bold-duotone-star_shine';
    case BoldduotoneHeartAngle = 'solar-bold-duotone-heart_angle';
    case BoldduotoneMedalRibbon = 'solar-bold-duotone-medal_ribbon';
    case BoldduotoneHeartShine = 'solar-bold-duotone-heart_shine';
    case BoldduotoneMedalStarCircle = 'solar-bold-duotone-medal_star_circle';
    case BoldduotoneMedalRibbonsStar = 'solar-bold-duotone-medal_ribbons_star';
    case BoldduotoneStar = 'solar-bold-duotone-Star';
    case BoldduotoneHeartUnlock = 'solar-bold-duotone-heart_unlock';
    case BoldduotoneMedalRibbonStar = 'solar-bold-duotone-medal_ribbon_star';
    case BoldduotoneHeartLock = 'solar-bold-duotone-heart_lock';
    case BoldduotoneHeartBroken = 'solar-bold-duotone-heart_broken';
    case BoldduotoneHearts = 'solar-bold-duotone-Hearts';
    case BoldduotoneMedalStar = 'solar-bold-duotone-medal_star';
    case BoldduotoneHeart = 'solar-bold-duotone-Heart';
    case BoldduotoneCloset = 'solar-bold-duotone-Closet';
    case BoldduotoneBed = 'solar-bold-duotone-Bed';
    case BoldduotoneWashingMachine = 'solar-bold-duotone-washing_machine';
    case BoldduotoneBedsideTable = 'solar-bold-duotone-bedside_table';
    case BoldduotoneSofa3 = 'solar-bold-duotone-sofa3';
    case BoldduotoneSofa2 = 'solar-bold-duotone-sofa2';
    case BoldduotoneChair2 = 'solar-bold-duotone-chair2';
    case BoldduotoneBath = 'solar-bold-duotone-Bath';
    case BoldduotoneSmartVacuumCleaner2 = 'solar-bold-duotone-smart_vacuum_cleaner2';
    case BoldduotoneCondicioner = 'solar-bold-duotone-Condicioner';
    case BoldduotoneSmartVacuumCleaner = 'solar-bold-duotone-smart_vacuum_cleaner';
    case BoldduotoneRemoteController2 = 'solar-bold-duotone-remote_controller2';
    case BoldduotoneFloorLampMinimalistic = 'solar-bold-duotone-floor_lamp_minimalistic';
    case BoldduotoneLamp = 'solar-bold-duotone-Lamp';
    case BoldduotoneBarChair = 'solar-bold-duotone-bar_chair';
    case BoldduotoneBedsideTable2 = 'solar-bold-duotone-bedside_table2';
    case BoldduotoneCloset2 = 'solar-bold-duotone-closet2';
    case BoldduotoneBedsideTable3 = 'solar-bold-duotone-bedside_table3';
    case BoldduotoneSpeaker = 'solar-bold-duotone-Speaker';
    case BoldduotoneVolumeKnob = 'solar-bold-duotone-volume_knob';
    case BoldduotoneArmchair = 'solar-bold-duotone-Armchair';
    case BoldduotoneSpeakerMinimalistic = 'solar-bold-duotone-speaker_minimalistic';
    case BoldduotoneRemoteController = 'solar-bold-duotone-remote_controller';
    case BoldduotoneTrellis = 'solar-bold-duotone-Trellis';
    case BoldduotoneFloorLamp = 'solar-bold-duotone-floor_lamp';
    case BoldduotoneCondicioner2 = 'solar-bold-duotone-condicioner2';
    case BoldduotoneBedsideTable4 = 'solar-bold-duotone-bedside_table4';
    case BoldduotoneArmchair2 = 'solar-bold-duotone-armchair2';
    case BoldduotoneWashingMachineMinimalistic = 'solar-bold-duotone-washing_machine_minimalistic';
    case BoldduotoneChair = 'solar-bold-duotone-Chair';
    case BoldduotoneRemoteControllerMinimalistic = 'solar-bold-duotone-remote_controller_minimalistic';
    case BoldduotoneChandelier = 'solar-bold-duotone-Chandelier';
    case BoldduotoneFridge = 'solar-bold-duotone-Fridge';
    case BoldduotoneMirror = 'solar-bold-duotone-Mirror';
    case BoldduotoneSofa = 'solar-bold-duotone-Sofa';
    case BoldduotoneEarth = 'solar-bold-duotone-Earth';
    case BoldduotoneStarsLine = 'solar-bold-duotone-stars_line';
    case BoldduotoneStarFall2 = 'solar-bold-duotone-star_fall2';
    case BoldduotoneStarFall = 'solar-bold-duotone-star_fall';
    case BoldduotoneBlackHole3 = 'solar-bold-duotone-black_hole3';
    case BoldduotoneWomen = 'solar-bold-duotone-Women';
    case BoldduotoneBlackHole = 'solar-bold-duotone-black_hole';
    case BoldduotoneStarRings = 'solar-bold-duotone-star_rings';
    case BoldduotoneBlackHole2 = 'solar-bold-duotone-black_hole2';
    case BoldduotoneStarFallMinimalistic2 = 'solar-bold-duotone-star_fall_minimalistic2';
    case BoldduotonePlanet = 'solar-bold-duotone-Planet';
    case BoldduotoneSatellite = 'solar-bold-duotone-Satellite';
    case BoldduotoneMen = 'solar-bold-duotone-Men';
    case BoldduotoneRocket2 = 'solar-bold-duotone-rocket2';
    case BoldduotoneStars = 'solar-bold-duotone-Stars';
    case BoldduotoneStarAngle = 'solar-bold-duotone-star_angle';
    case BoldduotoneInfinity = 'solar-bold-duotone-Infinity';
    case BoldduotoneUfo2 = 'solar-bold-duotone-ufo2';
    case BoldduotoneUfo3 = 'solar-bold-duotone-ufo3';
    case BoldduotoneStarRing = 'solar-bold-duotone-star_ring';
    case BoldduotonePlanet2 = 'solar-bold-duotone-planet2';
    case BoldduotonePlanet3 = 'solar-bold-duotone-planet3';
    case BoldduotoneAsteroid = 'solar-bold-duotone-Asteroid';
    case BoldduotoneStarsMinimalistic = 'solar-bold-duotone-stars_minimalistic';
    case BoldduotoneUFO = 'solar-bold-duotone-UFO';
    case BoldduotonePlanet4 = 'solar-bold-duotone-planet4';
    case BoldduotoneRocket = 'solar-bold-duotone-Rocket';
    case BoldduotoneStarFallMinimalistic = 'solar-bold-duotone-star_fall_minimalistic';
    case BoldduotoneStarRainbow = 'solar-bold-duotone-star_rainbow';
    case BoldduotoneAtom = 'solar-bold-duotone-Atom';
    case BoldduotoneStarCircle = 'solar-bold-duotone-star_circle';
    case BoldduotoneCompassBig = 'solar-bold-duotone-compass_big';
    case BoldduotoneMapPointSchool = 'solar-bold-duotone-map_point_school';
    case BoldduotoneSignpost = 'solar-bold-duotone-Signpost';
    case BoldduotoneMapArrowDown = 'solar-bold-duotone-map_arrow_down';
    case BoldduotoneMap = 'solar-bold-duotone-Map';
    case BoldduotoneMapArrowUp = 'solar-bold-duotone-map_arrow_up';
    case BoldduotonePointOnMapPerspective = 'solar-bold-duotone-point_on_map_perspective';
    case BoldduotoneRadar = 'solar-bold-duotone-Radar';
    case BoldduotoneStreets = 'solar-bold-duotone-Streets';
    case BoldduotoneMapPointWave = 'solar-bold-duotone-map_point_wave';
    case BoldduotonePeopleNearby = 'solar-bold-duotone-people_nearby';
    case BoldduotoneStreetsMapPoint = 'solar-bold-duotone-streets_map_point';
    case BoldduotoneMapPointSearch = 'solar-bold-duotone-map_point_search';
    case BoldduotoneGPS = 'solar-bold-duotone-GPS';
    case BoldduotoneMapArrowSquare = 'solar-bold-duotone-map_arrow_square';
    case BoldduotoneBranchingPathsDown = 'solar-bold-duotone-branching_paths_down';
    case BoldduotoneMapPointRotate = 'solar-bold-duotone-map_point_rotate';
    case BoldduotoneGlobal = 'solar-bold-duotone-Global';
    case BoldduotoneCompassSquare = 'solar-bold-duotone-compass_square';
    case BoldduotoneRouting3 = 'solar-bold-duotone-routing3';
    case BoldduotoneRouting2 = 'solar-bold-duotone-routing2';
    case BoldduotoneMapPointRemove = 'solar-bold-duotone-map_point_remove';
    case BoldduotoneGlobus = 'solar-bold-duotone-Globus';
    case BoldduotoneSignpost2 = 'solar-bold-duotone-signpost2';
    case BoldduotoneRadar2 = 'solar-bold-duotone-radar2';
    case BoldduotoneStreetsNavigation = 'solar-bold-duotone-streets_navigation';
    case BoldduotoneMapPoint = 'solar-bold-duotone-map_point';
    case BoldduotoneMapPointHospital = 'solar-bold-duotone-map_point_hospital';
    case BoldduotoneCompass = 'solar-bold-duotone-Compass';
    case BoldduotoneMapPointAdd = 'solar-bold-duotone-map_point_add';
    case BoldduotoneBranchingPathsUp = 'solar-bold-duotone-branching_paths_up';
    case BoldduotoneMapPointFavourite = 'solar-bold-duotone-map_point_favourite';
    case BoldduotoneRoute = 'solar-bold-duotone-Route';
    case BoldduotonePointOnMap = 'solar-bold-duotone-point_on_map';
    case BoldduotoneMapArrowRight = 'solar-bold-duotone-map_arrow_right';
    case BoldduotoneRouting = 'solar-bold-duotone-Routing';
    case BoldduotoneMapArrowLeft = 'solar-bold-duotone-map_arrow_left';
    case BoldduotoneIncognito = 'solar-bold-duotone-Incognito';
    case BoldduotoneLockPassword = 'solar-bold-duotone-lock_password';
    case BoldduotoneShieldNetwork = 'solar-bold-duotone-shield_network';
    case BoldduotoneKeyMinimalisticSquare = 'solar-bold-duotone-key_minimalistic_square';
    case BoldduotoneLockKeyholeUnlocked = 'solar-bold-duotone-lock_keyhole_unlocked';
    case BoldduotoneLock = 'solar-bold-duotone-Lock';
    case BoldduotoneShieldKeyhole = 'solar-bold-duotone-shield_keyhole';
    case BoldduotoneEyeClosed = 'solar-bold-duotone-eye_closed';
    case BoldduotoneKey = 'solar-bold-duotone-Key';
    case BoldduotoneShieldMinus = 'solar-bold-duotone-shield_minus';
    case BoldduotoneShield = 'solar-bold-duotone-Shield';
    case BoldduotoneLockUnlocked = 'solar-bold-duotone-lock_unlocked';
    case BoldduotoneBombMinimalistic = 'solar-bold-duotone-bomb_minimalistic';
    case BoldduotoneShieldStar = 'solar-bold-duotone-shield_star';
    case BoldduotoneBomb = 'solar-bold-duotone-Bomb';
    case BoldduotoneKeySquare = 'solar-bold-duotone-key_square';
    case BoldduotoneLockKeyholeMinimalisticUnlocked = 'solar-bold-duotone-lock_keyhole_minimalistic_unlocked';
    case BoldduotoneShieldCross = 'solar-bold-duotone-shield_cross';
    case BoldduotoneObjectScan = 'solar-bold-duotone-object_scan';
    case BoldduotonePasswordMinimalisticInput = 'solar-bold-duotone-password_minimalistic_input';
    case BoldduotoneLockPasswordUnlocked = 'solar-bold-duotone-lock_password_unlocked';
    case BoldduotoneSiren = 'solar-bold-duotone-Siren';
    case BoldduotoneShieldMinimalistic = 'solar-bold-duotone-shield_minimalistic';
    case BoldduotoneEyeScan = 'solar-bold-duotone-eye_scan';
    case BoldduotoneKeyMinimalisticSquare2 = 'solar-bold-duotone-key_minimalistic_square2';
    case BoldduotoneScanner2 = 'solar-bold-duotone-scanner2';
    case BoldduotoneKeyMinimalisticSquare3 = 'solar-bold-duotone-key_minimalistic_square3';
    case BoldduotoneKeyMinimalistic2 = 'solar-bold-duotone-key_minimalistic2';
    case BoldduotoneCodeScan = 'solar-bold-duotone-code_scan';
    case BoldduotoneShieldPlus = 'solar-bold-duotone-shield_plus';
    case BoldduotonePasswordMinimalistic = 'solar-bold-duotone-password_minimalistic';
    case BoldduotoneEye = 'solar-bold-duotone-Eye';
    case BoldduotoneQrCode = 'solar-bold-duotone-qr_code';
    case BoldduotoneShieldCheck = 'solar-bold-duotone-shield_check';
    case BoldduotoneKeyMinimalistic = 'solar-bold-duotone-key_minimalistic';
    case BoldduotoneLockKeyhole = 'solar-bold-duotone-lock_keyhole';
    case BoldduotoneShieldUser = 'solar-bold-duotone-shield_user';
    case BoldduotoneKeySquare2 = 'solar-bold-duotone-key_square2';
    case BoldduotoneBombEmoji = 'solar-bold-duotone-bomb_emoji';
    case BoldduotoneScanner = 'solar-bold-duotone-Scanner';
    case BoldduotoneShieldUp = 'solar-bold-duotone-shield_up';
    case BoldduotoneSirenRounded = 'solar-bold-duotone-siren_rounded';
    case BoldduotoneLockKeyholeMinimalistic = 'solar-bold-duotone-lock_keyhole_minimalistic';
    case BoldduotonePassword = 'solar-bold-duotone-Password';
    case BoldduotoneShieldKeyholeMinimalistic = 'solar-bold-duotone-shield_keyhole_minimalistic';
    case BoldduotoneShieldWarning = 'solar-bold-duotone-shield_warning';
    case BoldduotonePallete2 = 'solar-bold-duotone-pallete2';
    case BoldduotoneAlignVerticalSpacing = 'solar-bold-duotone-align_vertical_spacing';
    case BoldduotoneAlignVerticalCenter = 'solar-bold-duotone-align_vertical_center';
    case BoldduotoneCropMinimalistic = 'solar-bold-duotone-crop_minimalistic';
    case BoldduotoneMirrorRight = 'solar-bold-duotone-mirror_right';
    case BoldduotoneAlignBottom = 'solar-bold-duotone-align_bottom';
    case BoldduotoneRadialBlur = 'solar-bold-duotone-radial_blur';
    case BoldduotoneCrop = 'solar-bold-duotone-Crop';
    case BoldduotoneAlignHorizontaSpacing = 'solar-bold-duotone-align_horizonta_spacing';
    case BoldduotoneRulerPen = 'solar-bold-duotone-ruler_pen';
    case BoldduotoneThreeSquares = 'solar-bold-duotone-three_squares';
    case BoldduotonePaintRoller = 'solar-bold-duotone-paint_roller';
    case BoldduotoneLayers = 'solar-bold-duotone-Layers';
    case BoldduotoneFilters = 'solar-bold-duotone-Filters';
    case BoldduotoneRulerCrossPen = 'solar-bold-duotone-ruler_cross_pen';
    case BoldduotoneFlipHorizontal = 'solar-bold-duotone-flip_horizontal';
    case BoldduotoneAlignLeft = 'solar-bold-duotone-align_left';
    case BoldduotoneRuler = 'solar-bold-duotone-Ruler';
    case BoldduotonePalette = 'solar-bold-duotone-Palette';
    case BoldduotoneAlignTop = 'solar-bold-duotone-align_top';
    case BoldduotoneAlignHorizontalCenter = 'solar-bold-duotone-align_horizontal_center';
    case BoldduotoneAlignRight = 'solar-bold-duotone-align_right';
    case BoldduotoneRulerAngular = 'solar-bold-duotone-ruler_angular';
    case BoldduotonePipette = 'solar-bold-duotone-Pipette';
    case BoldduotoneFlipVertical = 'solar-bold-duotone-flip_vertical';
    case BoldduotoneMirrorLeft = 'solar-bold-duotone-mirror_left';
    case BoldduotoneLayersMinimalistic = 'solar-bold-duotone-layers_minimalistic';
    case BoldduotoneColourTuneing = 'solar-bold-duotone-colour_tuneing';
    case BoldduotonePaletteRound = 'solar-bold-duotone-palette_round';
    case BoldduotoneEraser = 'solar-bold-duotone-Eraser';
    case BoldduotoneTextItalicCircle = 'solar-bold-duotone-text_italic_circle';
    case BoldduotoneLinkRound = 'solar-bold-duotone-link_round';
    case BoldduotoneTextItalic = 'solar-bold-duotone-text_italic';
    case BoldduotoneLinkBrokenMinimalistic = 'solar-bold-duotone-link_broken_minimalistic';
    case BoldduotoneTextUnderlineCross = 'solar-bold-duotone-text_underline_cross';
    case BoldduotoneLink = 'solar-bold-duotone-Link';
    case BoldduotoneEraserCircle = 'solar-bold-duotone-eraser_circle';
    case BoldduotoneLinkCircle = 'solar-bold-duotone-link_circle';
    case BoldduotoneTextBoldCircle = 'solar-bold-duotone-text_bold_circle';
    case BoldduotoneTextField = 'solar-bold-duotone-text_field';
    case BoldduotoneTextSquare = 'solar-bold-duotone-text_square';
    case BoldduotoneTextSquare2 = 'solar-bold-duotone-text_square2';
    case BoldduotoneLinkRoundAngle = 'solar-bold-duotone-link_round_angle';
    case BoldduotoneTextUnderlineCircle = 'solar-bold-duotone-text_underline_circle';
    case BoldduotoneTextCrossCircle = 'solar-bold-duotone-text_cross_circle';
    case BoldduotoneTextItalicSquare = 'solar-bold-duotone-text_italic_square';
    case BoldduotoneParagraphSpacing = 'solar-bold-duotone-paragraph_spacing';
    case BoldduotoneText = 'solar-bold-duotone-Text';
    case BoldduotoneLinkBroken = 'solar-bold-duotone-link_broken';
    case BoldduotoneTextCross = 'solar-bold-duotone-text_cross';
    case BoldduotoneTextUnderline = 'solar-bold-duotone-text_underline';
    case BoldduotoneLinkMinimalistic = 'solar-bold-duotone-link_minimalistic';
    case BoldduotoneLinkMinimalistic2 = 'solar-bold-duotone-link_minimalistic2';
    case BoldduotoneTextBold = 'solar-bold-duotone-text_bold';
    case BoldduotoneTextSelection = 'solar-bold-duotone-text_selection';
    case BoldduotoneTextFieldFocus = 'solar-bold-duotone-text_field_focus';
    case BoldduotoneTextBoldSquare = 'solar-bold-duotone-text_bold_square';
    case BoldduotoneEraserSquare = 'solar-bold-duotone-eraser_square';
    case BoldduotoneLinkSquare = 'solar-bold-duotone-link_square';
    case BoldduotoneTextCircle = 'solar-bold-duotone-text_circle';
    case BoldduotoneBackspace = 'solar-bold-duotone-Backspace';
    case BoldduotoneTextCrossSquare = 'solar-bold-duotone-text_cross_square';
    case BoldduotoneInboxUnread = 'solar-bold-duotone-inbox_unread';
    case BoldduotoneChatUnread = 'solar-bold-duotone-chat_unread';
    case BoldduotoneChatRound = 'solar-bold-duotone-chat_round';
    case BoldduotoneUnread = 'solar-bold-duotone-Unread';
    case BoldduotoneMailbox = 'solar-bold-duotone-Mailbox';
    case BoldduotoneLetter = 'solar-bold-duotone-Letter';
    case BoldduotonePenNewRound = 'solar-bold-duotone-pen_new_round';
    case BoldduotoneMultipleForwardRight = 'solar-bold-duotone-multiple_forward_right';
    case BoldduotoneMultipleForwardLeft = 'solar-bold-duotone-multiple_forward_left';
    case BoldduotoneInboxArchive = 'solar-bold-duotone-inbox_archive';
    case BoldduotoneInbox = 'solar-bold-duotone-Inbox';
    case BoldduotonePen2 = 'solar-bold-duotone-pen2';
    case BoldduotonePenNewSquare = 'solar-bold-duotone-pen_new_square';
    case BoldduotonePen = 'solar-bold-duotone-Pen';
    case BoldduotoneChatDots = 'solar-bold-duotone-chat_dots';
    case BoldduotoneChatSquareCall = 'solar-bold-duotone-chat_square_call';
    case BoldduotoneSquareShareLine = 'solar-bold-duotone-square_share_line';
    case BoldduotoneChatRoundCheck = 'solar-bold-duotone-chat_round_check';
    case BoldduotoneInboxOut = 'solar-bold-duotone-inbox_out';
    case BoldduotonePlain3 = 'solar-bold-duotone-plain3';
    case BoldduotoneChatRoundDots = 'solar-bold-duotone-chat_round_dots';
    case BoldduotoneChatRoundLike = 'solar-bold-duotone-chat_round_like';
    case BoldduotonePlain2 = 'solar-bold-duotone-plain2';
    case BoldduotoneChatRoundUnread = 'solar-bold-duotone-chat_round_unread';
    case BoldduotoneChatSquareLike = 'solar-bold-duotone-chat_square_like';
    case BoldduotonePaperclip = 'solar-bold-duotone-Paperclip';
    case BoldduotoneChatSquareCheck = 'solar-bold-duotone-chat_square_check';
    case BoldduotoneChatSquare = 'solar-bold-duotone-chat_square';
    case BoldduotoneLetterOpened = 'solar-bold-duotone-letter_opened';
    case BoldduotoneSquareForward = 'solar-bold-duotone-square_forward';
    case BoldduotoneLetterUnread = 'solar-bold-duotone-letter_unread';
    case BoldduotonePaperclipRounded2 = 'solar-bold-duotone-paperclip_rounded2';
    case BoldduotoneChatRoundCall = 'solar-bold-duotone-chat_round_call';
    case BoldduotoneInboxLine = 'solar-bold-duotone-inbox_line';
    case BoldduotoneChatRoundVideo = 'solar-bold-duotone-chat_round_video';
    case BoldduotoneChatRoundMoney = 'solar-bold-duotone-chat_round_money';
    case BoldduotoneInboxIn = 'solar-bold-duotone-inbox_in';
    case BoldduotoneCheckRead = 'solar-bold-duotone-check_read';
    case BoldduotoneChatRoundLine = 'solar-bold-duotone-chat_round_line';
    case BoldduotoneForward = 'solar-bold-duotone-Forward';
    case BoldduotonePaperclip2 = 'solar-bold-duotone-paperclip2';
    case BoldduotoneDialog2 = 'solar-bold-duotone-dialog2';
    case BoldduotoneDialog = 'solar-bold-duotone-Dialog';
    case BoldduotonePaperclipRounded = 'solar-bold-duotone-paperclip_rounded';
    case BoldduotonePlain = 'solar-bold-duotone-Plain';
    case BoldduotoneChatSquareArrow = 'solar-bold-duotone-chat_square_arrow';
    case BoldduotoneChatSquareCode = 'solar-bold-duotone-chat_square_code';
    case BoldduotoneChatLine = 'solar-bold-duotone-chat_line';
    case BoldduotoneTennis = 'solar-bold-duotone-Tennis';
    case BoldduotoneBicyclingRound = 'solar-bold-duotone-bicycling_round';
    case BoldduotoneBalls = 'solar-bold-duotone-Balls';
    case BoldduotoneMeditationRound = 'solar-bold-duotone-meditation_round';
    case BoldduotoneStretchingRound = 'solar-bold-duotone-stretching_round';
    case BoldduotoneDumbbells2 = 'solar-bold-duotone-dumbbells2';
    case BoldduotoneMeditation = 'solar-bold-duotone-Meditation';
    case BoldduotoneRunning2 = 'solar-bold-duotone-running2';
    case BoldduotoneRugby = 'solar-bold-duotone-Rugby';
    case BoldduotoneBodyShapeMinimalistic = 'solar-bold-duotone-body_shape_minimalistic';
    case BoldduotoneStretching = 'solar-bold-duotone-Stretching';
    case BoldduotoneBowling = 'solar-bold-duotone-Bowling';
    case BoldduotoneRanking = 'solar-bold-duotone-Ranking';
    case BoldduotoneTreadmillRound = 'solar-bold-duotone-treadmill_round';
    case BoldduotoneVolleyball = 'solar-bold-duotone-Volleyball';
    case BoldduotoneDumbbellLargeMinimalistic = 'solar-bold-duotone-dumbbell_large_minimalistic';
    case BoldduotoneRunningRound = 'solar-bold-duotone-running_round';
    case BoldduotoneHiking = 'solar-bold-duotone-Hiking';
    case BoldduotoneHikingMinimalistic = 'solar-bold-duotone-hiking_minimalistic';
    case BoldduotoneWaterSun = 'solar-bold-duotone-water_sun';
    case BoldduotoneGolf = 'solar-bold-duotone-Golf';
    case BoldduotoneSkateboarding = 'solar-bold-duotone-Skateboarding';
    case BoldduotoneDumbbells = 'solar-bold-duotone-Dumbbells';
    case BoldduotoneWalkingRound = 'solar-bold-duotone-walking_round';
    case BoldduotoneRunning = 'solar-bold-duotone-Running';
    case BoldduotoneTreadmill = 'solar-bold-duotone-Treadmill';
    case BoldduotoneSkateboard = 'solar-bold-duotone-Skateboard';
    case BoldduotoneDumbbellSmall = 'solar-bold-duotone-dumbbell_small';
    case BoldduotoneBasketball = 'solar-bold-duotone-Basketball';
    case BoldduotoneFootball = 'solar-bold-duotone-Football';
    case BoldduotoneDumbbell = 'solar-bold-duotone-Dumbbell';
    case BoldduotoneBodyShape = 'solar-bold-duotone-body_shape';
    case BoldduotoneWater = 'solar-bold-duotone-Water';
    case BoldduotoneSkateboardingRound = 'solar-bold-duotone-skateboarding_round';
    case BoldduotoneHikingRound = 'solar-bold-duotone-hiking_round';
    case BoldduotoneVolleyball2 = 'solar-bold-duotone-volleyball2';
    case BoldduotoneTennis2 = 'solar-bold-duotone-tennis2';
    case BoldduotoneSwimming = 'solar-bold-duotone-Swimming';
    case BoldduotoneBicycling = 'solar-bold-duotone-Bicycling';
    case BoldduotoneWalking = 'solar-bold-duotone-Walking';
    case BoldduotoneDumbbellLarge = 'solar-bold-duotone-dumbbell_large';
    case BoldduotoneCalendarMark = 'solar-bold-duotone-calendar_mark';
    case BoldduotoneHistory2 = 'solar-bold-duotone-history2';
    case BoldduotoneWatchSquareMinimalisticCharge = 'solar-bold-duotone-watch_square_minimalistic_charge';
    case BoldduotoneHistory3 = 'solar-bold-duotone-history3';
    case BoldduotoneHourglass = 'solar-bold-duotone-Hourglass';
    case BoldduotoneCalendarSearch = 'solar-bold-duotone-calendar_search';
    case BoldduotoneStopwatchPlay = 'solar-bold-duotone-stopwatch_play';
    case BoldduotoneWatchRound = 'solar-bold-duotone-watch_round';
    case BoldduotoneCalendarAdd = 'solar-bold-duotone-calendar_add';
    case BoldduotoneCalendarDate = 'solar-bold-duotone-calendar_date';
    case BoldduotoneStopwatch = 'solar-bold-duotone-Stopwatch';
    case BoldduotoneAlarmPause = 'solar-bold-duotone-alarm_pause';
    case BoldduotoneAlarmTurnOff = 'solar-bold-duotone-alarm_turn_off';
    case BoldduotoneClockSquare = 'solar-bold-duotone-clock_square';
    case BoldduotoneStopwatchPause = 'solar-bold-duotone-stopwatch_pause';
    case BoldduotoneCalendarMinimalistic = 'solar-bold-duotone-calendar_minimalistic';
    case BoldduotoneAlarmAdd = 'solar-bold-duotone-alarm_add';
    case BoldduotoneAlarmPlay = 'solar-bold-duotone-alarm_play';
    case BoldduotoneHourglassLine = 'solar-bold-duotone-hourglass_line';
    case BoldduotoneAlarmSleep = 'solar-bold-duotone-alarm_sleep';
    case BoldduotoneAlarmRemove = 'solar-bold-duotone-alarm_remove';
    case BoldduotoneCalendar = 'solar-bold-duotone-Calendar';
    case BoldduotoneClockCircle = 'solar-bold-duotone-clock_circle';
    case BoldduotoneHistory = 'solar-bold-duotone-History';
    case BoldduotoneAlarm = 'solar-bold-duotone-Alarm';
    case BoldduotoneWatchSquare = 'solar-bold-duotone-watch_square';
    case BoldduotoneWatchSquareMinimalistic = 'solar-bold-duotone-watch_square_minimalistic';
    case BoldduotoneMagniferBug = 'solar-bold-duotone-magnifer_bug';
    case BoldduotoneMagnifer = 'solar-bold-duotone-Magnifer';
    case BoldduotoneMagniferZoomIn = 'solar-bold-duotone-magnifer_zoom_in';
    case BoldduotoneRoundedMagnifer = 'solar-bold-duotone-rounded_magnifer';
    case BoldduotoneRoundedMagniferZoomIn = 'solar-bold-duotone-rounded_magnifer_zoom_in';
    case BoldduotoneMinimalisticMagniferBug = 'solar-bold-duotone-minimalistic_magnifer_bug';
    case BoldduotoneRoundedMagniferBug = 'solar-bold-duotone-rounded_magnifer_bug';
    case BoldduotoneMinimalisticMagniferZoomOut = 'solar-bold-duotone-minimalistic_magnifer_zoom_out';
    case BoldduotoneMinimalisticMagnifer = 'solar-bold-duotone-minimalistic_magnifer';
    case BoldduotoneRoundedMagniferZoomOut = 'solar-bold-duotone-rounded_magnifer_zoom_out';
    case BoldduotoneMinimalisticMagniferZoomIn = 'solar-bold-duotone-minimalistic_magnifer_zoom_in';
    case BoldduotoneMagniferZoomOut = 'solar-bold-duotone-magnifer_zoom_out';
    case BoldduotoneBagCheck = 'solar-bold-duotone-bag_check';
    case BoldduotoneShopMinimalistic = 'solar-bold-duotone-shop_minimalistic';
    case BoldduotoneShop = 'solar-bold-duotone-Shop';
    case BoldduotoneCartCheck = 'solar-bold-duotone-cart_check';
    case BoldduotoneCart = 'solar-bold-duotone-Cart';
    case BoldduotoneCart3 = 'solar-bold-duotone-cart3';
    case BoldduotoneCart2 = 'solar-bold-duotone-cart2';
    case BoldduotoneBagMusic = 'solar-bold-duotone-bag_music';
    case BoldduotoneCartLargeMinimalistic = 'solar-bold-duotone-cart_large_minimalistic';
    case BoldduotoneCart5 = 'solar-bold-duotone-cart5';
    case BoldduotoneCart4 = 'solar-bold-duotone-cart4';
    case BoldduotoneBag = 'solar-bold-duotone-Bag';
    case BoldduotoneBagHeart = 'solar-bold-duotone-bag_heart';
    case BoldduotoneCartPlus = 'solar-bold-duotone-cart_plus';
    case BoldduotoneCartLarge = 'solar-bold-duotone-cart_large';
    case BoldduotoneBagCross = 'solar-bold-duotone-bag_cross';
    case BoldduotoneBagMusic2 = 'solar-bold-duotone-bag_music2';
    case BoldduotoneBag5 = 'solar-bold-duotone-bag5';
    case BoldduotoneBag4 = 'solar-bold-duotone-bag4';
    case BoldduotoneCartLarge4 = 'solar-bold-duotone-cart_large4';
    case BoldduotoneCartLarge3 = 'solar-bold-duotone-cart_large3';
    case BoldduotoneBag3 = 'solar-bold-duotone-bag3';
    case BoldduotoneBag2 = 'solar-bold-duotone-bag2';
    case BoldduotoneShop2 = 'solar-bold-duotone-shop2';
    case BoldduotoneCartLarge2 = 'solar-bold-duotone-cart_large2';
    case BoldduotoneBagSmile = 'solar-bold-duotone-bag_smile';
    case BoldduotoneCartCross = 'solar-bold-duotone-cart_cross';
    case BoldduotoneInfoSquare = 'solar-bold-duotone-info_square';
    case BoldduotoneFlashlightOn = 'solar-bold-duotone-flashlight_on';
    case BoldduotoneXXX = 'solar-bold-duotone-XXX';
    case BoldduotoneFigma = 'solar-bold-duotone-Figma';
    case BoldduotoneFlashlight = 'solar-bold-duotone-Flashlight';
    case BoldduotoneGhost = 'solar-bold-duotone-Ghost';
    case BoldduotoneCupMusic = 'solar-bold-duotone-cup_music';
    case BoldduotoneBatteryFullMinimalistic = 'solar-bold-duotone-battery_full_minimalistic';
    case BoldduotoneDangerCircle = 'solar-bold-duotone-danger_circle';
    case BoldduotoneCheckSquare = 'solar-bold-duotone-check_square';
    case BoldduotoneGhostSmile = 'solar-bold-duotone-ghost_smile';
    case BoldduotoneTarget = 'solar-bold-duotone-Target';
    case BoldduotoneBatteryHalfMinimalistic = 'solar-bold-duotone-battery_half_minimalistic';
    case BoldduotoneScissors = 'solar-bold-duotone-Scissors';
    case BoldduotonePinList = 'solar-bold-duotone-pin_list';
    case BoldduotoneBatteryCharge = 'solar-bold-duotone-battery_charge';
    case BoldduotoneUmbrella = 'solar-bold-duotone-Umbrella';
    case BoldduotoneHomeSmile = 'solar-bold-duotone-home_smile';
    case BoldduotoneHome = 'solar-bold-duotone-Home';
    case BoldduotoneCopyright = 'solar-bold-duotone-Copyright';
    case BoldduotoneHomeWifi = 'solar-bold-duotone-home_wifi';
    case BoldduotoneTShirt = 'solar-bold-duotone-t_shirt';
    case BoldduotoneBatteryChargeMinimalistic = 'solar-bold-duotone-battery_charge_minimalistic';
    case BoldduotoneCupStar = 'solar-bold-duotone-cup_star';
    case BoldduotoneSpecialEffects = 'solar-bold-duotone-special_effects';
    case BoldduotoneBody = 'solar-bold-duotone-Body';
    case BoldduotoneHamburgerMenu = 'solar-bold-duotone-hamburger_menu';
    case BoldduotonePower = 'solar-bold-duotone-Power';
    case BoldduotoneDatabase = 'solar-bold-duotone-Database';
    case BoldduotoneCursorSquare = 'solar-bold-duotone-cursor_square';
    case BoldduotoneFuel = 'solar-bold-duotone-Fuel';
    case BoldduotoneMentionCircle = 'solar-bold-duotone-mention_circle';
    case BoldduotoneConfettiMinimalistic = 'solar-bold-duotone-confetti_minimalistic';
    case BoldduotoneMenuDotsCircle = 'solar-bold-duotone-menu_dots_circle';
    case BoldduotonePaw = 'solar-bold-duotone-Paw';
    case BoldduotoneSubtitles = 'solar-bold-duotone-Subtitles';
    case BoldduotoneSliderVerticalMinimalistic = 'solar-bold-duotone-slider_vertical_minimalistic';
    case BoldduotoneCrownMinimalistic = 'solar-bold-duotone-crown_minimalistic';
    case BoldduotoneMenuDots = 'solar-bold-duotone-menu_dots';
    case BoldduotoneDelivery = 'solar-bold-duotone-Delivery';
    case BoldduotoneWaterdrop = 'solar-bold-duotone-Waterdrop';
    case BoldduotonePerfume = 'solar-bold-duotone-Perfume';
    case BoldduotoneHomeAngle2 = 'solar-bold-duotone-home_angle2';
    case BoldduotoneHomeWifiAngle = 'solar-bold-duotone-home_wifi_angle';
    case BoldduotoneQuestionCircle = 'solar-bold-duotone-question_circle';
    case BoldduotoneTrashBinMinimalistic = 'solar-bold-duotone-trash_bin_minimalistic';
    case BoldduotoneMagicStick3 = 'solar-bold-duotone-magic_stick3';
    case BoldduotoneAddSquare = 'solar-bold-duotone-add_square';
    case BoldduotoneCrownStar = 'solar-bold-duotone-crown_star';
    case BoldduotoneMagnet = 'solar-bold-duotone-Magnet';
    case BoldduotoneConfetti = 'solar-bold-duotone-Confetti';
    case BoldduotonePin = 'solar-bold-duotone-Pin';
    case BoldduotoneMinusSquare = 'solar-bold-duotone-minus_square';
    case BoldduotoneBolt = 'solar-bold-duotone-Bolt';
    case BoldduotoneCloseCircle = 'solar-bold-duotone-close_circle';
    case BoldduotoneForbiddenCircle = 'solar-bold-duotone-forbidden_circle';
    case BoldduotoneMagicStick2 = 'solar-bold-duotone-magic_stick2';
    case BoldduotoneCrownLine = 'solar-bold-duotone-crown_line';
    case BoldduotoneBoltCircle = 'solar-bold-duotone-bolt_circle';
    case BoldduotoneFlag = 'solar-bold-duotone-Flag';
    case BoldduotoneSliderHorizontal = 'solar-bold-duotone-slider_horizontal';
    case BoldduotoneHighDefinition = 'solar-bold-duotone-high_definition';
    case BoldduotoneCursor = 'solar-bold-duotone-Cursor';
    case BoldduotoneFeed = 'solar-bold-duotone-Feed';
    case BoldduotoneTrafficEconomy = 'solar-bold-duotone-traffic_economy';
    case BoldduotoneAugmentedReality = 'solar-bold-duotone-augmented_reality';
    case BoldduotoneIcon4K = 'solar-bold-duotone-4_k';
    case BoldduotoneMagnetWave = 'solar-bold-duotone-magnet_wave';
    case BoldduotoneHomeSmileAngle = 'solar-bold-duotone-home_smile_angle';
    case BoldduotoneSliderVertical = 'solar-bold-duotone-slider_vertical';
    case BoldduotoneCheckCircle = 'solar-bold-duotone-check_circle';
    case BoldduotoneCopy = 'solar-bold-duotone-Copy';
    case BoldduotoneDangerSquare = 'solar-bold-duotone-danger_square';
    case BoldduotoneSkirt = 'solar-bold-duotone-Skirt';
    case BoldduotoneGlasses = 'solar-bold-duotone-Glasses';
    case BoldduotoneHomeAdd = 'solar-bold-duotone-home_add';
    case BoldduotoneSledgehammer = 'solar-bold-duotone-Sledgehammer';
    case BoldduotoneInfoCircle = 'solar-bold-duotone-info_circle';
    case BoldduotoneDangerTriangle = 'solar-bold-duotone-danger_triangle';
    case BoldduotonePinCircle = 'solar-bold-duotone-pin_circle';
    case BoldduotoneSmartHome = 'solar-bold-duotone-smart_home';
    case BoldduotoneScissorsSquare = 'solar-bold-duotone-scissors_square';
    case BoldduotoneSleeping = 'solar-bold-duotone-Sleeping';
    case BoldduotoneBox = 'solar-bold-duotone-Box';
    case BoldduotoneCrown = 'solar-bold-duotone-Crown';
    case BoldduotoneBroom = 'solar-bold-duotone-Broom';
    case BoldduotonePostsCarouselHorizontal = 'solar-bold-duotone-posts_carousel_horizontal';
    case BoldduotoneFlag2 = 'solar-bold-duotone-flag2';
    case BoldduotonePlate = 'solar-bold-duotone-Plate';
    case BoldduotoneTrashBinTrash = 'solar-bold-duotone-trash_bin_trash';
    case BoldduotoneCupFirst = 'solar-bold-duotone-cup_first';
    case BoldduotoneSmartHomeAngle = 'solar-bold-duotone-smart_home_angle';
    case BoldduotonePaperBin = 'solar-bold-duotone-paper_bin';
    case BoldduotoneBoxMinimalistic = 'solar-bold-duotone-box_minimalistic';
    case BoldduotoneDanger = 'solar-bold-duotone-Danger';
    case BoldduotoneMenuDotsSquare = 'solar-bold-duotone-menu_dots_square';
    case BoldduotoneHanger2 = 'solar-bold-duotone-hanger2';
    case BoldduotoneBatteryHalf = 'solar-bold-duotone-battery_half';
    case BoldduotoneHome2 = 'solar-bold-duotone-home2';
    case BoldduotonePostsCarouselVertical = 'solar-bold-duotone-posts_carousel_vertical';
    case BoldduotoneRevote = 'solar-bold-duotone-Revote';
    case BoldduotoneMentionSquare = 'solar-bold-duotone-mention_square';
    case BoldduotoneWinRar = 'solar-bold-duotone-win_rar';
    case BoldduotoneForbidden = 'solar-bold-duotone-Forbidden';
    case BoldduotoneQuestionSquare = 'solar-bold-duotone-question_square';
    case BoldduotoneHanger = 'solar-bold-duotone-Hanger';
    case BoldduotoneReorder = 'solar-bold-duotone-Reorder';
    case BoldduotoneHomeAddAngle = 'solar-bold-duotone-home_add_angle';
    case BoldduotoneMasks = 'solar-bold-duotone-Masks';
    case BoldduotoneGift = 'solar-bold-duotone-Gift';
    case BoldduotoneCreativeCommons = 'solar-bold-duotone-creative_commons';
    case BoldduotoneSliderMinimalisticHorizontal = 'solar-bold-duotone-slider_minimalistic_horizontal';
    case BoldduotoneHomeAngle = 'solar-bold-duotone-home_angle';
    case BoldduotoneBatteryLowMinimalistic = 'solar-bold-duotone-battery_low_minimalistic';
    case BoldduotoneShare = 'solar-bold-duotone-Share';
    case BoldduotoneTrashBin2 = 'solar-bold-duotone-trash_bin2';
    case BoldduotoneSort = 'solar-bold-duotone-Sort';
    case BoldduotoneMinusCircle = 'solar-bold-duotone-minus_circle';
    case BoldduotoneExplicit = 'solar-bold-duotone-Explicit';
    case BoldduotoneTraffic = 'solar-bold-duotone-Traffic';
    case BoldduotoneFilter = 'solar-bold-duotone-Filter';
    case BoldduotoneCloseSquare = 'solar-bold-duotone-close_square';
    case BoldduotoneAddCircle = 'solar-bold-duotone-add_circle';
    case BoldduotoneFerrisWheel = 'solar-bold-duotone-ferris_wheel';
    case BoldduotoneCup = 'solar-bold-duotone-Cup';
    case BoldduotoneBalloon = 'solar-bold-duotone-Balloon';
    case BoldduotoneHelp = 'solar-bold-duotone-Help';
    case BoldduotoneBatteryFull = 'solar-bold-duotone-battery_full';
    case BoldduotoneCat = 'solar-bold-duotone-Cat';
    case BoldduotoneMaskSad = 'solar-bold-duotone-mask_sad';
    case BoldduotoneHighQuality = 'solar-bold-duotone-high_quality';
    case BoldduotoneMagicStick = 'solar-bold-duotone-magic_stick';
    case BoldduotoneCosmetic = 'solar-bold-duotone-Cosmetic';
    case BoldduotoneBatteryLow = 'solar-bold-duotone-battery_low';
    case BoldduotoneShareCircle = 'solar-bold-duotone-share_circle';
    case BoldduotoneMaskHapply = 'solar-bold-duotone-mask_happly';
    case BoldduotoneAccessibility = 'solar-bold-duotone-Accessibility';
    case BoldduotoneTrashBinMinimalistic2 = 'solar-bold-duotone-trash_bin_minimalistic2';
    case BoldduotoneIncomingCallRounded = 'solar-bold-duotone-incoming_call_rounded';
    case BoldduotoneCallDropped = 'solar-bold-duotone-call_dropped';
    case BoldduotoneCallChat = 'solar-bold-duotone-call_chat';
    case BoldduotoneCallCancelRounded = 'solar-bold-duotone-call_cancel_rounded';
    case BoldduotoneCallMedicineRounded = 'solar-bold-duotone-call_medicine_rounded';
    case BoldduotoneCallDroppedRounded = 'solar-bold-duotone-call_dropped_rounded';
    case BoldduotoneRecordSquare = 'solar-bold-duotone-record_square';
    case BoldduotonePhoneCalling = 'solar-bold-duotone-phone_calling';
    case BoldduotonePhoneRounded = 'solar-bold-duotone-phone_rounded';
    case BoldduotoneCallMedicine = 'solar-bold-duotone-call_medicine';
    case BoldduotoneRecordMinimalistic = 'solar-bold-duotone-record_minimalistic';
    case BoldduotoneEndCall = 'solar-bold-duotone-end_call';
    case BoldduotoneOutgoingCall = 'solar-bold-duotone-outgoing_call';
    case BoldduotoneRecordCircle = 'solar-bold-duotone-record_circle';
    case BoldduotoneIncomingCall = 'solar-bold-duotone-incoming_call';
    case BoldduotoneCallChatRounded = 'solar-bold-duotone-call_chat_rounded';
    case BoldduotoneEndCallRounded = 'solar-bold-duotone-end_call_rounded';
    case BoldduotonePhone = 'solar-bold-duotone-Phone';
    case BoldduotoneOutgoingCallRounded = 'solar-bold-duotone-outgoing_call_rounded';
    case BoldduotoneCallCancel = 'solar-bold-duotone-call_cancel';
    case BoldduotonePhoneCallingRounded = 'solar-bold-duotone-phone_calling_rounded';
    case BoldduotoneStationMinimalistic = 'solar-bold-duotone-station_minimalistic';
    case BoldduotoneSidebarCode = 'solar-bold-duotone-sidebar_code';
    case BoldduotoneWiFiRouterMinimalistic = 'solar-bold-duotone-wi_fi_router_minimalistic';
    case BoldduotoneUSB = 'solar-bold-duotone-USB';
    case BoldduotoneSiderbar = 'solar-bold-duotone-Siderbar';
    case BoldduotoneCode2 = 'solar-bold-duotone-code2';
    case BoldduotoneSlashCircle = 'solar-bold-duotone-slash_circle';
    case BoldduotoneScreencast = 'solar-bold-duotone-Screencast';
    case BoldduotoneHashtagSquare = 'solar-bold-duotone-hashtag_square';
    case BoldduotoneSidebarMinimalistic = 'solar-bold-duotone-sidebar_minimalistic';
    case BoldduotoneCode = 'solar-bold-duotone-Code';
    case BoldduotoneUsbSquare = 'solar-bold-duotone-usb_square';
    case BoldduotoneWiFiRouter = 'solar-bold-duotone-wi_fi_router';
    case BoldduotoneCodeCircle = 'solar-bold-duotone-code_circle';
    case BoldduotoneTranslation = 'solar-bold-duotone-Translation';
    case BoldduotoneBugMinimalistic = 'solar-bold-duotone-bug_minimalistic';
    case BoldduotoneStation = 'solar-bold-duotone-Station';
    case BoldduotoneProgramming = 'solar-bold-duotone-Programming';
    case BoldduotoneWiFiRouterRound = 'solar-bold-duotone-wi_fi_router_round';
    case BoldduotoneHashtag = 'solar-bold-duotone-Hashtag';
    case BoldduotoneBug = 'solar-bold-duotone-Bug';
    case BoldduotoneHashtagChat = 'solar-bold-duotone-hashtag_chat';
    case BoldduotoneCommand = 'solar-bold-duotone-Command';
    case BoldduotoneTranslation2 = 'solar-bold-duotone-translation2';
    case BoldduotoneHashtagCircle = 'solar-bold-duotone-hashtag_circle';
    case BoldduotoneScreencast2 = 'solar-bold-duotone-screencast2';
    case BoldduotoneSlashSquare = 'solar-bold-duotone-slash_square';
    case BoldduotoneWindowFrame = 'solar-bold-duotone-window_frame';
    case BoldduotoneStructure = 'solar-bold-duotone-Structure';
    case BoldduotoneUsbCircle = 'solar-bold-duotone-usb_circle';
    case BoldduotoneCodeSquare = 'solar-bold-duotone-code_square';
    case BoldduotoneNotes = 'solar-bold-duotone-Notes';
    case BoldduotoneDocumentText = 'solar-bold-duotone-document_text';
    case BoldduotoneDocumentAdd = 'solar-bold-duotone-document_add';
    case BoldduotoneDocumentMedicine = 'solar-bold-duotone-document_medicine';
    case BoldduotoneArchiveMinimalistic = 'solar-bold-duotone-archive_minimalistic';
    case BoldduotoneClipboard = 'solar-bold-duotone-Clipboard';
    case BoldduotoneClipboardAdd = 'solar-bold-duotone-clipboard_add';
    case BoldduotoneArchive = 'solar-bold-duotone-Archive';
    case BoldduotoneClipboardHeart = 'solar-bold-duotone-clipboard_heart';
    case BoldduotoneClipboardRemove = 'solar-bold-duotone-clipboard_remove';
    case BoldduotoneClipboardText = 'solar-bold-duotone-clipboard_text';
    case BoldduotoneDocument = 'solar-bold-duotone-Document';
    case BoldduotoneNotesMinimalistic = 'solar-bold-duotone-notes_minimalistic';
    case BoldduotoneArchiveUp = 'solar-bold-duotone-archive_up';
    case BoldduotoneArchiveUpMinimlistic = 'solar-bold-duotone-archive_up_minimlistic';
    case BoldduotoneArchiveCheck = 'solar-bold-duotone-archive_check';
    case BoldduotoneArchiveDown = 'solar-bold-duotone-archive_down';
    case BoldduotoneArchiveDownMinimlistic = 'solar-bold-duotone-archive_down_minimlistic';
    case BoldduotoneDocumentsMinimalistic = 'solar-bold-duotone-documents_minimalistic';
    case BoldduotoneClipboardCheck = 'solar-bold-duotone-clipboard_check';
    case BoldduotoneClipboardList = 'solar-bold-duotone-clipboard_list';
    case BoldduotoneDocuments = 'solar-bold-duotone-Documents';
    case BoldduotoneNotebook = 'solar-bold-duotone-Notebook';
    case BoldduotoneGalleryRound = 'solar-bold-duotone-gallery_round';
    case BoldduotonePlayCircle = 'solar-bold-duotone-play_circle';
    case BoldduotoneStream = 'solar-bold-duotone-Stream';
    case BoldduotoneGalleryRemove = 'solar-bold-duotone-gallery_remove';
    case BoldduotoneClapperboard = 'solar-bold-duotone-Clapperboard';
    case BoldduotonePauseCircle = 'solar-bold-duotone-pause_circle';
    case BoldduotoneRewind5SecondsBack = 'solar-bold-duotone-rewind5_seconds_back';
    case BoldduotoneRepeat = 'solar-bold-duotone-Repeat';
    case BoldduotoneClapperboardEdit = 'solar-bold-duotone-clapperboard_edit';
    case BoldduotoneVideoFrameCut = 'solar-bold-duotone-video_frame_cut';
    case BoldduotonePanorama = 'solar-bold-duotone-Panorama';
    case BoldduotonePlayStream = 'solar-bold-duotone-play_stream';
    case BoldduotoneClapperboardOpen = 'solar-bold-duotone-clapperboard_open';
    case BoldduotoneClapperboardText = 'solar-bold-duotone-clapperboard_text';
    case BoldduotoneLibrary = 'solar-bold-duotone-Library';
    case BoldduotoneReel2 = 'solar-bold-duotone-reel2';
    case BoldduotoneVolumeSmall = 'solar-bold-duotone-volume_small';
    case BoldduotoneVideoFrame = 'solar-bold-duotone-video_frame';
    case BoldduotoneMicrophoneLarge = 'solar-bold-duotone-microphone_large';
    case BoldduotoneRewindForward = 'solar-bold-duotone-rewind_forward';
    case BoldduotoneRewindBackCircle = 'solar-bold-duotone-rewind_back_circle';
    case BoldduotoneMicrophone = 'solar-bold-duotone-Microphone';
    case BoldduotoneVideoFrameReplace = 'solar-bold-duotone-video_frame_replace';
    case BoldduotoneClapperboardPlay = 'solar-bold-duotone-clapperboard_play';
    case BoldduotoneGalleryDownload = 'solar-bold-duotone-gallery_download';
    case BoldduotoneMusicNote4 = 'solar-bold-duotone-music_note4';
    case BoldduotoneVideocameraRecord = 'solar-bold-duotone-videocamera_record';
    case BoldduotonePlaybackSpeed = 'solar-bold-duotone-playback_speed';
    case BoldduotoneSoundwave = 'solar-bold-duotone-Soundwave';
    case BoldduotoneStopCircle = 'solar-bold-duotone-stop_circle';
    case BoldduotoneQuitFullScreenCircle = 'solar-bold-duotone-quit_full_screen_circle';
    case BoldduotoneRewindBack = 'solar-bold-duotone-rewind_back';
    case BoldduotoneRepeatOne = 'solar-bold-duotone-repeat_one';
    case BoldduotoneGalleryCheck = 'solar-bold-duotone-gallery_check';
    case BoldduotoneWallpaper = 'solar-bold-duotone-Wallpaper';
    case BoldduotoneRewindForwardCircle = 'solar-bold-duotone-rewind_forward_circle';
    case BoldduotoneGalleryEdit = 'solar-bold-duotone-gallery_edit';
    case BoldduotoneGallery = 'solar-bold-duotone-Gallery';
    case BoldduotoneGalleryMinimalistic = 'solar-bold-duotone-gallery_minimalistic';
    case BoldduotoneUploadTrack = 'solar-bold-duotone-upload_track';
    case BoldduotoneVolume = 'solar-bold-duotone-Volume';
    case BoldduotoneUploadTrack2 = 'solar-bold-duotone-upload_track2';
    case BoldduotoneMusicNotes = 'solar-bold-duotone-music_notes';
    case BoldduotoneMusicNote2 = 'solar-bold-duotone-music_note2';
    case BoldduotoneCameraAdd = 'solar-bold-duotone-camera_add';
    case BoldduotonePodcast = 'solar-bold-duotone-Podcast';
    case BoldduotoneCameraRotate = 'solar-bold-duotone-camera_rotate';
    case BoldduotoneMusicNote3 = 'solar-bold-duotone-music_note3';
    case BoldduotoneStop = 'solar-bold-duotone-Stop';
    case BoldduotoneMuted = 'solar-bold-duotone-Muted';
    case BoldduotoneSkipNext = 'solar-bold-duotone-skip_next';
    case BoldduotoneGallerySend = 'solar-bold-duotone-gallery_send';
    case BoldduotoneRecord = 'solar-bold-duotone-Record';
    case BoldduotoneFullScreenCircle = 'solar-bold-duotone-full_screen_circle';
    case BoldduotoneVolumeCross = 'solar-bold-duotone-volume_cross';
    case BoldduotoneSoundwaveCircle = 'solar-bold-duotone-soundwave_circle';
    case BoldduotoneSkipPrevious = 'solar-bold-duotone-skip_previous';
    case BoldduotoneRewind5SecondsForward = 'solar-bold-duotone-rewind5_seconds_forward';
    case BoldduotonePlay = 'solar-bold-duotone-Play';
    case BoldduotonePIP = 'solar-bold-duotone-PIP';
    case BoldduotoneMusicLibrary = 'solar-bold-duotone-music_library';
    case BoldduotoneVideoFrame2 = 'solar-bold-duotone-video_frame2';
    case BoldduotoneCamera = 'solar-bold-duotone-Camera';
    case BoldduotoneQuitPip = 'solar-bold-duotone-quit_pip';
    case BoldduotoneClapperboardOpenPlay = 'solar-bold-duotone-clapperboard_open_play';
    case BoldduotoneRewind10SecondsBack = 'solar-bold-duotone-rewind10_seconds_back';
    case BoldduotoneRepeatOneMinimalistic = 'solar-bold-duotone-repeat_one_minimalistic';
    case BoldduotoneVinyl = 'solar-bold-duotone-Vinyl';
    case BoldduotoneVideoLibrary = 'solar-bold-duotone-video_library';
    case BoldduotoneGalleryWide = 'solar-bold-duotone-gallery_wide';
    case BoldduotoneReel = 'solar-bold-duotone-Reel';
    case BoldduotoneToPip = 'solar-bold-duotone-to_pip';
    case BoldduotonePip2 = 'solar-bold-duotone-pip2';
    case BoldduotoneFullScreen = 'solar-bold-duotone-full_screen';
    case BoldduotoneCameraMinimalistic = 'solar-bold-duotone-camera_minimalistic';
    case BoldduotoneVideoFrameCut2 = 'solar-bold-duotone-video_frame_cut2';
    case BoldduotoneGalleryCircle = 'solar-bold-duotone-gallery_circle';
    case BoldduotoneVideoFramePlayHorizontal = 'solar-bold-duotone-video_frame_play_horizontal';
    case BoldduotoneMusicNoteSlider2 = 'solar-bold-duotone-music_note_slider2';
    case BoldduotoneMusicNoteSlider = 'solar-bold-duotone-music_note_slider';
    case BoldduotoneVideocameraAdd = 'solar-bold-duotone-videocamera_add';
    case BoldduotoneQuitFullScreenSquare = 'solar-bold-duotone-quit_full_screen_square';
    case BoldduotoneAlbum = 'solar-bold-duotone-Album';
    case BoldduotoneGalleryAdd = 'solar-bold-duotone-gallery_add';
    case BoldduotoneCameraSquare = 'solar-bold-duotone-camera_square';
    case BoldduotoneRewind15SecondsBack = 'solar-bold-duotone-rewind15_seconds_back';
    case BoldduotoneRewind15SecondsForward = 'solar-bold-duotone-rewind15_seconds_forward';
    case BoldduotoneVinylRecord = 'solar-bold-duotone-vinyl_record';
    case BoldduotoneShuffle = 'solar-bold-duotone-Shuffle';
    case BoldduotonePause = 'solar-bold-duotone-Pause';
    case BoldduotoneMusicNote = 'solar-bold-duotone-music_note';
    case BoldduotoneQuitFullScreen = 'solar-bold-duotone-quit_full_screen';
    case BoldduotoneMicrophone2 = 'solar-bold-duotone-microphone2';
    case BoldduotoneVideocamera = 'solar-bold-duotone-Videocamera';
    case BoldduotoneGalleryFavourite = 'solar-bold-duotone-gallery_favourite';
    case BoldduotoneMusicLibrary2 = 'solar-bold-duotone-music_library2';
    case BoldduotoneVideoFramePlayVertical = 'solar-bold-duotone-video_frame_play_vertical';
    case BoldduotoneFullScreenSquare = 'solar-bold-duotone-full_screen_square';
    case BoldduotoneRewind10SecondsForward = 'solar-bold-duotone-rewind10_seconds_forward';
    case BoldduotoneVolumeLoud = 'solar-bold-duotone-volume_loud';
    case BoldduotoneMicrophone3 = 'solar-bold-duotone-microphone3';
    case BoldduotoneSoundwaveSquare = 'solar-bold-duotone-soundwave_square';
    case BoldduotoneCardholder = 'solar-bold-duotone-Cardholder';
    case BoldduotoneBillList = 'solar-bold-duotone-bill_list';
    case BoldduotoneSaleSquare = 'solar-bold-duotone-sale_square';
    case BoldduotoneDollar = 'solar-bold-duotone-Dollar';
    case BoldduotoneTicket = 'solar-bold-duotone-Ticket';
    case BoldduotoneTag = 'solar-bold-duotone-Tag';
    case BoldduotoneCashOut = 'solar-bold-duotone-cash_out';
    case BoldduotoneWallet2 = 'solar-bold-duotone-wallet2';
    case BoldduotoneRuble = 'solar-bold-duotone-Ruble';
    case BoldduotoneCardTransfer = 'solar-bold-duotone-card_transfer';
    case BoldduotoneEuro = 'solar-bold-duotone-Euro';
    case BoldduotoneSale = 'solar-bold-duotone-Sale';
    case BoldduotoneCardSearch = 'solar-bold-duotone-card_search';
    case BoldduotoneWallet = 'solar-bold-duotone-Wallet';
    case BoldduotoneBillCross = 'solar-bold-duotone-bill_cross';
    case BoldduotoneTicketSale = 'solar-bold-duotone-ticket_sale';
    case BoldduotoneSafeSquare = 'solar-bold-duotone-safe_square';
    case BoldduotoneCard = 'solar-bold-duotone-Card';
    case BoldduotoneSafe2 = 'solar-bold-duotone-safe2';
    case BoldduotoneDollarMinimalistic = 'solar-bold-duotone-dollar_minimalistic';
    case BoldduotoneTagPrice = 'solar-bold-duotone-tag_price';
    case BoldduotoneMoneyBag = 'solar-bold-duotone-money_bag';
    case BoldduotoneBill = 'solar-bold-duotone-Bill';
    case BoldduotoneCardSend = 'solar-bold-duotone-card_send';
    case BoldduotoneCardRecive = 'solar-bold-duotone-card_recive';
    case BoldduotoneBanknote2 = 'solar-bold-duotone-banknote2';
    case BoldduotoneTagHorizontal = 'solar-bold-duotone-tag_horizontal';
    case BoldduotoneBillCheck = 'solar-bold-duotone-bill_check';
    case BoldduotoneTickerStar = 'solar-bold-duotone-ticker_star';
    case BoldduotoneBanknote = 'solar-bold-duotone-Banknote';
    case BoldduotoneVerifiedCheck = 'solar-bold-duotone-verified_check';
    case BoldduotoneWadOfMoney = 'solar-bold-duotone-wad_of_money';
    case BoldduotoneCard2 = 'solar-bold-duotone-card2';
    case BoldduotoneSafeCircle = 'solar-bold-duotone-safe_circle';
    case BoldduotoneWalletMoney = 'solar-bold-duotone-wallet_money';
    case BoldduotoneList = 'solar-bold-duotone-List';
    case BoldduotoneListDownMinimalistic = 'solar-bold-duotone-list_down_minimalistic';
    case BoldduotonePlaylist2 = 'solar-bold-duotone-playlist2';
    case BoldduotoneChecklistMinimalistic = 'solar-bold-duotone-checklist_minimalistic';
    case BoldduotonePlaaylistMinimalistic = 'solar-bold-duotone-plaaylist_minimalistic';
    case BoldduotoneListHeart = 'solar-bold-duotone-list_heart';
    case BoldduotoneListArrowDown = 'solar-bold-duotone-list_arrow_down';
    case BoldduotoneListArrowUp = 'solar-bold-duotone-list_arrow_up';
    case BoldduotoneListUpMinimalistic = 'solar-bold-duotone-list_up_minimalistic';
    case BoldduotonePlaylist = 'solar-bold-duotone-Playlist';
    case BoldduotoneListUp = 'solar-bold-duotone-list_up';
    case BoldduotoneListCrossMinimalistic = 'solar-bold-duotone-list_cross_minimalistic';
    case BoldduotoneListCross = 'solar-bold-duotone-list_cross';
    case BoldduotoneListArrowDownMinimalistic = 'solar-bold-duotone-list_arrow_down_minimalistic';
    case BoldduotoneSortByAlphabet = 'solar-bold-duotone-sort_by_alphabet';
    case BoldduotoneChecklist = 'solar-bold-duotone-Checklist';
    case BoldduotoneSortFromBottomToTop = 'solar-bold-duotone-sort_from_bottom_to_top';
    case BoldduotoneListCheck = 'solar-bold-duotone-list_check';
    case BoldduotonePlaylistMinimalistic2 = 'solar-bold-duotone-playlist_minimalistic2';
    case BoldduotonePlaylistMinimalistic3 = 'solar-bold-duotone-playlist_minimalistic3';
    case BoldduotoneList1 = 'solar-bold-duotone-list1';
    case BoldduotoneSortFromTopToBottom = 'solar-bold-duotone-sort_from_top_to_bottom';
    case BoldduotoneSortByTime = 'solar-bold-duotone-sort_by_time';
    case BoldduotoneListDown = 'solar-bold-duotone-list_down';
    case BoldduotoneListHeartMinimalistic = 'solar-bold-duotone-list_heart_minimalistic';
    case BoldduotoneListCheckMinimalistic = 'solar-bold-duotone-list_check_minimalistic';
    case BoldduotoneListArrowUpMinimalistic = 'solar-bold-duotone-list_arrow_up_minimalistic';
    case BoldduotoneUserCrossRounded = 'solar-bold-duotone-user_cross_rounded';
    case BoldduotoneUser = 'solar-bold-duotone-User';
    case BoldduotoneUsersGroupRounded = 'solar-bold-duotone-users_group_rounded';
    case BoldduotoneUserPlusRounded = 'solar-bold-duotone-user_plus_rounded';
    case BoldduotoneUserBlock = 'solar-bold-duotone-user_block';
    case BoldduotoneUserMinus = 'solar-bold-duotone-user_minus';
    case BoldduotoneUserHands = 'solar-bold-duotone-user_hands';
    case BoldduotoneUserHeart = 'solar-bold-duotone-user_heart';
    case BoldduotoneUserMinusRounded = 'solar-bold-duotone-user_minus_rounded';
    case BoldduotoneUserCross = 'solar-bold-duotone-user_cross';
    case BoldduotoneUserSpeakRounded = 'solar-bold-duotone-user_speak_rounded';
    case BoldduotoneUserId = 'solar-bold-duotone-user_id';
    case BoldduotoneUserBlockRounded = 'solar-bold-duotone-user_block_rounded';
    case BoldduotoneUserHeartRounded = 'solar-bold-duotone-user_heart_rounded';
    case BoldduotoneUsersGroupTwoRounded = 'solar-bold-duotone-users_group_two_rounded';
    case BoldduotoneUserHandUp = 'solar-bold-duotone-user_hand_up';
    case BoldduotoneUserCircle = 'solar-bold-duotone-user_circle';
    case BoldduotoneUserRounded = 'solar-bold-duotone-user_rounded';
    case BoldduotoneUserCheck = 'solar-bold-duotone-user_check';
    case BoldduotoneUserPlus = 'solar-bold-duotone-user_plus';
    case BoldduotoneUserCheckRounded = 'solar-bold-duotone-user_check_rounded';
    case BoldduotoneUserSpeak = 'solar-bold-duotone-user_speak';
    case BoldduotoneVirus = 'solar-bold-duotone-Virus';
    case BoldduotoneAdhesivePlaster2 = 'solar-bold-duotone-adhesive_plaster2';
    case BoldduotoneDropper = 'solar-bold-duotone-Dropper';
    case BoldduotonePulse2 = 'solar-bold-duotone-pulse2';
    case BoldduotoneBoneBroken = 'solar-bold-duotone-bone_broken';
    case BoldduotoneHeartPulse2 = 'solar-bold-duotone-heart_pulse2';
    case BoldduotoneMedicalKit = 'solar-bold-duotone-medical_kit';
    case BoldduotoneTestTube = 'solar-bold-duotone-test_tube';
    case BoldduotoneHealth = 'solar-bold-duotone-Health';
    case BoldduotoneDropperMinimalistic2 = 'solar-bold-duotone-dropper_minimalistic2';
    case BoldduotoneDNA = 'solar-bold-duotone-DNA';
    case BoldduotoneDropper3 = 'solar-bold-duotone-dropper3';
    case BoldduotoneThermometer = 'solar-bold-duotone-Thermometer';
    case BoldduotoneDropper2 = 'solar-bold-duotone-dropper2';
    case BoldduotoneJarOfPills2 = 'solar-bold-duotone-jar_of_pills2';
    case BoldduotoneBoneCrack = 'solar-bold-duotone-bone_crack';
    case BoldduotoneJarOfPills = 'solar-bold-duotone-jar_of_pills';
    case BoldduotoneSyringe = 'solar-bold-duotone-Syringe';
    case BoldduotoneStethoscope = 'solar-bold-duotone-Stethoscope';
    case BoldduotoneBenzeneRing = 'solar-bold-duotone-benzene_ring';
    case BoldduotoneBacteria = 'solar-bold-duotone-Bacteria';
    case BoldduotoneAdhesivePlaster = 'solar-bold-duotone-adhesive_plaster';
    case BoldduotoneBone = 'solar-bold-duotone-Bone';
    case BoldduotoneBones = 'solar-bold-duotone-Bones';
    case BoldduotonePill = 'solar-bold-duotone-Pill';
    case BoldduotonePills = 'solar-bold-duotone-Pills';
    case BoldduotoneHeartPulse = 'solar-bold-duotone-heart_pulse';
    case BoldduotoneTestTubeMinimalistic = 'solar-bold-duotone-test_tube_minimalistic';
    case BoldduotonePills2 = 'solar-bold-duotone-pills2';
    case BoldduotonePulse = 'solar-bold-duotone-Pulse';
    case BoldduotoneDropperMinimalistic = 'solar-bold-duotone-dropper_minimalistic';
    case BoldduotonePills3 = 'solar-bold-duotone-pills3';
    case BoldduotoneWhisk = 'solar-bold-duotone-Whisk';
    case BoldduotoneBottle = 'solar-bold-duotone-Bottle';
    case BoldduotoneOvenMittsMinimalistic = 'solar-bold-duotone-oven_mitts_minimalistic';
    case BoldduotoneChefHatMinimalistic = 'solar-bold-duotone-chef_hat_minimalistic';
    case BoldduotoneTeaCup = 'solar-bold-duotone-tea_cup';
    case BoldduotoneWineglassTriangle = 'solar-bold-duotone-wineglass_triangle';
    case BoldduotoneOvenMitts = 'solar-bold-duotone-oven_mitts';
    case BoldduotoneCupPaper = 'solar-bold-duotone-cup_paper';
    case BoldduotoneLadle = 'solar-bold-duotone-Ladle';
    case BoldduotoneCorkscrew = 'solar-bold-duotone-Corkscrew';
    case BoldduotoneDonutBitten = 'solar-bold-duotone-donut_bitten';
    case BoldduotoneWineglass = 'solar-bold-duotone-Wineglass';
    case BoldduotoneDonut = 'solar-bold-duotone-Donut';
    case BoldduotoneCupHot = 'solar-bold-duotone-cup_hot';
    case BoldduotoneChefHatHeart = 'solar-bold-duotone-chef_hat_heart';
    case BoldduotoneChefHat = 'solar-bold-duotone-chef_hat';
    case BoldduotoneRollingPin = 'solar-bold-duotone-rolling_pin';
    case BoldduotoneCodeFile = 'solar-bold-duotone-code_file';
    case BoldduotoneFileCorrupted = 'solar-bold-duotone-file_corrupted';
    case BoldduotoneFile = 'solar-bold-duotone-File';
    case BoldduotoneFileRight = 'solar-bold-duotone-file_right';
    case BoldduotoneFileFavourite = 'solar-bold-duotone-file_favourite';
    case BoldduotoneFileDownload = 'solar-bold-duotone-file_download';
    case BoldduotoneZipFile = 'solar-bold-duotone-zip_file';
    case BoldduotoneFileText = 'solar-bold-duotone-file_text';
    case BoldduotoneFileSmile = 'solar-bold-duotone-file_smile_)';
    case BoldduotoneFileCheck = 'solar-bold-duotone-file_check';
    case BoldduotoneFileSend = 'solar-bold-duotone-file_send';
    case BoldduotoneFileLeft = 'solar-bold-duotone-file_left';
    case BoldduotoneFigmaFile = 'solar-bold-duotone-figma_file';
    case BoldduotoneFileRemove = 'solar-bold-duotone-file_remove';
    case BoldduotoneCloudFile = 'solar-bold-duotone-cloud_file';
    case BoldduotoneRemoveFolder = 'solar-bold-duotone-remove_folder';
    case BoldduotoneFolderFavouritestar = 'solar-bold-duotone-folder_favourite(star)';
    case BoldduotoneAddFolder = 'solar-bold-duotone-add_folder';
    case BoldduotoneFolderCheck = 'solar-bold-duotone-folder_check';
    case BoldduotoneFolderFavouritebookmark = 'solar-bold-duotone-folder_favourite(bookmark)';
    case BoldduotoneFolder2 = 'solar-bold-duotone-folder2';
    case BoldduotoneFolderSecurity = 'solar-bold-duotone-folder_security';
    case BoldduotoneFolderCloud = 'solar-bold-duotone-folder_cloud';
    case BoldduotoneMoveToFolder = 'solar-bold-duotone-move_to_folder';
    case BoldduotoneFolderError = 'solar-bold-duotone-folder_error';
    case BoldduotoneFolderPathConnect = 'solar-bold-duotone-folder_path_connect';
    case BoldduotoneFolderOpen = 'solar-bold-duotone-folder_open';
    case BoldduotoneFolder = 'solar-bold-duotone-Folder';
    case BoldduotoneFolderWithFiles = 'solar-bold-duotone-folder_with_files';
    case BoldduotoneCloudCheck = 'solar-bold-duotone-cloud_check';
    case BoldduotoneTemperature = 'solar-bold-duotone-Temperature';
    case BoldduotoneWind = 'solar-bold-duotone-Wind';
    case BoldduotoneCloudSnowfall = 'solar-bold-duotone-cloud_snowfall';
    case BoldduotoneSunrise = 'solar-bold-duotone-Sunrise';
    case BoldduotoneSun2 = 'solar-bold-duotone-sun2';
    case BoldduotoneCloudSun = 'solar-bold-duotone-cloud_sun';
    case BoldduotoneCloudBoltMinimalistic = 'solar-bold-duotone-cloud_bolt_minimalistic';
    case BoldduotoneCloudDownload = 'solar-bold-duotone-cloud_download';
    case BoldduotoneClouds = 'solar-bold-duotone-Clouds';
    case BoldduotoneTornado = 'solar-bold-duotone-Tornado';
    case BoldduotoneMoonSleep = 'solar-bold-duotone-moon_sleep';
    case BoldduotoneCloudUpload = 'solar-bold-duotone-cloud_upload';
    case BoldduotoneCloudRain = 'solar-bold-duotone-cloud_rain';
    case BoldduotoneFog = 'solar-bold-duotone-Fog';
    case BoldduotoneSnowflake = 'solar-bold-duotone-Snowflake';
    case BoldduotoneMoonFog = 'solar-bold-duotone-moon_fog';
    case BoldduotoneCloudMinus = 'solar-bold-duotone-cloud_minus';
    case BoldduotoneCloudBolt = 'solar-bold-duotone-cloud_bolt';
    case BoldduotoneCloudWaterdrop = 'solar-bold-duotone-cloud_waterdrop';
    case BoldduotoneSunset = 'solar-bold-duotone-Sunset';
    case BoldduotoneWaterdrops = 'solar-bold-duotone-Waterdrops';
    case BoldduotoneMoonStars = 'solar-bold-duotone-moon_stars';
    case BoldduotoneCloudPlus = 'solar-bold-duotone-cloud_plus';
    case BoldduotoneSun = 'solar-bold-duotone-Sun';
    case BoldduotoneCloudWaterdrops = 'solar-bold-duotone-cloud_waterdrops';
    case BoldduotoneCloudSun2 = 'solar-bold-duotone-cloud_sun2';
    case BoldduotoneCloudyMoon = 'solar-bold-duotone-cloudy_moon';
    case BoldduotoneTornadoSmall = 'solar-bold-duotone-tornado_small';
    case BoldduotoneCloud = 'solar-bold-duotone-Cloud';
    case BoldduotoneSunFog = 'solar-bold-duotone-sun_fog';
    case BoldduotoneCloundCross = 'solar-bold-duotone-clound_cross';
    case BoldduotoneCloudSnowfallMinimalistic = 'solar-bold-duotone-cloud_snowfall_minimalistic';
    case BoldduotoneCloudStorm = 'solar-bold-duotone-cloud_storm';
    case BoldduotoneMoon = 'solar-bold-duotone-Moon';
    case BoldduotoneRefreshCircle = 'solar-bold-duotone-refresh_circle';
    case BoldduotoneSquareArrowRightDown = 'solar-bold-duotone-square_arrow_right_down';
    case BoldduotoneRoundArrowLeftDown = 'solar-bold-duotone-round_arrow_left_down';
    case BoldduotoneRestart = 'solar-bold-duotone-Restart';
    case BoldduotoneRoundAltArrowDown = 'solar-bold-duotone-round_alt_arrow_down';
    case BoldduotoneRoundSortVertical = 'solar-bold-duotone-round_sort_vertical';
    case BoldduotoneSquareAltArrowUp = 'solar-bold-duotone-square_alt_arrow_up';
    case BoldduotoneArrowLeftUp = 'solar-bold-duotone-arrow_left_up';
    case BoldduotoneSortHorizontal = 'solar-bold-duotone-sort_horizontal';
    case BoldduotoneTransferHorizontal = 'solar-bold-duotone-transfer_horizontal';
    case BoldduotoneSquareDoubleAltArrowUp = 'solar-bold-duotone-square_double_alt_arrow_up';
    case BoldduotoneRoundArrowLeftUp = 'solar-bold-duotone-round_arrow_left_up';
    case BoldduotoneAltArrowRight = 'solar-bold-duotone-alt_arrow_right';
    case BoldduotoneRoundDoubleAltArrowUp = 'solar-bold-duotone-round_double_alt_arrow_up';
    case BoldduotoneRestartCircle = 'solar-bold-duotone-restart_circle';
    case BoldduotoneSquareArrowDown = 'solar-bold-duotone-square_arrow_down';
    case BoldduotoneSortVertical = 'solar-bold-duotone-sort_vertical';
    case BoldduotoneSquareSortHorizontal = 'solar-bold-duotone-square_sort_horizontal';
    case BoldduotoneDoubleAltArrowLeft = 'solar-bold-duotone-double_alt_arrow_left';
    case BoldduotoneSquareAltArrowDown = 'solar-bold-duotone-square_alt_arrow_down';
    case BoldduotoneSquareAltArrowRight = 'solar-bold-duotone-square_alt_arrow_right';
    case BoldduotoneSquareArrowUp = 'solar-bold-duotone-square_arrow_up';
    case BoldduotoneDoubleAltArrowRight = 'solar-bold-duotone-double_alt_arrow_right';
    case BoldduotoneRoundTransferVertical = 'solar-bold-duotone-round_transfer_vertical';
    case BoldduotoneArrowLeft = 'solar-bold-duotone-arrow_left';
    case BoldduotoneRoundDoubleAltArrowRight = 'solar-bold-duotone-round_double_alt_arrow_right';
    case BoldduotoneSquareDoubleAltArrowLeft = 'solar-bold-duotone-square_double_alt_arrow_left';
    case BoldduotoneAltArrowDown = 'solar-bold-duotone-alt_arrow_down';
    case BoldduotoneRoundTransferHorizontal = 'solar-bold-duotone-round_transfer_horizontal';
    case BoldduotoneRoundArrowRightDown = 'solar-bold-duotone-round_arrow_right_down';
    case BoldduotoneArrowUp = 'solar-bold-duotone-arrow_up';
    case BoldduotoneRoundArrowLeft = 'solar-bold-duotone-round_arrow_left';
    case BoldduotoneDoubleAltArrowUp = 'solar-bold-duotone-double_alt_arrow_up';
    case BoldduotoneRoundArrowRight = 'solar-bold-duotone-round_arrow_right';
    case BoldduotoneSquareTransferHorizontal = 'solar-bold-duotone-square_transfer_horizontal';
    case BoldduotoneArrowRight = 'solar-bold-duotone-arrow_right';
    case BoldduotoneRoundDoubleAltArrowLeft = 'solar-bold-duotone-round_double_alt_arrow_left';
    case BoldduotoneRoundArrowUp = 'solar-bold-duotone-round_arrow_up';
    case BoldduotoneSquareSortVertical = 'solar-bold-duotone-square_sort_vertical';
    case BoldduotoneAltArrowLeft = 'solar-bold-duotone-alt_arrow_left';
    case BoldduotoneSquareDoubleAltArrowRight = 'solar-bold-duotone-square_double_alt_arrow_right';
    case BoldduotoneRefresh = 'solar-bold-duotone-Refresh';
    case BoldduotoneTransferVertical = 'solar-bold-duotone-transfer_vertical';
    case BoldduotoneRefreshSquare = 'solar-bold-duotone-refresh_square';
    case BoldduotoneSquareTransferVertical = 'solar-bold-duotone-square_transfer_vertical';
    case BoldduotoneSquareDoubleAltArrowDown = 'solar-bold-duotone-square_double_alt_arrow_down';
    case BoldduotoneRoundArrowRightUp = 'solar-bold-duotone-round_arrow_right_up';
    case BoldduotoneArrowDown = 'solar-bold-duotone-arrow_down';
    case BoldduotoneRestartSquare = 'solar-bold-duotone-restart_square';
    case BoldduotoneSquareArrowRight = 'solar-bold-duotone-square_arrow_right';
    case BoldduotoneRoundDoubleAltArrowDown = 'solar-bold-duotone-round_double_alt_arrow_down';
    case BoldduotoneSquareArrowLeftUp = 'solar-bold-duotone-square_arrow_left_up';
    case BoldduotoneRoundArrowDown = 'solar-bold-duotone-round_arrow_down';
    case BoldduotoneSquareArrowRightUp = 'solar-bold-duotone-square_arrow_right_up';
    case BoldduotoneRoundTransferDiagonal = 'solar-bold-duotone-round_transfer_diagonal';
    case BoldduotoneArrowRightDown = 'solar-bold-duotone-arrow_right_down';
    case BoldduotoneArrowLeftDown = 'solar-bold-duotone-arrow_left_down';
    case BoldduotoneRoundAltArrowLeft = 'solar-bold-duotone-round_alt_arrow_left';
    case BoldduotoneArrowRightUp = 'solar-bold-duotone-arrow_right_up';
    case BoldduotoneSquareArrowLeftDown = 'solar-bold-duotone-square_arrow_left_down';
    case BoldduotoneRoundAltArrowUp = 'solar-bold-duotone-round_alt_arrow_up';
    case BoldduotoneAltArrowUp = 'solar-bold-duotone-alt_arrow_up';
    case BoldduotoneSquareAltArrowLeft = 'solar-bold-duotone-square_alt_arrow_left';
    case BoldduotoneRoundSortHorizontal = 'solar-bold-duotone-round_sort_horizontal';
    case BoldduotoneDoubleAltArrowDown = 'solar-bold-duotone-double_alt_arrow_down';
    case BoldduotoneRoundAltArrowRight = 'solar-bold-duotone-round_alt_arrow_right';
    case BoldduotoneSquareArrowLeft = 'solar-bold-duotone-square_arrow_left';
    case BoldduotoneTuningSquare2 = 'solar-bold-duotone-tuning_square2';
    case BoldduotoneWidgetAdd = 'solar-bold-duotone-widget_add';
    case BoldduotoneTuningSquare = 'solar-bold-duotone-tuning_square';
    case BoldduotoneSettingsMinimalistic = 'solar-bold-duotone-settings_minimalistic';
    case BoldduotoneWidget6 = 'solar-bold-duotone-widget6';
    case BoldduotoneWidget4 = 'solar-bold-duotone-widget4';
    case BoldduotoneSettings = 'solar-bold-duotone-Settings';
    case BoldduotoneWidget5 = 'solar-bold-duotone-widget5';
    case BoldduotoneWidget2 = 'solar-bold-duotone-widget2';
    case BoldduotoneWidget3 = 'solar-bold-duotone-widget3';
    case BoldduotoneTuning2 = 'solar-bold-duotone-tuning2';
    case BoldduotoneTuning3 = 'solar-bold-duotone-tuning3';
    case BoldduotoneWidget = 'solar-bold-duotone-Widget';
    case BoldduotoneTuning4 = 'solar-bold-duotone-tuning4';
    case BoldduotoneTuning = 'solar-bold-duotone-Tuning';
    case BoldduotoneDiagramDown = 'solar-bold-duotone-diagram_down';
    case BoldduotoneChart2 = 'solar-bold-duotone-chart2';
    case BoldduotoneChart = 'solar-bold-duotone-Chart';
    case BoldduotoneDiagramUp = 'solar-bold-duotone-diagram_up';
    case BoldduotoneGraphNew = 'solar-bold-duotone-graph_new';
    case BoldduotoneCourseUp = 'solar-bold-duotone-course_up';
    case BoldduotoneGraphDownNew = 'solar-bold-duotone-graph_down_new';
    case BoldduotonePieChart3 = 'solar-bold-duotone-pie_chart3';
    case BoldduotonePieChart2 = 'solar-bold-duotone-pie_chart2';
    case BoldduotoneGraphNewUp = 'solar-bold-duotone-graph_new_up';
    case BoldduotonePieChart = 'solar-bold-duotone-pie_chart';
    case BoldduotoneRoundGraph = 'solar-bold-duotone-round_graph';
    case BoldduotoneGraphUp = 'solar-bold-duotone-graph_up';
    case BoldduotoneChartSquare = 'solar-bold-duotone-chart_square';
    case BoldduotoneCourseDown = 'solar-bold-duotone-course_down';
    case BoldduotoneChatSquare2 = 'solar-bold-duotone-chat_square2';
    case BoldduotoneGraphDown = 'solar-bold-duotone-graph_down';
    case BoldduotoneGraph = 'solar-bold-duotone-Graph';
    case BoldduotonePresentationGraph = 'solar-bold-duotone-presentation_graph';
    case BoldduotoneMaximizeSquare3 = 'solar-bold-duotone-maximize_square3';
    case BoldduotoneMaximizeSquareMinimalistic = 'solar-bold-duotone-maximize_square_minimalistic';
    case BoldduotoneMaximizeSquare2 = 'solar-bold-duotone-maximize_square2';
    case BoldduotoneMinimizeSquare = 'solar-bold-duotone-minimize_square';
    case BoldduotoneDownloadSquare = 'solar-bold-duotone-download_square';
    case BoldduotoneUndoLeftRoundSquare = 'solar-bold-duotone-undo_left_round_square';
    case BoldduotoneReply = 'solar-bold-duotone-Reply';
    case BoldduotoneLogout = 'solar-bold-duotone-Logout';
    case BoldduotoneReciveSquare = 'solar-bold-duotone-recive_square';
    case BoldduotoneExport = 'solar-bold-duotone-Export';
    case BoldduotoneSendTwiceSquare = 'solar-bold-duotone-send_twice_square';
    case BoldduotoneUndoLeftRound = 'solar-bold-duotone-undo_left_round';
    case BoldduotoneForward2 = 'solar-bold-duotone-forward2';
    case BoldduotoneMaximize = 'solar-bold-duotone-Maximize';
    case BoldduotoneUndoRightRound = 'solar-bold-duotone-undo_right_round';
    case BoldduotoneMinimizeSquare2 = 'solar-bold-duotone-minimize_square2';
    case BoldduotoneMinimizeSquare3 = 'solar-bold-duotone-minimize_square3';
    case BoldduotoneUploadTwiceSquare = 'solar-bold-duotone-upload_twice_square';
    case BoldduotoneMinimize = 'solar-bold-duotone-Minimize';
    case BoldduotoneCircleTopUp = 'solar-bold-duotone-circle_top_up';
    case BoldduotoneUploadMinimalistic = 'solar-bold-duotone-upload_minimalistic';
    case BoldduotoneDownload = 'solar-bold-duotone-Download';
    case BoldduotoneImport = 'solar-bold-duotone-Import';
    case BoldduotoneLogin = 'solar-bold-duotone-Login';
    case BoldduotoneUndoLeft = 'solar-bold-duotone-undo_left';
    case BoldduotoneSquareTopUp = 'solar-bold-duotone-square_top_up';
    case BoldduotoneDownloadTwiceSquare = 'solar-bold-duotone-download_twice_square';
    case BoldduotoneCircleBottomDown = 'solar-bold-duotone-circle_bottom_down';
    case BoldduotoneMaximizeSquare = 'solar-bold-duotone-maximize_square';
    case BoldduotoneUploadSquare = 'solar-bold-duotone-upload_square';
    case BoldduotoneUndoRightSquare = 'solar-bold-duotone-undo_right_square';
    case BoldduotoneReciveTwiceSquare = 'solar-bold-duotone-recive_twice_square';
    case BoldduotoneCircleTopDown = 'solar-bold-duotone-circle_top_down';
    case BoldduotoneArrowToDownLeft = 'solar-bold-duotone-arrow_to_down_left';
    case BoldduotoneLogout2 = 'solar-bold-duotone-logout2';
    case BoldduotoneLogout3 = 'solar-bold-duotone-logout3';
    case BoldduotoneScale = 'solar-bold-duotone-Scale';
    case BoldduotoneArrowToDownRight = 'solar-bold-duotone-arrow_to_down_right';
    case BoldduotoneDownloadMinimalistic = 'solar-bold-duotone-download_minimalistic';
    case BoldduotoneMinimizeSquareMinimalistic = 'solar-bold-duotone-minimize_square_minimalistic';
    case BoldduotoneReply2 = 'solar-bold-duotone-reply2';
    case BoldduotoneSquareBottomUp = 'solar-bold-duotone-square_bottom_up';
    case BoldduotoneUndoRight = 'solar-bold-duotone-undo_right';
    case BoldduotoneUndoLeftSquare = 'solar-bold-duotone-undo_left_square';
    case BoldduotoneSendSquare = 'solar-bold-duotone-send_square';
    case BoldduotoneExit = 'solar-bold-duotone-Exit';
    case BoldduotoneSquareBottomDown = 'solar-bold-duotone-square_bottom_down';
    case BoldduotoneUndoRightRoundSquare = 'solar-bold-duotone-undo_right_round_square';
    case BoldduotoneArrowToTopLeft = 'solar-bold-duotone-arrow_to_top_left';
    case BoldduotoneCircleBottomUp = 'solar-bold-duotone-circle_bottom_up';
    case BoldduotoneScreenShare = 'solar-bold-duotone-screen_share';
    case BoldduotoneUpload = 'solar-bold-duotone-Upload';
    case BoldduotoneSquareTopDown = 'solar-bold-duotone-square_top_down';
    case BoldduotoneArrowToTopRight = 'solar-bold-duotone-arrow_to_top_right';
    case BoldduotoneLogin3 = 'solar-bold-duotone-login3';
    case BoldduotoneLogin2 = 'solar-bold-duotone-login2';
    case BoldduotonePassport = 'solar-bold-duotone-Passport';
    case BoldduotoneDiplomaVerified = 'solar-bold-duotone-diploma_verified';
    case BoldduotoneCaseRound = 'solar-bold-duotone-case_round';
    case BoldduotoneBackpack = 'solar-bold-duotone-Backpack';
    case BoldduotoneBook2 = 'solar-bold-duotone-book2';
    case BoldduotoneSquareAcademicCap2 = 'solar-bold-duotone-square_academic_cap2';
    case BoldduotoneCaseRoundMinimalistic = 'solar-bold-duotone-case_round_minimalistic';
    case BoldduotoneCase = 'solar-bold-duotone-Case';
    case BoldduotoneBookBookmarkMinimalistic = 'solar-bold-duotone-book_bookmark_minimalistic';
    case BoldduotoneBookmarkOpened = 'solar-bold-duotone-bookmark_opened';
    case BoldduotoneDiploma = 'solar-bold-duotone-Diploma';
    case BoldduotoneBook = 'solar-bold-duotone-Book';
    case BoldduotoneSquareAcademicCap = 'solar-bold-duotone-square_academic_cap';
    case BoldduotoneBookmarkCircle = 'solar-bold-duotone-bookmark_circle';
    case BoldduotoneCalculatorMinimalistic = 'solar-bold-duotone-calculator_minimalistic';
    case BoldduotoneNotebookSquare = 'solar-bold-duotone-notebook_square';
    case BoldduotoneBookMinimalistic = 'solar-bold-duotone-book_minimalistic';
    case BoldduotoneCaseMinimalistic = 'solar-bold-duotone-case_minimalistic';
    case BoldduotoneNotebookBookmark = 'solar-bold-duotone-notebook_bookmark';
    case BoldduotonePassportMinimalistic = 'solar-bold-duotone-passport_minimalistic';
    case BoldduotoneBookBookmark = 'solar-bold-duotone-book_bookmark';
    case BoldduotoneBookmarkSquareMinimalistic = 'solar-bold-duotone-bookmark_square_minimalistic';
    case BoldduotoneBookmark = 'solar-bold-duotone-Bookmark';
    case BoldduotonePlusMinus = 'solar-bold-duotone-plus,_minus';
    case BoldduotoneCalculator = 'solar-bold-duotone-Calculator';
    case BoldduotoneBookmarkSquare = 'solar-bold-duotone-bookmark_square';
    case BoldduotoneNotebookMinimalistic = 'solar-bold-duotone-notebook_minimalistic';
    case BoldduotoneFireSquare = 'solar-bold-duotone-fire_square';
    case BoldduotoneSuitcaseLines = 'solar-bold-duotone-suitcase_lines';
    case BoldduotoneFire = 'solar-bold-duotone-Fire';
    case BoldduotoneBonfire = 'solar-bold-duotone-Bonfire';
    case BoldduotoneSuitcaseTag = 'solar-bold-duotone-suitcase_tag';
    case BoldduotoneLeaf = 'solar-bold-duotone-Leaf';
    case BoldduotoneSuitcase = 'solar-bold-duotone-Suitcase';
    case BoldduotoneFlame = 'solar-bold-duotone-Flame';
    case BoldduotoneFireMinimalistic = 'solar-bold-duotone-fire_minimalistic';
    case BoldduotoneBellBing = 'solar-bold-duotone-bell_bing';
    case BoldduotoneNotificationLinesRemove = 'solar-bold-duotone-notification_lines_remove';
    case BoldduotoneNotificationUnread = 'solar-bold-duotone-notification_unread';
    case BoldduotoneBell = 'solar-bold-duotone-Bell';
    case BoldduotoneNotificationRemove = 'solar-bold-duotone-notification_remove';
    case BoldduotoneNotificationUnreadLines = 'solar-bold-duotone-notification_unread_lines';
    case BoldduotoneBellOff = 'solar-bold-duotone-bell_off';
    case BoldduotoneLightning = 'solar-bold-duotone-Lightning';
    case BoldduotoneLightbulbMinimalistic = 'solar-bold-duotone-lightbulb_minimalistic';
    case BoldduotoneServerSquareCloud = 'solar-bold-duotone-server_square_cloud';
    case BoldduotoneLightbulbBolt = 'solar-bold-duotone-lightbulb_bolt';
    case BoldduotoneAirbudsCharge = 'solar-bold-duotone-airbuds_charge';
    case BoldduotoneServerPath = 'solar-bold-duotone-server_path';
    case BoldduotoneSimCardMinimalistic = 'solar-bold-duotone-sim_card_minimalistic';
    case BoldduotoneSmartphone = 'solar-bold-duotone-Smartphone';
    case BoldduotoneTurntable = 'solar-bold-duotone-Turntable';
    case BoldduotoneAirbudsCheck = 'solar-bold-duotone-airbuds_check';
    case BoldduotoneMouseMinimalistic = 'solar-bold-duotone-mouse_minimalistic';
    case BoldduotoneSmartphoneRotateAngle = 'solar-bold-duotone-smartphone_rotate_angle';
    case BoldduotoneRadioMinimalistic = 'solar-bold-duotone-radio_minimalistic';
    case BoldduotoneAirbuds = 'solar-bold-duotone-Airbuds';
    case BoldduotoneSmartphoneRotateOrientation = 'solar-bold-duotone-smartphone_rotate_orientation';
    case BoldduotoneIPhone = 'solar-bold-duotone-i_phone';
    case BoldduotoneSimCard = 'solar-bold-duotone-sim_card';
    case BoldduotoneFlashDrive = 'solar-bold-duotone-flash_drive';
    case BoldduotoneDevices = 'solar-bold-duotone-Devices';
    case BoldduotoneSimCards = 'solar-bold-duotone-sim_cards';
    case BoldduotoneAirbudsCaseOpen = 'solar-bold-duotone-airbuds_case_open';
    case BoldduotoneTurntableMusicNote = 'solar-bold-duotone-turntable_music_note';
    case BoldduotoneKeyboard = 'solar-bold-duotone-Keyboard';
    case BoldduotoneGamepadCharge = 'solar-bold-duotone-gamepad_charge';
    case BoldduotoneBoombox = 'solar-bold-duotone-Boombox';
    case BoldduotoneSmartSpeakerMinimalistic = 'solar-bold-duotone-smart_speaker_minimalistic';
    case BoldduotoneTelescope = 'solar-bold-duotone-Telescope';
    case BoldduotoneMonitorCamera = 'solar-bold-duotone-monitor_camera';
    case BoldduotoneLaptopMinimalistic = 'solar-bold-duotone-laptop_minimalistic';
    case BoldduotoneServer2 = 'solar-bold-duotone-server2';
    case BoldduotoneSmartSpeaker = 'solar-bold-duotone-smart_speaker';
    case BoldduotoneProjector = 'solar-bold-duotone-Projector';
    case BoldduotoneServer = 'solar-bold-duotone-Server';
    case BoldduotoneTV = 'solar-bold-duotone-TV';
    case BoldduotoneCassette2 = 'solar-bold-duotone-cassette2';
    case BoldduotoneRadio = 'solar-bold-duotone-Radio';
    case BoldduotoneSmartphoneVibration = 'solar-bold-duotone-smartphone_vibration';
    case BoldduotoneAirbudsLeft = 'solar-bold-duotone-airbuds_left';
    case BoldduotoneHeadphonesRound = 'solar-bold-duotone-headphones_round';
    case BoldduotoneGameboy = 'solar-bold-duotone-Gameboy';
    case BoldduotoneHeadphonesRoundSound = 'solar-bold-duotone-headphones_round_sound';
    case BoldduotoneCPU = 'solar-bold-duotone-CPU';
    case BoldduotonePrinter2 = 'solar-bold-duotone-printer2';
    case BoldduotoneHeadphonesSquare = 'solar-bold-duotone-headphones_square';
    case BoldduotoneServerSquareUpdate = 'solar-bold-duotone-server_square_update';
    case BoldduotonePrinterMinimalistic = 'solar-bold-duotone-printer_minimalistic';
    case BoldduotoneBluetooth = 'solar-bold-duotone-Bluetooth';
    case BoldduotoneWirelessCharge = 'solar-bold-duotone-wireless_charge';
    case BoldduotoneBluetoothCircle = 'solar-bold-duotone-bluetooth_circle';
    case BoldduotoneAirbudsCaseMinimalistic = 'solar-bold-duotone-airbuds_case_minimalistic';
    case BoldduotoneLightbulb = 'solar-bold-duotone-Lightbulb';
    case BoldduotoneAirbudsRemove = 'solar-bold-duotone-airbuds_remove';
    case BoldduotoneSmartphoneRotate2 = 'solar-bold-duotone-smartphone_rotate2';
    case BoldduotoneSsdSquare = 'solar-bold-duotone-ssd_square';
    case BoldduotonePrinter = 'solar-bold-duotone-Printer';
    case BoldduotoneSmartphone2 = 'solar-bold-duotone-smartphone2';
    case BoldduotoneServerMinimalistic = 'solar-bold-duotone-server_minimalistic';
    case BoldduotoneHeadphonesSquareSound = 'solar-bold-duotone-headphones_square_sound';
    case BoldduotoneDiskette = 'solar-bold-duotone-Diskette';
    case BoldduotoneBluetoothWave = 'solar-bold-duotone-bluetooth_wave';
    case BoldduotoneSmartSpeaker2 = 'solar-bold-duotone-smart_speaker2';
    case BoldduotoneLaptop3 = 'solar-bold-duotone-laptop3';
    case BoldduotoneLaptop2 = 'solar-bold-duotone-laptop2';
    case BoldduotoneMouseCircle = 'solar-bold-duotone-mouse_circle';
    case BoldduotoneTurntableMinimalistic = 'solar-bold-duotone-turntable_minimalistic';
    case BoldduotoneSmartphoneUpdate = 'solar-bold-duotone-smartphone_update';
    case BoldduotoneGamepadMinimalistic = 'solar-bold-duotone-gamepad_minimalistic';
    case BoldduotoneSdCard = 'solar-bold-duotone-sd_card';
    case BoldduotonePlugCircle = 'solar-bold-duotone-plug_circle';
    case BoldduotoneAirbudsCase = 'solar-bold-duotone-airbuds_case';
    case BoldduotoneSsdRound = 'solar-bold-duotone-ssd_round';
    case BoldduotoneLaptop = 'solar-bold-duotone-Laptop';
    case BoldduotoneAirbudsRight = 'solar-bold-duotone-airbuds_right';
    case BoldduotoneDisplay = 'solar-bold-duotone-Display';
    case BoldduotoneMonitorSmartphone = 'solar-bold-duotone-monitor_smartphone';
    case BoldduotoneSocket = 'solar-bold-duotone-Socket';
    case BoldduotoneGamepadOld = 'solar-bold-duotone-gamepad_old';
    case BoldduotoneCpuBolt = 'solar-bold-duotone-cpu_bolt';
    case BoldduotoneAirbudsCaseCharge = 'solar-bold-duotone-airbuds_case_charge';
    case BoldduotoneTablet = 'solar-bold-duotone-Tablet';
    case BoldduotoneWeigher = 'solar-bold-duotone-Weigher';
    case BoldduotoneServerSquare = 'solar-bold-duotone-server_square';
    case BoldduotoneMouse = 'solar-bold-duotone-Mouse';
    case BoldduotoneGamepadNoCharge = 'solar-bold-duotone-gamepad_no_charge';
    case BoldduotoneBluetoothSquare = 'solar-bold-duotone-bluetooth_square';
    case BoldduotoneCloudStorage = 'solar-bold-duotone-cloud_storage';
    case BoldduotoneGamepad = 'solar-bold-duotone-Gamepad';
    case BoldduotoneMonitor = 'solar-bold-duotone-Monitor';
    case BoldduotoneCassette = 'solar-bold-duotone-Cassette';
    // Line Duotone Style (1205 icons)
    case LineduotoneFacemaskCircle = 'solar-line-duotone-facemask_circle';
    case LineduotoneConfoundedCircle = 'solar-line-duotone-confounded_circle';
    case LineduotoneSadSquare = 'solar-line-duotone-sad_square';
    case LineduotoneSleepingCircle = 'solar-line-duotone-sleeping_circle';
    case LineduotoneFaceScanCircle = 'solar-line-duotone-face_scan_circle';
    case LineduotoneSmileCircle = 'solar-line-duotone-smile_circle';
    case LineduotoneStickerSmileCircle = 'solar-line-duotone-sticker_smile_circle';
    case LineduotoneStickerSquare = 'solar-line-duotone-sticker_square';
    case LineduotoneEmojiFunnyCircle = 'solar-line-duotone-emoji_funny_circle';
    case LineduotoneExpressionlessSquare = 'solar-line-duotone-expressionless_square';
    case LineduotoneSleepingSquare = 'solar-line-duotone-sleeping_square';
    case LineduotoneSadCircle = 'solar-line-duotone-sad_circle';
    case LineduotoneFacemaskSquare = 'solar-line-duotone-facemask_square';
    case LineduotoneConfoundedSquare = 'solar-line-duotone-confounded_square';
    case LineduotoneFaceScanSquare = 'solar-line-duotone-face_scan_square';
    case LineduotoneSmileSquare = 'solar-line-duotone-smile_square';
    case LineduotoneStickerSmileCircle2 = 'solar-line-duotone-sticker_smile_circle2';
    case LineduotoneStickerSmileSquare = 'solar-line-duotone-sticker_smile_square';
    case LineduotoneEmojiFunnySquare = 'solar-line-duotone-emoji_funny_square';
    case LineduotoneStickerCircle = 'solar-line-duotone-sticker_circle';
    case LineduotoneExpressionlessCircle = 'solar-line-duotone-expressionless_circle';
    case LineduotoneLike = 'solar-line-duotone-Like';
    case LineduotoneMedalStarSquare = 'solar-line-duotone-medal_star_square';
    case LineduotoneDislike = 'solar-line-duotone-Dislike';
    case LineduotoneStarShine = 'solar-line-duotone-star_shine';
    case LineduotoneHeartAngle = 'solar-line-duotone-heart_angle';
    case LineduotoneMedalRibbon = 'solar-line-duotone-medal_ribbon';
    case LineduotoneHeartShine = 'solar-line-duotone-heart_shine';
    case LineduotoneMedalStarCircle = 'solar-line-duotone-medal_star_circle';
    case LineduotoneMedalRibbonsStar = 'solar-line-duotone-medal_ribbons_star';
    case LineduotoneStar = 'solar-line-duotone-Star';
    case LineduotoneHeartUnlock = 'solar-line-duotone-heart_unlock';
    case LineduotoneMedalRibbonStar = 'solar-line-duotone-medal_ribbon_star';
    case LineduotoneHeartLock = 'solar-line-duotone-heart_lock';
    case LineduotoneHeartBroken = 'solar-line-duotone-heart_broken';
    case LineduotoneHearts = 'solar-line-duotone-Hearts';
    case LineduotoneMedalStar = 'solar-line-duotone-medal_star';
    case LineduotoneHeart = 'solar-line-duotone-Heart';
    case LineduotoneCloset = 'solar-line-duotone-Closet';
    case LineduotoneBed = 'solar-line-duotone-Bed';
    case LineduotoneWashingMachine = 'solar-line-duotone-washing_machine';
    case LineduotoneBedsideTable = 'solar-line-duotone-bedside_table';
    case LineduotoneSofa3 = 'solar-line-duotone-sofa3';
    case LineduotoneSofa2 = 'solar-line-duotone-sofa2';
    case LineduotoneChair2 = 'solar-line-duotone-chair2';
    case LineduotoneBath = 'solar-line-duotone-Bath';
    case LineduotoneSmartVacuumCleaner2 = 'solar-line-duotone-smart_vacuum_cleaner2';
    case LineduotoneCondicioner = 'solar-line-duotone-Condicioner';
    case LineduotoneSmartVacuumCleaner = 'solar-line-duotone-smart_vacuum_cleaner';
    case LineduotoneRemoteController2 = 'solar-line-duotone-remote_controller2';
    case LineduotoneFloorLampMinimalistic = 'solar-line-duotone-floor_lamp_minimalistic';
    case LineduotoneLamp = 'solar-line-duotone-Lamp';
    case LineduotoneBarChair = 'solar-line-duotone-bar_chair';
    case LineduotoneBedsideTable2 = 'solar-line-duotone-bedside_table2';
    case LineduotoneCloset2 = 'solar-line-duotone-closet2';
    case LineduotoneBedsideTable3 = 'solar-line-duotone-bedside_table3';
    case LineduotoneSpeaker = 'solar-line-duotone-Speaker';
    case LineduotoneVolumeKnob = 'solar-line-duotone-volume_knob';
    case LineduotoneArmchair = 'solar-line-duotone-Armchair';
    case LineduotoneSpeakerMinimalistic = 'solar-line-duotone-speaker_minimalistic';
    case LineduotoneRemoteController = 'solar-line-duotone-remote_controller';
    case LineduotoneTrellis = 'solar-line-duotone-Trellis';
    case LineduotoneFloorLamp = 'solar-line-duotone-floor_lamp';
    case LineduotoneCondicioner2 = 'solar-line-duotone-condicioner2';
    case LineduotoneBedsideTable4 = 'solar-line-duotone-bedside_table4';
    case LineduotoneArmchair2 = 'solar-line-duotone-armchair2';
    case LineduotoneWashingMachineMinimalistic = 'solar-line-duotone-washing_machine_minimalistic';
    case LineduotoneChair = 'solar-line-duotone-Chair';
    case LineduotoneRemoteControllerMinimalistic = 'solar-line-duotone-remote_controller_minimalistic';
    case LineduotoneChandelier = 'solar-line-duotone-Chandelier';
    case LineduotoneFridge = 'solar-line-duotone-Fridge';
    case LineduotoneMirror = 'solar-line-duotone-Mirror';
    case LineduotoneSofa = 'solar-line-duotone-Sofa';
    case LineduotoneEarth = 'solar-line-duotone-Earth';
    case LineduotoneStarsLine = 'solar-line-duotone-stars_line';
    case LineduotoneStarFall2 = 'solar-line-duotone-star_fall2';
    case LineduotoneStarFall = 'solar-line-duotone-star_fall';
    case LineduotoneBlackHole3 = 'solar-line-duotone-black_hole3';
    case LineduotoneWomen = 'solar-line-duotone-Women';
    case LineduotoneBlackHole = 'solar-line-duotone-black_hole';
    case LineduotoneStarRings = 'solar-line-duotone-star_rings';
    case LineduotoneBlackHole2 = 'solar-line-duotone-black_hole2';
    case LineduotoneStarFallMinimalistic2 = 'solar-line-duotone-star_fall_minimalistic2';
    case LineduotonePlanet = 'solar-line-duotone-Planet';
    case LineduotoneSatellite = 'solar-line-duotone-Satellite';
    case LineduotoneMen = 'solar-line-duotone-Men';
    case LineduotoneRocket2 = 'solar-line-duotone-rocket2';
    case LineduotoneStars = 'solar-line-duotone-Stars';
    case LineduotoneStarAngle = 'solar-line-duotone-star_angle';
    case LineduotoneInfinity = 'solar-line-duotone-Infinity';
    case LineduotoneUfo2 = 'solar-line-duotone-ufo2';
    case LineduotoneUfo3 = 'solar-line-duotone-ufo3';
    case LineduotoneStarRing = 'solar-line-duotone-star_ring';
    case LineduotonePlanet2 = 'solar-line-duotone-planet2';
    case LineduotonePlanet3 = 'solar-line-duotone-planet3';
    case LineduotoneAsteroid = 'solar-line-duotone-Asteroid';
    case LineduotoneStarsMinimalistic = 'solar-line-duotone-stars_minimalistic';
    case LineduotoneUFO = 'solar-line-duotone-UFO';
    case LineduotonePlanet4 = 'solar-line-duotone-planet4';
    case LineduotoneRocket = 'solar-line-duotone-Rocket';
    case LineduotoneStarFallMinimalistic = 'solar-line-duotone-star_fall_minimalistic';
    case LineduotoneStarRainbow = 'solar-line-duotone-star_rainbow';
    case LineduotoneAtom = 'solar-line-duotone-Atom';
    case LineduotoneStarCircle = 'solar-line-duotone-star_circle';
    case LineduotoneCompassBig = 'solar-line-duotone-compass_big';
    case LineduotoneMapPointSchool = 'solar-line-duotone-map_point_school';
    case LineduotoneSignpost = 'solar-line-duotone-Signpost';
    case LineduotoneMapArrowDown = 'solar-line-duotone-map_arrow_down';
    case LineduotoneMap = 'solar-line-duotone-Map';
    case LineduotoneMapArrowUp = 'solar-line-duotone-map_arrow_up';
    case LineduotonePointOnMapPerspective = 'solar-line-duotone-point_on_map_perspective';
    case LineduotoneRadar = 'solar-line-duotone-Radar';
    case LineduotoneStreets = 'solar-line-duotone-Streets';
    case LineduotoneMapPointWave = 'solar-line-duotone-map_point_wave';
    case LineduotonePeopleNearby = 'solar-line-duotone-people_nearby';
    case LineduotoneStreetsMapPoint = 'solar-line-duotone-streets_map_point';
    case LineduotoneMapPointSearch = 'solar-line-duotone-map_point_search';
    case LineduotoneGPS = 'solar-line-duotone-GPS';
    case LineduotoneMapArrowSquare = 'solar-line-duotone-map_arrow_square';
    case LineduotoneBranchingPathsDown = 'solar-line-duotone-branching_paths_down';
    case LineduotoneMapPointRotate = 'solar-line-duotone-map_point_rotate';
    case LineduotoneGlobal = 'solar-line-duotone-Global';
    case LineduotoneCompassSquare = 'solar-line-duotone-compass_square';
    case LineduotoneRouting3 = 'solar-line-duotone-routing3';
    case LineduotoneRouting2 = 'solar-line-duotone-routing2';
    case LineduotoneMapPointRemove = 'solar-line-duotone-map_point_remove';
    case LineduotoneGlobus = 'solar-line-duotone-Globus';
    case LineduotoneSignpost2 = 'solar-line-duotone-signpost2';
    case LineduotoneRadar2 = 'solar-line-duotone-radar2';
    case LineduotoneStreetsNavigation = 'solar-line-duotone-streets_navigation';
    case LineduotoneMapPoint = 'solar-line-duotone-map_point';
    case LineduotoneMapPointHospital = 'solar-line-duotone-map_point_hospital';
    case LineduotoneCompass = 'solar-line-duotone-Compass';
    case LineduotoneMapPointAdd = 'solar-line-duotone-map_point_add';
    case LineduotoneBranchingPathsUp = 'solar-line-duotone-branching_paths_up';
    case LineduotoneMapPointFavourite = 'solar-line-duotone-map_point_favourite';
    case LineduotoneRoute = 'solar-line-duotone-Route';
    case LineduotonePointOnMap = 'solar-line-duotone-point_on_map';
    case LineduotoneMapArrowRight = 'solar-line-duotone-map_arrow_right';
    case LineduotoneRouting = 'solar-line-duotone-Routing';
    case LineduotoneMapArrowLeft = 'solar-line-duotone-map_arrow_left';
    case LineduotoneIncognito = 'solar-line-duotone-Incognito';
    case LineduotoneLockPassword = 'solar-line-duotone-lock_password';
    case LineduotoneShieldNetwork = 'solar-line-duotone-shield_network';
    case LineduotoneKeyMinimalisticSquare = 'solar-line-duotone-key_minimalistic_square';
    case LineduotoneLockKeyholeUnlocked = 'solar-line-duotone-lock_keyhole_unlocked';
    case LineduotoneLock = 'solar-line-duotone-Lock';
    case LineduotoneShieldKeyhole = 'solar-line-duotone-shield_keyhole';
    case LineduotoneEyeClosed = 'solar-line-duotone-eye_closed';
    case LineduotoneKey = 'solar-line-duotone-Key';
    case LineduotoneShieldMinus = 'solar-line-duotone-shield_minus';
    case LineduotoneShield = 'solar-line-duotone-Shield';
    case LineduotoneLockUnlocked = 'solar-line-duotone-lock_unlocked';
    case LineduotoneBombMinimalistic = 'solar-line-duotone-bomb_minimalistic';
    case LineduotoneShieldStar = 'solar-line-duotone-shield_star';
    case LineduotoneBomb = 'solar-line-duotone-Bomb';
    case LineduotoneKeySquare = 'solar-line-duotone-key_square';
    case LineduotoneLockKeyholeMinimalisticUnlocked = 'solar-line-duotone-lock_keyhole_minimalistic_unlocked';
    case LineduotoneShieldCross = 'solar-line-duotone-shield_cross';
    case LineduotoneObjectScan = 'solar-line-duotone-object_scan';
    case LineduotonePasswordMinimalisticInput = 'solar-line-duotone-password_minimalistic_input';
    case LineduotoneLockPasswordUnlocked = 'solar-line-duotone-lock_password_unlocked';
    case LineduotoneSiren = 'solar-line-duotone-Siren';
    case LineduotoneShieldMinimalistic = 'solar-line-duotone-shield_minimalistic';
    case LineduotoneEyeScan = 'solar-line-duotone-eye_scan';
    case LineduotoneKeyMinimalisticSquare2 = 'solar-line-duotone-key_minimalistic_square2';
    case LineduotoneScanner2 = 'solar-line-duotone-scanner2';
    case LineduotoneKeyMinimalisticSquare3 = 'solar-line-duotone-key_minimalistic_square3';
    case LineduotoneKeyMinimalistic2 = 'solar-line-duotone-key_minimalistic2';
    case LineduotoneCodeScan = 'solar-line-duotone-code_scan';
    case LineduotoneShieldPlus = 'solar-line-duotone-shield_plus';
    case LineduotonePasswordMinimalistic = 'solar-line-duotone-password_minimalistic';
    case LineduotoneEye = 'solar-line-duotone-Eye';
    case LineduotoneQrCode = 'solar-line-duotone-qr_code';
    case LineduotoneShieldCheck = 'solar-line-duotone-shield_check';
    case LineduotoneKeyMinimalistic = 'solar-line-duotone-key_minimalistic';
    case LineduotoneLockKeyhole = 'solar-line-duotone-lock_keyhole';
    case LineduotoneShieldUser = 'solar-line-duotone-shield_user';
    case LineduotoneKeySquare2 = 'solar-line-duotone-key_square2';
    case LineduotoneBombEmoji = 'solar-line-duotone-bomb_emoji';
    case LineduotoneScanner = 'solar-line-duotone-Scanner';
    case LineduotoneShieldUp = 'solar-line-duotone-shield_up';
    case LineduotoneSirenRounded = 'solar-line-duotone-siren_rounded';
    case LineduotoneLockKeyholeMinimalistic = 'solar-line-duotone-lock_keyhole_minimalistic';
    case LineduotonePassword = 'solar-line-duotone-Password';
    case LineduotoneShieldKeyholeMinimalistic = 'solar-line-duotone-shield_keyhole_minimalistic';
    case LineduotoneShieldWarning = 'solar-line-duotone-shield_warning';
    case LineduotonePallete2 = 'solar-line-duotone-pallete2';
    case LineduotoneAlignVerticalSpacing = 'solar-line-duotone-align_vertical_spacing';
    case LineduotoneAlignVerticalCenter = 'solar-line-duotone-align_vertical_center';
    case LineduotoneCropMinimalistic = 'solar-line-duotone-crop_minimalistic';
    case LineduotoneMirrorRight = 'solar-line-duotone-mirror_right';
    case LineduotoneAlignBottom = 'solar-line-duotone-align_bottom';
    case LineduotoneRadialBlur = 'solar-line-duotone-radial_blur';
    case LineduotoneCrop = 'solar-line-duotone-Crop';
    case LineduotoneAlignHorizontaSpacing = 'solar-line-duotone-align_horizonta_spacing';
    case LineduotoneRulerPen = 'solar-line-duotone-ruler_pen';
    case LineduotoneThreeSquares = 'solar-line-duotone-three_squares';
    case LineduotonePaintRoller = 'solar-line-duotone-paint_roller';
    case LineduotoneLayers = 'solar-line-duotone-Layers';
    case LineduotoneFilters = 'solar-line-duotone-Filters';
    case LineduotoneRulerCrossPen = 'solar-line-duotone-ruler_cross_pen';
    case LineduotoneFlipHorizontal = 'solar-line-duotone-flip_horizontal';
    case LineduotoneAlignLeft = 'solar-line-duotone-align_left';
    case LineduotoneRuler = 'solar-line-duotone-Ruler';
    case LineduotonePalette = 'solar-line-duotone-Palette';
    case LineduotoneAlignTop = 'solar-line-duotone-align_top';
    case LineduotoneAlignHorizontalCenter = 'solar-line-duotone-align_horizontal_center';
    case LineduotoneAlignRight = 'solar-line-duotone-align_right';
    case LineduotoneRulerAngular = 'solar-line-duotone-ruler_angular';
    case LineduotonePipette = 'solar-line-duotone-Pipette';
    case LineduotoneFlipVertical = 'solar-line-duotone-flip_vertical';
    case LineduotoneMirrorLeft = 'solar-line-duotone-mirror_left';
    case LineduotoneLayersMinimalistic = 'solar-line-duotone-layers_minimalistic';
    case LineduotoneColourTuneing = 'solar-line-duotone-colour_tuneing';
    case LineduotonePaletteRound = 'solar-line-duotone-palette_round';
    case LineduotoneEraser = 'solar-line-duotone-Eraser';
    case LineduotoneTextItalicCircle = 'solar-line-duotone-text_italic_circle';
    case LineduotoneLinkRound = 'solar-line-duotone-link_round';
    case LineduotoneTextItalic = 'solar-line-duotone-text_italic';
    case LineduotoneLinkBrokenMinimalistic = 'solar-line-duotone-link_broken_minimalistic';
    case LineduotoneTextUnderlineCross = 'solar-line-duotone-text_underline_cross';
    case LineduotoneLink = 'solar-line-duotone-Link';
    case LineduotoneEraserCircle = 'solar-line-duotone-eraser_circle';
    case LineduotoneLinkCircle = 'solar-line-duotone-link_circle';
    case LineduotoneTextBoldCircle = 'solar-line-duotone-text_bold_circle';
    case LineduotoneTextField = 'solar-line-duotone-text_field';
    case LineduotoneTextSquare = 'solar-line-duotone-text_square';
    case LineduotoneTextSquare2 = 'solar-line-duotone-text_square2';
    case LineduotoneLinkRoundAngle = 'solar-line-duotone-link_round_angle';
    case LineduotoneTextUnderlineCircle = 'solar-line-duotone-text_underline_circle';
    case LineduotoneTextCrossCircle = 'solar-line-duotone-text_cross_circle';
    case LineduotoneTextItalicSquare = 'solar-line-duotone-text_italic_square';
    case LineduotoneParagraphSpacing = 'solar-line-duotone-paragraph_spacing';
    case LineduotoneText = 'solar-line-duotone-Text';
    case LineduotoneLinkBroken = 'solar-line-duotone-link_broken';
    case LineduotoneTextCross = 'solar-line-duotone-text_cross';
    case LineduotoneTextUnderline = 'solar-line-duotone-text_underline';
    case LineduotoneLinkMinimalistic = 'solar-line-duotone-link_minimalistic';
    case LineduotoneLinkMinimalistic2 = 'solar-line-duotone-link_minimalistic2';
    case LineduotoneTextBold = 'solar-line-duotone-text_bold';
    case LineduotoneTextSelection = 'solar-line-duotone-text_selection';
    case LineduotoneTextFieldFocus = 'solar-line-duotone-text_field_focus';
    case LineduotoneTextBoldSquare = 'solar-line-duotone-text_bold_square';
    case LineduotoneEraserSquare = 'solar-line-duotone-eraser_square';
    case LineduotoneLinkSquare = 'solar-line-duotone-link_square';
    case LineduotoneTextCircle = 'solar-line-duotone-text_circle';
    case LineduotoneBackspace = 'solar-line-duotone-Backspace';
    case LineduotoneTextCrossSquare = 'solar-line-duotone-text_cross_square';
    case LineduotoneInboxUnread = 'solar-line-duotone-inbox_unread';
    case LineduotoneChatUnread = 'solar-line-duotone-chat_unread';
    case LineduotoneChatRound = 'solar-line-duotone-chat_round';
    case LineduotoneUnread = 'solar-line-duotone-Unread';
    case LineduotoneMailbox = 'solar-line-duotone-Mailbox';
    case LineduotoneLetter = 'solar-line-duotone-Letter';
    case LineduotonePenNewRound = 'solar-line-duotone-pen_new_round';
    case LineduotoneMultipleForwardRight = 'solar-line-duotone-multiple_forward_right';
    case LineduotoneMultipleForwardLeft = 'solar-line-duotone-multiple_forward_left';
    case LineduotoneInboxArchive = 'solar-line-duotone-inbox_archive';
    case LineduotoneInbox = 'solar-line-duotone-Inbox';
    case LineduotonePen2 = 'solar-line-duotone-pen2';
    case LineduotonePenNewSquare = 'solar-line-duotone-pen_new_square';
    case LineduotonePen = 'solar-line-duotone-Pen';
    case LineduotoneChatDots = 'solar-line-duotone-chat_dots';
    case LineduotoneChatSquareCall = 'solar-line-duotone-chat_square_call';
    case LineduotoneSquareShareLine = 'solar-line-duotone-square_share_line';
    case LineduotoneChatRoundCheck = 'solar-line-duotone-chat_round_check';
    case LineduotoneInboxOut = 'solar-line-duotone-inbox_out';
    case LineduotonePlain3 = 'solar-line-duotone-plain3';
    case LineduotoneChatRoundDots = 'solar-line-duotone-chat_round_dots';
    case LineduotoneChatRoundLike = 'solar-line-duotone-chat_round_like';
    case LineduotonePlain2 = 'solar-line-duotone-plain2';
    case LineduotoneChatRoundUnread = 'solar-line-duotone-chat_round_unread';
    case LineduotoneChatSquareLike = 'solar-line-duotone-chat_square_like';
    case LineduotonePaperclip = 'solar-line-duotone-Paperclip';
    case LineduotoneChatSquareCheck = 'solar-line-duotone-chat_square_check';
    case LineduotoneChatSquare = 'solar-line-duotone-chat_square';
    case LineduotoneLetterOpened = 'solar-line-duotone-letter_opened';
    case LineduotoneSquareForward = 'solar-line-duotone-square_forward';
    case LineduotoneLetterUnread = 'solar-line-duotone-letter_unread';
    case LineduotonePaperclipRounded2 = 'solar-line-duotone-paperclip_rounded2';
    case LineduotoneChatRoundCall = 'solar-line-duotone-chat_round_call';
    case LineduotoneInboxLine = 'solar-line-duotone-inbox_line';
    case LineduotoneChatRoundVideo = 'solar-line-duotone-chat_round_video';
    case LineduotoneChatRoundMoney = 'solar-line-duotone-chat_round_money';
    case LineduotoneInboxIn = 'solar-line-duotone-inbox_in';
    case LineduotoneCheckRead = 'solar-line-duotone-check_read';
    case LineduotoneChatRoundLine = 'solar-line-duotone-chat_round_line';
    case LineduotoneForward = 'solar-line-duotone-Forward';
    case LineduotonePaperclip2 = 'solar-line-duotone-paperclip2';
    case LineduotoneDialog2 = 'solar-line-duotone-dialog2';
    case LineduotoneDialog = 'solar-line-duotone-Dialog';
    case LineduotonePaperclipRounded = 'solar-line-duotone-paperclip_rounded';
    case LineduotonePlain = 'solar-line-duotone-Plain';
    case LineduotoneChatSquareArrow = 'solar-line-duotone-chat_square_arrow';
    case LineduotoneChatSquareCode = 'solar-line-duotone-chat_square_code';
    case LineduotoneChatLine = 'solar-line-duotone-chat_line';
    case LineduotoneTennis = 'solar-line-duotone-Tennis';
    case LineduotoneBicyclingRound = 'solar-line-duotone-bicycling_round';
    case LineduotoneBalls = 'solar-line-duotone-Balls';
    case LineduotoneMeditationRound = 'solar-line-duotone-meditation_round';
    case LineduotoneStretchingRound = 'solar-line-duotone-stretching_round';
    case LineduotoneDumbbells2 = 'solar-line-duotone-dumbbells2';
    case LineduotoneMeditation = 'solar-line-duotone-Meditation';
    case LineduotoneRunning2 = 'solar-line-duotone-running2';
    case LineduotoneRugby = 'solar-line-duotone-Rugby';
    case LineduotoneBodyShapeMinimalistic = 'solar-line-duotone-body_shape_minimalistic';
    case LineduotoneStretching = 'solar-line-duotone-Stretching';
    case LineduotoneBowling = 'solar-line-duotone-Bowling';
    case LineduotoneRanking = 'solar-line-duotone-Ranking';
    case LineduotoneTreadmillRound = 'solar-line-duotone-treadmill_round';
    case LineduotoneVolleyball = 'solar-line-duotone-Volleyball';
    case LineduotoneDumbbellLargeMinimalistic = 'solar-line-duotone-dumbbell_large_minimalistic';
    case LineduotoneRunningRound = 'solar-line-duotone-running_round';
    case LineduotoneHiking = 'solar-line-duotone-Hiking';
    case LineduotoneHikingMinimalistic = 'solar-line-duotone-hiking_minimalistic';
    case LineduotoneWaterSun = 'solar-line-duotone-water_sun';
    case LineduotoneGolf = 'solar-line-duotone-Golf';
    case LineduotoneSkateboarding = 'solar-line-duotone-Skateboarding';
    case LineduotoneDumbbells = 'solar-line-duotone-Dumbbells';
    case LineduotoneWalkingRound = 'solar-line-duotone-walking_round';
    case LineduotoneRunning = 'solar-line-duotone-Running';
    case LineduotoneTreadmill = 'solar-line-duotone-Treadmill';
    case LineduotoneSkateboard = 'solar-line-duotone-Skateboard';
    case LineduotoneDumbbellSmall = 'solar-line-duotone-dumbbell_small';
    case LineduotoneBasketball = 'solar-line-duotone-Basketball';
    case LineduotoneFootball = 'solar-line-duotone-Football';
    case LineduotoneDumbbell = 'solar-line-duotone-Dumbbell';
    case LineduotoneBodyShape = 'solar-line-duotone-body_shape';
    case LineduotoneWater = 'solar-line-duotone-Water';
    case LineduotoneSkateboardingRound = 'solar-line-duotone-skateboarding_round';
    case LineduotoneHikingRound = 'solar-line-duotone-hiking_round';
    case LineduotoneVolleyball2 = 'solar-line-duotone-volleyball2';
    case LineduotoneTennis2 = 'solar-line-duotone-tennis2';
    case LineduotoneSwimming = 'solar-line-duotone-Swimming';
    case LineduotoneBicycling = 'solar-line-duotone-Bicycling';
    case LineduotoneWalking = 'solar-line-duotone-Walking';
    case LineduotoneDumbbellLarge = 'solar-line-duotone-dumbbell_large';
    case LineduotoneCalendarMark = 'solar-line-duotone-calendar_mark';
    case LineduotoneHistory2 = 'solar-line-duotone-history2';
    case LineduotoneWatchSquareMinimalisticCharge = 'solar-line-duotone-watch_square_minimalistic_charge';
    case LineduotoneHistory3 = 'solar-line-duotone-history3';
    case LineduotoneHourglass = 'solar-line-duotone-Hourglass';
    case LineduotoneCalendarSearch = 'solar-line-duotone-calendar_search';
    case LineduotoneStopwatchPlay = 'solar-line-duotone-stopwatch_play';
    case LineduotoneWatchRound = 'solar-line-duotone-watch_round';
    case LineduotoneCalendarAdd = 'solar-line-duotone-calendar_add';
    case LineduotoneCalendarDate = 'solar-line-duotone-calendar_date';
    case LineduotoneStopwatch = 'solar-line-duotone-Stopwatch';
    case LineduotoneAlarmPause = 'solar-line-duotone-alarm_pause';
    case LineduotoneAlarmTurnOff = 'solar-line-duotone-alarm_turn_off';
    case LineduotoneClockSquare = 'solar-line-duotone-clock_square';
    case LineduotoneStopwatchPause = 'solar-line-duotone-stopwatch_pause';
    case LineduotoneCalendarMinimalistic = 'solar-line-duotone-calendar_minimalistic';
    case LineduotoneAlarmAdd = 'solar-line-duotone-alarm_add';
    case LineduotoneAlarmPlay = 'solar-line-duotone-alarm_play';
    case LineduotoneHourglassLine = 'solar-line-duotone-hourglass_line';
    case LineduotoneAlarmSleep = 'solar-line-duotone-alarm_sleep';
    case LineduotoneAlarmRemove = 'solar-line-duotone-alarm_remove';
    case LineduotoneCalendar = 'solar-line-duotone-Calendar';
    case LineduotoneClockCircle = 'solar-line-duotone-clock_circle';
    case LineduotoneHistory = 'solar-line-duotone-History';
    case LineduotoneAlarm = 'solar-line-duotone-Alarm';
    case LineduotoneWatchSquare = 'solar-line-duotone-watch_square';
    case LineduotoneWatchSquareMinimalistic = 'solar-line-duotone-watch_square_minimalistic';
    case LineduotoneMagniferBug = 'solar-line-duotone-magnifer_bug';
    case LineduotoneMagnifer = 'solar-line-duotone-Magnifer';
    case LineduotoneMagniferZoomIn = 'solar-line-duotone-magnifer_zoom_in';
    case LineduotoneRoundedMagnifer = 'solar-line-duotone-rounded_magnifer';
    case LineduotoneRoundedMagniferZoomIn = 'solar-line-duotone-rounded_magnifer_zoom_in';
    case LineduotoneMinimalisticMagniferBug = 'solar-line-duotone-minimalistic_magnifer_bug';
    case LineduotoneRoundedMagniferBug = 'solar-line-duotone-rounded_magnifer_bug';
    case LineduotoneMinimalisticMagniferZoomOut = 'solar-line-duotone-minimalistic_magnifer_zoom_out';
    case LineduotoneMinimalisticMagnifer = 'solar-line-duotone-minimalistic_magnifer';
    case LineduotoneRoundedMagniferZoomOut = 'solar-line-duotone-rounded_magnifer_zoom_out';
    case LineduotoneMinimalisticMagniferZoomIn = 'solar-line-duotone-minimalistic_magnifer_zoom_in';
    case LineduotoneMagniferZoomOut = 'solar-line-duotone-magnifer_zoom_out';
    case LineduotoneBagCheck = 'solar-line-duotone-bag_check';
    case LineduotoneShopMinimalistic = 'solar-line-duotone-shop_minimalistic';
    case LineduotoneShop = 'solar-line-duotone-Shop';
    case LineduotoneCartCheck = 'solar-line-duotone-cart_check';
    case LineduotoneCart = 'solar-line-duotone-Cart';
    case LineduotoneCart3 = 'solar-line-duotone-cart3';
    case LineduotoneCart2 = 'solar-line-duotone-cart2';
    case LineduotoneBagMusic = 'solar-line-duotone-bag_music';
    case LineduotoneCartLargeMinimalistic = 'solar-line-duotone-cart_large_minimalistic';
    case LineduotoneCart5 = 'solar-line-duotone-cart5';
    case LineduotoneCart4 = 'solar-line-duotone-cart4';
    case LineduotoneBag = 'solar-line-duotone-Bag';
    case LineduotoneBagHeart = 'solar-line-duotone-bag_heart';
    case LineduotoneCartPlus = 'solar-line-duotone-cart_plus';
    case LineduotoneCartLarge = 'solar-line-duotone-cart_large';
    case LineduotoneBagCross = 'solar-line-duotone-bag_cross';
    case LineduotoneBagMusic2 = 'solar-line-duotone-bag_music2';
    case LineduotoneBag5 = 'solar-line-duotone-bag5';
    case LineduotoneBag4 = 'solar-line-duotone-bag4';
    case LineduotoneCartLarge4 = 'solar-line-duotone-cart_large4';
    case LineduotoneCartLarge3 = 'solar-line-duotone-cart_large3';
    case LineduotoneBag3 = 'solar-line-duotone-bag3';
    case LineduotoneBag2 = 'solar-line-duotone-bag2';
    case LineduotoneShop2 = 'solar-line-duotone-shop2';
    case LineduotoneCartLarge2 = 'solar-line-duotone-cart_large2';
    case LineduotoneBagSmile = 'solar-line-duotone-bag_smile';
    case LineduotoneCartCross = 'solar-line-duotone-cart_cross';
    case LineduotoneInfoSquare = 'solar-line-duotone-info_square';
    case LineduotoneFlashlightOn = 'solar-line-duotone-flashlight_on';
    case LineduotoneXXX = 'solar-line-duotone-XXX';
    case LineduotoneFigma = 'solar-line-duotone-Figma';
    case LineduotoneFlashlight = 'solar-line-duotone-Flashlight';
    case LineduotoneGhost = 'solar-line-duotone-Ghost';
    case LineduotoneCupMusic = 'solar-line-duotone-cup_music';
    case LineduotoneBatteryFullMinimalistic = 'solar-line-duotone-battery_full_minimalistic';
    case LineduotoneDangerCircle = 'solar-line-duotone-danger_circle';
    case LineduotoneCheckSquare = 'solar-line-duotone-check_square';
    case LineduotoneGhostSmile = 'solar-line-duotone-ghost_smile';
    case LineduotoneTarget = 'solar-line-duotone-Target';
    case LineduotoneBatteryHalfMinimalistic = 'solar-line-duotone-battery_half_minimalistic';
    case LineduotoneScissors = 'solar-line-duotone-Scissors';
    case LineduotonePinList = 'solar-line-duotone-pin_list';
    case LineduotoneBatteryCharge = 'solar-line-duotone-battery_charge';
    case LineduotoneUmbrella = 'solar-line-duotone-Umbrella';
    case LineduotoneHomeSmile = 'solar-line-duotone-home_smile';
    case LineduotoneHome = 'solar-line-duotone-Home';
    case LineduotoneCopyright = 'solar-line-duotone-Copyright';
    case LineduotoneHomeWifi = 'solar-line-duotone-home_wifi';
    case LineduotoneTShirt = 'solar-line-duotone-t_shirt';
    case LineduotoneBatteryChargeMinimalistic = 'solar-line-duotone-battery_charge_minimalistic';
    case LineduotoneCupStar = 'solar-line-duotone-cup_star';
    case LineduotoneSpecialEffects = 'solar-line-duotone-special_effects';
    case LineduotoneBody = 'solar-line-duotone-Body';
    case LineduotoneHamburgerMenu = 'solar-line-duotone-hamburger_menu';
    case LineduotonePower = 'solar-line-duotone-Power';
    case LineduotoneDatabase = 'solar-line-duotone-Database';
    case LineduotoneCursorSquare = 'solar-line-duotone-cursor_square';
    case LineduotoneFuel = 'solar-line-duotone-Fuel';
    case LineduotoneMentionCircle = 'solar-line-duotone-mention_circle';
    case LineduotoneConfettiMinimalistic = 'solar-line-duotone-confetti_minimalistic';
    case LineduotoneMenuDotsCircle = 'solar-line-duotone-menu_dots_circle';
    case LineduotonePaw = 'solar-line-duotone-Paw';
    case LineduotoneSubtitles = 'solar-line-duotone-Subtitles';
    case LineduotoneSliderVerticalMinimalistic = 'solar-line-duotone-slider_vertical_minimalistic';
    case LineduotoneCrownMinimalistic = 'solar-line-duotone-crown_minimalistic';
    case LineduotoneMenuDots = 'solar-line-duotone-menu_dots';
    case LineduotoneDelivery = 'solar-line-duotone-Delivery';
    case LineduotoneWaterdrop = 'solar-line-duotone-Waterdrop';
    case LineduotonePerfume = 'solar-line-duotone-Perfume';
    case LineduotoneHomeAngle2 = 'solar-line-duotone-home_angle2';
    case LineduotoneHomeWifiAngle = 'solar-line-duotone-home_wifi_angle';
    case LineduotoneQuestionCircle = 'solar-line-duotone-question_circle';
    case LineduotoneTrashBinMinimalistic = 'solar-line-duotone-trash_bin_minimalistic';
    case LineduotoneMagicStick3 = 'solar-line-duotone-magic_stick3';
    case LineduotoneAddSquare = 'solar-line-duotone-add_square';
    case LineduotoneCrownStar = 'solar-line-duotone-crown_star';
    case LineduotoneMagnet = 'solar-line-duotone-Magnet';
    case LineduotoneConfetti = 'solar-line-duotone-Confetti';
    case LineduotonePin = 'solar-line-duotone-Pin';
    case LineduotoneMinusSquare = 'solar-line-duotone-minus_square';
    case LineduotoneBolt = 'solar-line-duotone-Bolt';
    case LineduotoneCloseCircle = 'solar-line-duotone-close_circle';
    case LineduotoneForbiddenCircle = 'solar-line-duotone-forbidden_circle';
    case LineduotoneMagicStick2 = 'solar-line-duotone-magic_stick2';
    case LineduotoneCrownLine = 'solar-line-duotone-crown_line';
    case LineduotoneBoltCircle = 'solar-line-duotone-bolt_circle';
    case LineduotoneFlag = 'solar-line-duotone-Flag';
    case LineduotoneSliderHorizontal = 'solar-line-duotone-slider_horizontal';
    case LineduotoneHighDefinition = 'solar-line-duotone-high_definition';
    case LineduotoneCursor = 'solar-line-duotone-Cursor';
    case LineduotoneFeed = 'solar-line-duotone-Feed';
    case LineduotoneTrafficEconomy = 'solar-line-duotone-traffic_economy';
    case LineduotoneAugmentedReality = 'solar-line-duotone-augmented_reality';
    case LineduotoneIcon4K = 'solar-line-duotone-4_k';
    case LineduotoneMagnetWave = 'solar-line-duotone-magnet_wave';
    case LineduotoneHomeSmileAngle = 'solar-line-duotone-home_smile_angle';
    case LineduotoneSliderVertical = 'solar-line-duotone-slider_vertical';
    case LineduotoneCheckCircle = 'solar-line-duotone-check_circle';
    case LineduotoneCopy = 'solar-line-duotone-Copy';
    case LineduotoneDangerSquare = 'solar-line-duotone-danger_square';
    case LineduotoneSkirt = 'solar-line-duotone-Skirt';
    case LineduotoneGlasses = 'solar-line-duotone-Glasses';
    case LineduotoneHomeAdd = 'solar-line-duotone-home_add';
    case LineduotoneSledgehammer = 'solar-line-duotone-Sledgehammer';
    case LineduotoneInfoCircle = 'solar-line-duotone-info_circle';
    case LineduotoneDangerTriangle = 'solar-line-duotone-danger_triangle';
    case LineduotonePinCircle = 'solar-line-duotone-pin_circle';
    case LineduotoneSmartHome = 'solar-line-duotone-smart_home';
    case LineduotoneScissorsSquare = 'solar-line-duotone-scissors_square';
    case LineduotoneSleeping = 'solar-line-duotone-Sleeping';
    case LineduotoneBox = 'solar-line-duotone-Box';
    case LineduotoneCrown = 'solar-line-duotone-Crown';
    case LineduotoneBroom = 'solar-line-duotone-Broom';
    case LineduotonePostsCarouselHorizontal = 'solar-line-duotone-posts_carousel_horizontal';
    case LineduotoneFlag2 = 'solar-line-duotone-flag2';
    case LineduotonePlate = 'solar-line-duotone-Plate';
    case LineduotoneTrashBinTrash = 'solar-line-duotone-trash_bin_trash';
    case LineduotoneCupFirst = 'solar-line-duotone-cup_first';
    case LineduotoneSmartHomeAngle = 'solar-line-duotone-smart_home_angle';
    case LineduotonePaperBin = 'solar-line-duotone-paper_bin';
    case LineduotoneBoxMinimalistic = 'solar-line-duotone-box_minimalistic';
    case LineduotoneDanger = 'solar-line-duotone-Danger';
    case LineduotoneMenuDotsSquare = 'solar-line-duotone-menu_dots_square';
    case LineduotoneHanger2 = 'solar-line-duotone-hanger2';
    case LineduotoneBatteryHalf = 'solar-line-duotone-battery_half';
    case LineduotoneHome2 = 'solar-line-duotone-home2';
    case LineduotonePostsCarouselVertical = 'solar-line-duotone-posts_carousel_vertical';
    case LineduotoneRevote = 'solar-line-duotone-Revote';
    case LineduotoneMentionSquare = 'solar-line-duotone-mention_square';
    case LineduotoneWinRar = 'solar-line-duotone-win_rar';
    case LineduotoneForbidden = 'solar-line-duotone-Forbidden';
    case LineduotoneQuestionSquare = 'solar-line-duotone-question_square';
    case LineduotoneHanger = 'solar-line-duotone-Hanger';
    case LineduotoneReorder = 'solar-line-duotone-Reorder';
    case LineduotoneHomeAddAngle = 'solar-line-duotone-home_add_angle';
    case LineduotoneMasks = 'solar-line-duotone-Masks';
    case LineduotoneGift = 'solar-line-duotone-Gift';
    case LineduotoneCreativeCommons = 'solar-line-duotone-creative_commons';
    case LineduotoneSliderMinimalisticHorizontal = 'solar-line-duotone-slider_minimalistic_horizontal';
    case LineduotoneHomeAngle = 'solar-line-duotone-home_angle';
    case LineduotoneBatteryLowMinimalistic = 'solar-line-duotone-battery_low_minimalistic';
    case LineduotoneShare = 'solar-line-duotone-Share';
    case LineduotoneTrashBin2 = 'solar-line-duotone-trash_bin2';
    case LineduotoneSort = 'solar-line-duotone-Sort';
    case LineduotoneMinusCircle = 'solar-line-duotone-minus_circle';
    case LineduotoneExplicit = 'solar-line-duotone-Explicit';
    case LineduotoneTraffic = 'solar-line-duotone-Traffic';
    case LineduotoneFilter = 'solar-line-duotone-Filter';
    case LineduotoneCloseSquare = 'solar-line-duotone-close_square';
    case LineduotoneAddCircle = 'solar-line-duotone-add_circle';
    case LineduotoneFerrisWheel = 'solar-line-duotone-ferris_wheel';
    case LineduotoneCup = 'solar-line-duotone-Cup';
    case LineduotoneBalloon = 'solar-line-duotone-Balloon';
    case LineduotoneHelp = 'solar-line-duotone-Help';
    case LineduotoneBatteryFull = 'solar-line-duotone-battery_full';
    case LineduotoneCat = 'solar-line-duotone-Cat';
    case LineduotoneMaskSad = 'solar-line-duotone-mask_sad';
    case LineduotoneHighQuality = 'solar-line-duotone-high_quality';
    case LineduotoneMagicStick = 'solar-line-duotone-magic_stick';
    case LineduotoneCosmetic = 'solar-line-duotone-Cosmetic';
    case LineduotoneBatteryLow = 'solar-line-duotone-battery_low';
    case LineduotoneShareCircle = 'solar-line-duotone-share_circle';
    case LineduotoneMaskHapply = 'solar-line-duotone-mask_happly';
    case LineduotoneAccessibility = 'solar-line-duotone-Accessibility';
    case LineduotoneTrashBinMinimalistic2 = 'solar-line-duotone-trash_bin_minimalistic2';
    case LineduotoneIncomingCallRounded = 'solar-line-duotone-incoming_call_rounded';
    case LineduotoneCallDropped = 'solar-line-duotone-call_dropped';
    case LineduotoneCallChat = 'solar-line-duotone-call_chat';
    case LineduotoneCallCancelRounded = 'solar-line-duotone-call_cancel_rounded';
    case LineduotoneCallMedicineRounded = 'solar-line-duotone-call_medicine_rounded';
    case LineduotoneCallDroppedRounded = 'solar-line-duotone-call_dropped_rounded';
    case LineduotoneRecordSquare = 'solar-line-duotone-record_square';
    case LineduotonePhoneCalling = 'solar-line-duotone-phone_calling';
    case LineduotonePhoneRounded = 'solar-line-duotone-phone_rounded';
    case LineduotoneCallMedicine = 'solar-line-duotone-call_medicine';
    case LineduotoneRecordMinimalistic = 'solar-line-duotone-record_minimalistic';
    case LineduotoneEndCall = 'solar-line-duotone-end_call';
    case LineduotoneOutgoingCall = 'solar-line-duotone-outgoing_call';
    case LineduotoneRecordCircle = 'solar-line-duotone-record_circle';
    case LineduotoneIncomingCall = 'solar-line-duotone-incoming_call';
    case LineduotoneCallChatRounded = 'solar-line-duotone-call_chat_rounded';
    case LineduotoneEndCallRounded = 'solar-line-duotone-end_call_rounded';
    case LineduotonePhone = 'solar-line-duotone-Phone';
    case LineduotoneOutgoingCallRounded = 'solar-line-duotone-outgoing_call_rounded';
    case LineduotoneCallCancel = 'solar-line-duotone-call_cancel';
    case LineduotonePhoneCallingRounded = 'solar-line-duotone-phone_calling_rounded';
    case LineduotoneStationMinimalistic = 'solar-line-duotone-station_minimalistic';
    case LineduotoneSidebarCode = 'solar-line-duotone-sidebar_code';
    case LineduotoneWiFiRouterMinimalistic = 'solar-line-duotone-wi_fi_router_minimalistic';
    case LineduotoneUSB = 'solar-line-duotone-USB';
    case LineduotoneSiderbar = 'solar-line-duotone-Siderbar';
    case LineduotoneCode2 = 'solar-line-duotone-code2';
    case LineduotoneSlashCircle = 'solar-line-duotone-slash_circle';
    case LineduotoneScreencast = 'solar-line-duotone-Screencast';
    case LineduotoneHashtagSquare = 'solar-line-duotone-hashtag_square';
    case LineduotoneSidebarMinimalistic = 'solar-line-duotone-sidebar_minimalistic';
    case LineduotoneCode = 'solar-line-duotone-Code';
    case LineduotoneUsbSquare = 'solar-line-duotone-usb_square';
    case LineduotoneWiFiRouter = 'solar-line-duotone-wi_fi_router';
    case LineduotoneCodeCircle = 'solar-line-duotone-code_circle';
    case LineduotoneTranslation = 'solar-line-duotone-Translation';
    case LineduotoneBugMinimalistic = 'solar-line-duotone-bug_minimalistic';
    case LineduotoneStation = 'solar-line-duotone-Station';
    case LineduotoneProgramming = 'solar-line-duotone-Programming';
    case LineduotoneWiFiRouterRound = 'solar-line-duotone-wi_fi_router_round';
    case LineduotoneHashtag = 'solar-line-duotone-Hashtag';
    case LineduotoneBug = 'solar-line-duotone-Bug';
    case LineduotoneHashtagChat = 'solar-line-duotone-hashtag_chat';
    case LineduotoneCommand = 'solar-line-duotone-Command';
    case LineduotoneTranslation2 = 'solar-line-duotone-translation2';
    case LineduotoneHashtagCircle = 'solar-line-duotone-hashtag_circle';
    case LineduotoneScreencast2 = 'solar-line-duotone-screencast2';
    case LineduotoneSlashSquare = 'solar-line-duotone-slash_square';
    case LineduotoneWindowFrame = 'solar-line-duotone-window_frame';
    case LineduotoneStructure = 'solar-line-duotone-Structure';
    case LineduotoneUsbCircle = 'solar-line-duotone-usb_circle';
    case LineduotoneCodeSquare = 'solar-line-duotone-code_square';
    case LineduotoneNotes = 'solar-line-duotone-Notes';
    case LineduotoneDocumentText = 'solar-line-duotone-document_text';
    case LineduotoneDocumentAdd = 'solar-line-duotone-document_add';
    case LineduotoneDocumentMedicine = 'solar-line-duotone-document_medicine';
    case LineduotoneArchiveMinimalistic = 'solar-line-duotone-archive_minimalistic';
    case LineduotoneClipboard = 'solar-line-duotone-Clipboard';
    case LineduotoneClipboardAdd = 'solar-line-duotone-clipboard_add';
    case LineduotoneArchive = 'solar-line-duotone-Archive';
    case LineduotoneClipboardHeart = 'solar-line-duotone-clipboard_heart';
    case LineduotoneClipboardRemove = 'solar-line-duotone-clipboard_remove';
    case LineduotoneClipboardText = 'solar-line-duotone-clipboard_text';
    case LineduotoneDocument = 'solar-line-duotone-Document';
    case LineduotoneNotesMinimalistic = 'solar-line-duotone-notes_minimalistic';
    case LineduotoneArchiveUp = 'solar-line-duotone-archive_up';
    case LineduotoneArchiveUpMinimlistic = 'solar-line-duotone-archive_up_minimlistic';
    case LineduotoneArchiveCheck = 'solar-line-duotone-archive_check';
    case LineduotoneArchiveDown = 'solar-line-duotone-archive_down';
    case LineduotoneArchiveDownMinimlistic = 'solar-line-duotone-archive_down_minimlistic';
    case LineduotoneDocumentsMinimalistic = 'solar-line-duotone-documents_minimalistic';
    case LineduotoneClipboardCheck = 'solar-line-duotone-clipboard_check';
    case LineduotoneClipboardList = 'solar-line-duotone-clipboard_list';
    case LineduotoneDocuments = 'solar-line-duotone-Documents';
    case LineduotoneNotebook = 'solar-line-duotone-Notebook';
    case LineduotoneGalleryRound = 'solar-line-duotone-gallery_round';
    case LineduotonePlayCircle = 'solar-line-duotone-play_circle';
    case LineduotoneStream = 'solar-line-duotone-Stream';
    case LineduotoneGalleryRemove = 'solar-line-duotone-gallery_remove';
    case LineduotoneClapperboard = 'solar-line-duotone-Clapperboard';
    case LineduotonePauseCircle = 'solar-line-duotone-pause_circle';
    case LineduotoneRewind5SecondsBack = 'solar-line-duotone-rewind5_seconds_back';
    case LineduotoneRepeat = 'solar-line-duotone-Repeat';
    case LineduotoneClapperboardEdit = 'solar-line-duotone-clapperboard_edit';
    case LineduotoneVideoFrameCut = 'solar-line-duotone-video_frame_cut';
    case LineduotonePanorama = 'solar-line-duotone-Panorama';
    case LineduotonePlayStream = 'solar-line-duotone-play_stream';
    case LineduotoneClapperboardOpen = 'solar-line-duotone-clapperboard_open';
    case LineduotoneClapperboardText = 'solar-line-duotone-clapperboard_text';
    case LineduotoneLibrary = 'solar-line-duotone-Library';
    case LineduotoneReel2 = 'solar-line-duotone-reel2';
    case LineduotoneVolumeSmall = 'solar-line-duotone-volume_small';
    case LineduotoneVideoFrame = 'solar-line-duotone-video_frame';
    case LineduotoneMicrophoneLarge = 'solar-line-duotone-microphone_large';
    case LineduotoneRewindForward = 'solar-line-duotone-rewind_forward';
    case LineduotoneRewindBackCircle = 'solar-line-duotone-rewind_back_circle';
    case LineduotoneMicrophone = 'solar-line-duotone-Microphone';
    case LineduotoneVideoFrameReplace = 'solar-line-duotone-video_frame_replace';
    case LineduotoneClapperboardPlay = 'solar-line-duotone-clapperboard_play';
    case LineduotoneGalleryDownload = 'solar-line-duotone-gallery_download';
    case LineduotoneMusicNote4 = 'solar-line-duotone-music_note4';
    case LineduotoneVideocameraRecord = 'solar-line-duotone-videocamera_record';
    case LineduotonePlaybackSpeed = 'solar-line-duotone-playback_speed';
    case LineduotoneSoundwave = 'solar-line-duotone-Soundwave';
    case LineduotoneStopCircle = 'solar-line-duotone-stop_circle';
    case LineduotoneQuitFullScreenCircle = 'solar-line-duotone-quit_full_screen_circle';
    case LineduotoneRewindBack = 'solar-line-duotone-rewind_back';
    case LineduotoneRepeatOne = 'solar-line-duotone-repeat_one';
    case LineduotoneGalleryCheck = 'solar-line-duotone-gallery_check';
    case LineduotoneWallpaper = 'solar-line-duotone-Wallpaper';
    case LineduotoneRewindForwardCircle = 'solar-line-duotone-rewind_forward_circle';
    case LineduotoneGalleryEdit = 'solar-line-duotone-gallery_edit';
    case LineduotoneGallery = 'solar-line-duotone-Gallery';
    case LineduotoneGalleryMinimalistic = 'solar-line-duotone-gallery_minimalistic';
    case LineduotoneUploadTrack = 'solar-line-duotone-upload_track';
    case LineduotoneVolume = 'solar-line-duotone-Volume';
    case LineduotoneUploadTrack2 = 'solar-line-duotone-upload_track2';
    case LineduotoneMusicNotes = 'solar-line-duotone-music_notes';
    case LineduotoneMusicNote2 = 'solar-line-duotone-music_note2';
    case LineduotoneCameraAdd = 'solar-line-duotone-camera_add';
    case LineduotonePodcast = 'solar-line-duotone-Podcast';
    case LineduotoneCameraRotate = 'solar-line-duotone-camera_rotate';
    case LineduotoneMusicNote3 = 'solar-line-duotone-music_note3';
    case LineduotoneStop = 'solar-line-duotone-Stop';
    case LineduotoneMuted = 'solar-line-duotone-Muted';
    case LineduotoneSkipNext = 'solar-line-duotone-skip_next';
    case LineduotoneGallerySend = 'solar-line-duotone-gallery_send';
    case LineduotoneRecord = 'solar-line-duotone-Record';
    case LineduotoneFullScreenCircle = 'solar-line-duotone-full_screen_circle';
    case LineduotoneVolumeCross = 'solar-line-duotone-volume_cross';
    case LineduotoneSoundwaveCircle = 'solar-line-duotone-soundwave_circle';
    case LineduotoneSkipPrevious = 'solar-line-duotone-skip_previous';
    case LineduotoneRewind5SecondsForward = 'solar-line-duotone-rewind5_seconds_forward';
    case LineduotonePlay = 'solar-line-duotone-Play';
    case LineduotonePIP = 'solar-line-duotone-PIP';
    case LineduotoneMusicLibrary = 'solar-line-duotone-music_library';
    case LineduotoneVideoFrame2 = 'solar-line-duotone-video_frame2';
    case LineduotoneCamera = 'solar-line-duotone-Camera';
    case LineduotoneQuitPip = 'solar-line-duotone-quit_pip';
    case LineduotoneClapperboardOpenPlay = 'solar-line-duotone-clapperboard_open_play';
    case LineduotoneRewind10SecondsBack = 'solar-line-duotone-rewind10_seconds_back';
    case LineduotoneRepeatOneMinimalistic = 'solar-line-duotone-repeat_one_minimalistic';
    case LineduotoneVinyl = 'solar-line-duotone-Vinyl';
    case LineduotoneVideoLibrary = 'solar-line-duotone-video_library';
    case LineduotoneGalleryWide = 'solar-line-duotone-gallery_wide';
    case LineduotoneReel = 'solar-line-duotone-Reel';
    case LineduotoneToPip = 'solar-line-duotone-to_pip';
    case LineduotonePip2 = 'solar-line-duotone-pip2';
    case LineduotoneFullScreen = 'solar-line-duotone-full_screen';
    case LineduotoneCameraMinimalistic = 'solar-line-duotone-camera_minimalistic';
    case LineduotoneVideoFrameCut2 = 'solar-line-duotone-video_frame_cut2';
    case LineduotoneGalleryCircle = 'solar-line-duotone-gallery_circle';
    case LineduotoneVideoFramePlayHorizontal = 'solar-line-duotone-video_frame_play_horizontal';
    case LineduotoneMusicNoteSlider2 = 'solar-line-duotone-music_note_slider2';
    case LineduotoneMusicNoteSlider = 'solar-line-duotone-music_note_slider';
    case LineduotoneVideocameraAdd = 'solar-line-duotone-videocamera_add';
    case LineduotoneQuitFullScreenSquare = 'solar-line-duotone-quit_full_screen_square';
    case LineduotoneAlbum = 'solar-line-duotone-Album';
    case LineduotoneGalleryAdd = 'solar-line-duotone-gallery_add';
    case LineduotoneCameraSquare = 'solar-line-duotone-camera_square';
    case LineduotoneRewind15SecondsBack = 'solar-line-duotone-rewind15_seconds_back';
    case LineduotoneRewind15SecondsForward = 'solar-line-duotone-rewind15_seconds_forward';
    case LineduotoneVinylRecord = 'solar-line-duotone-vinyl_record';
    case LineduotoneShuffle = 'solar-line-duotone-Shuffle';
    case LineduotonePause = 'solar-line-duotone-Pause';
    case LineduotoneMusicNote = 'solar-line-duotone-music_note';
    case LineduotoneQuitFullScreen = 'solar-line-duotone-quit_full_screen';
    case LineduotoneMicrophone2 = 'solar-line-duotone-microphone2';
    case LineduotoneVideocamera = 'solar-line-duotone-Videocamera';
    case LineduotoneGalleryFavourite = 'solar-line-duotone-gallery_favourite';
    case LineduotoneMusicLibrary2 = 'solar-line-duotone-music_library2';
    case LineduotoneVideoFramePlayVertical = 'solar-line-duotone-video_frame_play_vertical';
    case LineduotoneFullScreenSquare = 'solar-line-duotone-full_screen_square';
    case LineduotoneRewind10SecondsForward = 'solar-line-duotone-rewind10_seconds_forward';
    case LineduotoneVolumeLoud = 'solar-line-duotone-volume_loud';
    case LineduotoneMicrophone3 = 'solar-line-duotone-microphone3';
    case LineduotoneSoundwaveSquare = 'solar-line-duotone-soundwave_square';
    case LineduotoneCardholder = 'solar-line-duotone-Cardholder';
    case LineduotoneBillList = 'solar-line-duotone-bill_list';
    case LineduotoneSaleSquare = 'solar-line-duotone-sale_square';
    case LineduotoneDollar = 'solar-line-duotone-Dollar';
    case LineduotoneTicket = 'solar-line-duotone-Ticket';
    case LineduotoneTag = 'solar-line-duotone-Tag';
    case LineduotoneCashOut = 'solar-line-duotone-cash_out';
    case LineduotoneWallet2 = 'solar-line-duotone-wallet2';
    case LineduotoneRuble = 'solar-line-duotone-Ruble';
    case LineduotoneCardTransfer = 'solar-line-duotone-card_transfer';
    case LineduotoneEuro = 'solar-line-duotone-Euro';
    case LineduotoneSale = 'solar-line-duotone-Sale';
    case LineduotoneCardSearch = 'solar-line-duotone-card_search';
    case LineduotoneWallet = 'solar-line-duotone-Wallet';
    case LineduotoneBillCross = 'solar-line-duotone-bill_cross';
    case LineduotoneTicketSale = 'solar-line-duotone-ticket_sale';
    case LineduotoneSafeSquare = 'solar-line-duotone-safe_square';
    case LineduotoneCard = 'solar-line-duotone-Card';
    case LineduotoneSafe2 = 'solar-line-duotone-safe2';
    case LineduotoneDollarMinimalistic = 'solar-line-duotone-dollar_minimalistic';
    case LineduotoneTagPrice = 'solar-line-duotone-tag_price';
    case LineduotoneMoneyBag = 'solar-line-duotone-money_bag';
    case LineduotoneBill = 'solar-line-duotone-Bill';
    case LineduotoneCardSend = 'solar-line-duotone-card_send';
    case LineduotoneCardRecive = 'solar-line-duotone-card_recive';
    case LineduotoneBanknote2 = 'solar-line-duotone-banknote2';
    case LineduotoneTagHorizontal = 'solar-line-duotone-tag_horizontal';
    case LineduotoneBillCheck = 'solar-line-duotone-bill_check';
    case LineduotoneTickerStar = 'solar-line-duotone-ticker_star';
    case LineduotoneBanknote = 'solar-line-duotone-Banknote';
    case LineduotoneVerifiedCheck = 'solar-line-duotone-verified_check';
    case LineduotoneWadOfMoney = 'solar-line-duotone-wad_of_money';
    case LineduotoneCard2 = 'solar-line-duotone-card2';
    case LineduotoneSafeCircle = 'solar-line-duotone-safe_circle';
    case LineduotoneWalletMoney = 'solar-line-duotone-wallet_money';
    case LineduotoneList = 'solar-line-duotone-List';
    case LineduotoneListDownMinimalistic = 'solar-line-duotone-list_down_minimalistic';
    case LineduotonePlaylist2 = 'solar-line-duotone-playlist2';
    case LineduotoneChecklistMinimalistic = 'solar-line-duotone-checklist_minimalistic';
    case LineduotonePlaaylistMinimalistic = 'solar-line-duotone-plaaylist_minimalistic';
    case LineduotoneListHeart = 'solar-line-duotone-list_heart';
    case LineduotoneListArrowDown = 'solar-line-duotone-list_arrow_down';
    case LineduotoneListArrowUp = 'solar-line-duotone-list_arrow_up';
    case LineduotoneListUpMinimalistic = 'solar-line-duotone-list_up_minimalistic';
    case LineduotonePlaylist = 'solar-line-duotone-Playlist';
    case LineduotoneListUp = 'solar-line-duotone-list_up';
    case LineduotoneListCrossMinimalistic = 'solar-line-duotone-list_cross_minimalistic';
    case LineduotoneListCross = 'solar-line-duotone-list_cross';
    case LineduotoneListArrowDownMinimalistic = 'solar-line-duotone-list_arrow_down_minimalistic';
    case LineduotoneSortByAlphabet = 'solar-line-duotone-sort_by_alphabet';
    case LineduotoneChecklist = 'solar-line-duotone-Checklist';
    case LineduotoneSortFromBottomToTop = 'solar-line-duotone-sort_from_bottom_to_top';
    case LineduotoneListCheck = 'solar-line-duotone-list_check';
    case LineduotonePlaylistMinimalistic2 = 'solar-line-duotone-playlist_minimalistic2';
    case LineduotonePlaylistMinimalistic3 = 'solar-line-duotone-playlist_minimalistic3';
    case LineduotoneList1 = 'solar-line-duotone-list1';
    case LineduotoneSortFromTopToBottom = 'solar-line-duotone-sort_from_top_to_bottom';
    case LineduotoneSortByTime = 'solar-line-duotone-sort_by_time';
    case LineduotoneListDown = 'solar-line-duotone-list_down';
    case LineduotoneListHeartMinimalistic = 'solar-line-duotone-list_heart_minimalistic';
    case LineduotoneListCheckMinimalistic = 'solar-line-duotone-list_check_minimalistic';
    case LineduotoneListArrowUpMinimalistic = 'solar-line-duotone-list_arrow_up_minimalistic';
    case LineduotoneUserCrossRounded = 'solar-line-duotone-user_cross_rounded';
    case LineduotoneUser = 'solar-line-duotone-User';
    case LineduotoneUsersGroupRounded = 'solar-line-duotone-users_group_rounded';
    case LineduotoneUserPlusRounded = 'solar-line-duotone-user_plus_rounded';
    case LineduotoneUserBlock = 'solar-line-duotone-user_block';
    case LineduotoneUserMinus = 'solar-line-duotone-user_minus';
    case LineduotoneUserHands = 'solar-line-duotone-user_hands';
    case LineduotoneUserHeart = 'solar-line-duotone-user_heart';
    case LineduotoneUserMinusRounded = 'solar-line-duotone-user_minus_rounded';
    case LineduotoneUserCross = 'solar-line-duotone-user_cross';
    case LineduotoneUserSpeakRounded = 'solar-line-duotone-user_speak_rounded';
    case LineduotoneUserId = 'solar-line-duotone-user_id';
    case LineduotoneUserBlockRounded = 'solar-line-duotone-user_block_rounded';
    case LineduotoneUserHeartRounded = 'solar-line-duotone-user_heart_rounded';
    case LineduotoneUsersGroupTwoRounded = 'solar-line-duotone-users_group_two_rounded';
    case LineduotoneUserHandUp = 'solar-line-duotone-user_hand_up';
    case LineduotoneUserCircle = 'solar-line-duotone-user_circle';
    case LineduotoneUserRounded = 'solar-line-duotone-user_rounded';
    case LineduotoneUserCheck = 'solar-line-duotone-user_check';
    case LineduotoneUserPlus = 'solar-line-duotone-user_plus';
    case LineduotoneUserCheckRounded = 'solar-line-duotone-user_check_rounded';
    case LineduotoneUserSpeak = 'solar-line-duotone-user_speak';
    case LineduotoneVirus = 'solar-line-duotone-Virus';
    case LineduotoneAdhesivePlaster2 = 'solar-line-duotone-adhesive_plaster2';
    case LineduotoneDropper = 'solar-line-duotone-Dropper';
    case LineduotonePulse2 = 'solar-line-duotone-pulse2';
    case LineduotoneBoneBroken = 'solar-line-duotone-bone_broken';
    case LineduotoneHeartPulse2 = 'solar-line-duotone-heart_pulse2';
    case LineduotoneMedicalKit = 'solar-line-duotone-medical_kit';
    case LineduotoneTestTube = 'solar-line-duotone-test_tube';
    case LineduotoneHealth = 'solar-line-duotone-Health';
    case LineduotoneDropperMinimalistic2 = 'solar-line-duotone-dropper_minimalistic2';
    case LineduotoneDNA = 'solar-line-duotone-DNA';
    case LineduotoneDropper3 = 'solar-line-duotone-dropper3';
    case LineduotoneThermometer = 'solar-line-duotone-Thermometer';
    case LineduotoneDropper2 = 'solar-line-duotone-dropper2';
    case LineduotoneJarOfPills2 = 'solar-line-duotone-jar_of_pills2';
    case LineduotoneBoneCrack = 'solar-line-duotone-bone_crack';
    case LineduotoneJarOfPills = 'solar-line-duotone-jar_of_pills';
    case LineduotoneSyringe = 'solar-line-duotone-Syringe';
    case LineduotoneStethoscope = 'solar-line-duotone-Stethoscope';
    case LineduotoneBenzeneRing = 'solar-line-duotone-benzene_ring';
    case LineduotoneBacteria = 'solar-line-duotone-Bacteria';
    case LineduotoneAdhesivePlaster = 'solar-line-duotone-adhesive_plaster';
    case LineduotoneBone = 'solar-line-duotone-Bone';
    case LineduotoneBones = 'solar-line-duotone-Bones';
    case LineduotonePill = 'solar-line-duotone-Pill';
    case LineduotonePills = 'solar-line-duotone-Pills';
    case LineduotoneHeartPulse = 'solar-line-duotone-heart_pulse';
    case LineduotoneTestTubeMinimalistic = 'solar-line-duotone-test_tube_minimalistic';
    case LineduotonePills2 = 'solar-line-duotone-pills2';
    case LineduotonePulse = 'solar-line-duotone-Pulse';
    case LineduotoneDropperMinimalistic = 'solar-line-duotone-dropper_minimalistic';
    case LineduotonePills3 = 'solar-line-duotone-pills3';
    case LineduotoneWhisk = 'solar-line-duotone-Whisk';
    case LineduotoneBottle = 'solar-line-duotone-Bottle';
    case LineduotoneOvenMittsMinimalistic = 'solar-line-duotone-oven_mitts_minimalistic';
    case LineduotoneChefHatMinimalistic = 'solar-line-duotone-chef_hat_minimalistic';
    case LineduotoneTeaCup = 'solar-line-duotone-tea_cup';
    case LineduotoneWineglassTriangle = 'solar-line-duotone-wineglass_triangle';
    case LineduotoneOvenMitts = 'solar-line-duotone-oven_mitts';
    case LineduotoneCupPaper = 'solar-line-duotone-cup_paper';
    case LineduotoneLadle = 'solar-line-duotone-Ladle';
    case LineduotoneCorkscrew = 'solar-line-duotone-Corkscrew';
    case LineduotoneDonutBitten = 'solar-line-duotone-donut_bitten';
    case LineduotoneWineglass = 'solar-line-duotone-Wineglass';
    case LineduotoneDonut = 'solar-line-duotone-Donut';
    case LineduotoneCupHot = 'solar-line-duotone-cup_hot';
    case LineduotoneChefHatHeart = 'solar-line-duotone-chef_hat_heart';
    case LineduotoneChefHat = 'solar-line-duotone-chef_hat';
    case LineduotoneRollingPin = 'solar-line-duotone-rolling_pin';
    case LineduotoneCodeFile = 'solar-line-duotone-code_file';
    case LineduotoneFileCorrupted = 'solar-line-duotone-file_corrupted';
    case LineduotoneFile = 'solar-line-duotone-File';
    case LineduotoneFileRight = 'solar-line-duotone-file_right';
    case LineduotoneFileFavourite = 'solar-line-duotone-file_favourite';
    case LineduotoneFileDownload = 'solar-line-duotone-file_download';
    case LineduotoneZipFile = 'solar-line-duotone-zip_file';
    case LineduotoneFileText = 'solar-line-duotone-file_text';
    case LineduotoneFileSmile = 'solar-line-duotone-file_smile_)';
    case LineduotoneFileCheck = 'solar-line-duotone-file_check';
    case LineduotoneFileSend = 'solar-line-duotone-file_send';
    case LineduotoneFileLeft = 'solar-line-duotone-file_left';
    case LineduotoneFigmaFile = 'solar-line-duotone-figma_file';
    case LineduotoneFileRemove = 'solar-line-duotone-file_remove';
    case LineduotoneCloudFile = 'solar-line-duotone-cloud_file';
    case LineduotoneRemoveFolder = 'solar-line-duotone-remove_folder';
    case LineduotoneFolderFavouritestar = 'solar-line-duotone-folder_favourite(star)';
    case LineduotoneAddFolder = 'solar-line-duotone-add_folder';
    case LineduotoneFolderCheck = 'solar-line-duotone-folder_check';
    case LineduotoneFolderFavouritebookmark = 'solar-line-duotone-folder_favourite(bookmark)';
    case LineduotoneFolder2 = 'solar-line-duotone-folder2';
    case LineduotoneFolderSecurity = 'solar-line-duotone-folder_security';
    case LineduotoneFolderCloud = 'solar-line-duotone-folder_cloud';
    case LineduotoneMoveToFolder = 'solar-line-duotone-move_to_folder';
    case LineduotoneFolderError = 'solar-line-duotone-folder_error';
    case LineduotoneFolderPathConnect = 'solar-line-duotone-folder_path_connect';
    case LineduotoneFolderOpen = 'solar-line-duotone-folder_open';
    case LineduotoneFolder = 'solar-line-duotone-Folder';
    case LineduotoneFolderWithFiles = 'solar-line-duotone-folder_with_files';
    case LineduotoneCloudCheck = 'solar-line-duotone-cloud_check';
    case LineduotoneTemperature = 'solar-line-duotone-Temperature';
    case LineduotoneWind = 'solar-line-duotone-Wind';
    case LineduotoneCloudSnowfall = 'solar-line-duotone-cloud_snowfall';
    case LineduotoneSunrise = 'solar-line-duotone-Sunrise';
    case LineduotoneSun2 = 'solar-line-duotone-sun2';
    case LineduotoneCloudSun = 'solar-line-duotone-cloud_sun';
    case LineduotoneCloudBoltMinimalistic = 'solar-line-duotone-cloud_bolt_minimalistic';
    case LineduotoneCloudDownload = 'solar-line-duotone-cloud_download';
    case LineduotoneClouds = 'solar-line-duotone-Clouds';
    case LineduotoneTornado = 'solar-line-duotone-Tornado';
    case LineduotoneMoonSleep = 'solar-line-duotone-moon_sleep';
    case LineduotoneCloudUpload = 'solar-line-duotone-cloud_upload';
    case LineduotoneCloudRain = 'solar-line-duotone-cloud_rain';
    case LineduotoneFog = 'solar-line-duotone-Fog';
    case LineduotoneSnowflake = 'solar-line-duotone-Snowflake';
    case LineduotoneMoonFog = 'solar-line-duotone-moon_fog';
    case LineduotoneCloudMinus = 'solar-line-duotone-cloud_minus';
    case LineduotoneCloudBolt = 'solar-line-duotone-cloud_bolt';
    case LineduotoneCloudWaterdrop = 'solar-line-duotone-cloud_waterdrop';
    case LineduotoneSunset = 'solar-line-duotone-Sunset';
    case LineduotoneWaterdrops = 'solar-line-duotone-Waterdrops';
    case LineduotoneMoonStars = 'solar-line-duotone-moon_stars';
    case LineduotoneCloudPlus = 'solar-line-duotone-cloud_plus';
    case LineduotoneSun = 'solar-line-duotone-Sun';
    case LineduotoneCloudWaterdrops = 'solar-line-duotone-cloud_waterdrops';
    case LineduotoneCloudSun2 = 'solar-line-duotone-cloud_sun2';
    case LineduotoneCloudyMoon = 'solar-line-duotone-cloudy_moon';
    case LineduotoneTornadoSmall = 'solar-line-duotone-tornado_small';
    case LineduotoneCloud = 'solar-line-duotone-Cloud';
    case LineduotoneSunFog = 'solar-line-duotone-sun_fog';
    case LineduotoneCloundCross = 'solar-line-duotone-clound_cross';
    case LineduotoneCloudSnowfallMinimalistic = 'solar-line-duotone-cloud_snowfall_minimalistic';
    case LineduotoneCloudStorm = 'solar-line-duotone-cloud_storm';
    case LineduotoneMoon = 'solar-line-duotone-Moon';
    case LineduotoneRefreshCircle = 'solar-line-duotone-refresh_circle';
    case LineduotoneSquareArrowRightDown = 'solar-line-duotone-square_arrow_right_down';
    case LineduotoneRoundArrowLeftDown = 'solar-line-duotone-round_arrow_left_down';
    case LineduotoneRestart = 'solar-line-duotone-Restart';
    case LineduotoneRoundAltArrowDown = 'solar-line-duotone-round_alt_arrow_down';
    case LineduotoneRoundSortVertical = 'solar-line-duotone-round_sort_vertical';
    case LineduotoneSquareAltArrowUp = 'solar-line-duotone-square_alt_arrow_up';
    case LineduotoneArrowLeftUp = 'solar-line-duotone-arrow_left_up';
    case LineduotoneSortHorizontal = 'solar-line-duotone-sort_horizontal';
    case LineduotoneTransferHorizontal = 'solar-line-duotone-transfer_horizontal';
    case LineduotoneSquareDoubleAltArrowUp = 'solar-line-duotone-square_double_alt_arrow_up';
    case LineduotoneRoundArrowLeftUp = 'solar-line-duotone-round_arrow_left_up';
    case LineduotoneAltArrowRight = 'solar-line-duotone-alt_arrow_right';
    case LineduotoneRoundDoubleAltArrowUp = 'solar-line-duotone-round_double_alt_arrow_up';
    case LineduotoneRestartCircle = 'solar-line-duotone-restart_circle';
    case LineduotoneSquareArrowDown = 'solar-line-duotone-square_arrow_down';
    case LineduotoneSortVertical = 'solar-line-duotone-sort_vertical';
    case LineduotoneSquareSortHorizontal = 'solar-line-duotone-square_sort_horizontal';
    case LineduotoneDoubleAltArrowLeft = 'solar-line-duotone-double_alt_arrow_left';
    case LineduotoneSquareAltArrowDown = 'solar-line-duotone-square_alt_arrow_down';
    case LineduotoneSquareAltArrowRight = 'solar-line-duotone-square_alt_arrow_right';
    case LineduotoneSquareArrowUp = 'solar-line-duotone-square_arrow_up';
    case LineduotoneDoubleAltArrowRight = 'solar-line-duotone-double_alt_arrow_right';
    case LineduotoneRoundTransferVertical = 'solar-line-duotone-round_transfer_vertical';
    case LineduotoneArrowLeft = 'solar-line-duotone-arrow_left';
    case LineduotoneRoundDoubleAltArrowRight = 'solar-line-duotone-round_double_alt_arrow_right';
    case LineduotoneSquareDoubleAltArrowLeft = 'solar-line-duotone-square_double_alt_arrow_left';
    case LineduotoneAltArrowDown = 'solar-line-duotone-alt_arrow_down';
    case LineduotoneRoundTransferHorizontal = 'solar-line-duotone-round_transfer_horizontal';
    case LineduotoneRoundArrowRightDown = 'solar-line-duotone-round_arrow_right_down';
    case LineduotoneArrowUp = 'solar-line-duotone-arrow_up';
    case LineduotoneRoundArrowLeft = 'solar-line-duotone-round_arrow_left';
    case LineduotoneDoubleAltArrowUp = 'solar-line-duotone-double_alt_arrow_up';
    case LineduotoneRoundArrowRight = 'solar-line-duotone-round_arrow_right';
    case LineduotoneSquareTransferHorizontal = 'solar-line-duotone-square_transfer_horizontal';
    case LineduotoneArrowRight = 'solar-line-duotone-arrow_right';
    case LineduotoneRoundDoubleAltArrowLeft = 'solar-line-duotone-round_double_alt_arrow_left';
    case LineduotoneRoundArrowUp = 'solar-line-duotone-round_arrow_up';
    case LineduotoneSquareSortVertical = 'solar-line-duotone-square_sort_vertical';
    case LineduotoneAltArrowLeft = 'solar-line-duotone-alt_arrow_left';
    case LineduotoneSquareDoubleAltArrowRight = 'solar-line-duotone-square_double_alt_arrow_right';
    case LineduotoneRefresh = 'solar-line-duotone-Refresh';
    case LineduotoneTransferVertical = 'solar-line-duotone-transfer_vertical';
    case LineduotoneRefreshSquare = 'solar-line-duotone-refresh_square';
    case LineduotoneSquareTransferVertical = 'solar-line-duotone-square_transfer_vertical';
    case LineduotoneSquareDoubleAltArrowDown = 'solar-line-duotone-square_double_alt_arrow_down';
    case LineduotoneRoundArrowRightUp = 'solar-line-duotone-round_arrow_right_up';
    case LineduotoneArrowDown = 'solar-line-duotone-arrow_down';
    case LineduotoneRestartSquare = 'solar-line-duotone-restart_square';
    case LineduotoneSquareArrowRight = 'solar-line-duotone-square_arrow_right';
    case LineduotoneRoundDoubleAltArrowDown = 'solar-line-duotone-round_double_alt_arrow_down';
    case LineduotoneSquareArrowLeftUp = 'solar-line-duotone-square_arrow_left_up';
    case LineduotoneRoundArrowDown = 'solar-line-duotone-round_arrow_down';
    case LineduotoneSquareArrowRightUp = 'solar-line-duotone-square_arrow_right_up';
    case LineduotoneRoundTransferDiagonal = 'solar-line-duotone-round_transfer_diagonal';
    case LineduotoneArrowRightDown = 'solar-line-duotone-arrow_right_down';
    case LineduotoneArrowLeftDown = 'solar-line-duotone-arrow_left_down';
    case LineduotoneRoundAltArrowLeft = 'solar-line-duotone-round_alt_arrow_left';
    case LineduotoneArrowRightUp = 'solar-line-duotone-arrow_right_up';
    case LineduotoneSquareArrowLeftDown = 'solar-line-duotone-square_arrow_left_down';
    case LineduotoneRoundAltArrowUp = 'solar-line-duotone-round_alt_arrow_up';
    case LineduotoneAltArrowUp = 'solar-line-duotone-alt_arrow_up';
    case LineduotoneSquareAltArrowLeft = 'solar-line-duotone-square_alt_arrow_left';
    case LineduotoneRoundSortHorizontal = 'solar-line-duotone-round_sort_horizontal';
    case LineduotoneDoubleAltArrowDown = 'solar-line-duotone-double_alt_arrow_down';
    case LineduotoneRoundAltArrowRight = 'solar-line-duotone-round_alt_arrow_right';
    case LineduotoneSquareArrowLeft = 'solar-line-duotone-square_arrow_left';
    case LineduotoneTuningSquare2 = 'solar-line-duotone-tuning_square2';
    case LineduotoneWidgetAdd = 'solar-line-duotone-widget_add';
    case LineduotoneTuningSquare = 'solar-line-duotone-tuning_square';
    case LineduotoneSettingsMinimalistic = 'solar-line-duotone-settings_minimalistic';
    case LineduotoneWidget6 = 'solar-line-duotone-widget6';
    case LineduotoneWidget4 = 'solar-line-duotone-widget4';
    case LineduotoneSettings = 'solar-line-duotone-Settings';
    case LineduotoneWidget5 = 'solar-line-duotone-widget5';
    case LineduotoneWidget2 = 'solar-line-duotone-widget2';
    case LineduotoneWidget3 = 'solar-line-duotone-widget3';
    case LineduotoneTuning2 = 'solar-line-duotone-tuning2';
    case LineduotoneTuning3 = 'solar-line-duotone-tuning3';
    case LineduotoneWidget = 'solar-line-duotone-Widget';
    case LineduotoneTuning4 = 'solar-line-duotone-tuning4';
    case LineduotoneTuning = 'solar-line-duotone-Tuning';
    case LineduotoneDiagramDown = 'solar-line-duotone-diagram_down';
    case LineduotoneChart2 = 'solar-line-duotone-chart2';
    case LineduotoneChart = 'solar-line-duotone-Chart';
    case LineduotoneDiagramUp = 'solar-line-duotone-diagram_up';
    case LineduotoneGraphNew = 'solar-line-duotone-graph_new';
    case LineduotoneCourseUp = 'solar-line-duotone-course_up';
    case LineduotoneGraphDownNew = 'solar-line-duotone-graph_down_new';
    case LineduotonePieChart3 = 'solar-line-duotone-pie_chart3';
    case LineduotonePieChart2 = 'solar-line-duotone-pie_chart2';
    case LineduotoneGraphNewUp = 'solar-line-duotone-graph_new_up';
    case LineduotonePieChart = 'solar-line-duotone-pie_chart';
    case LineduotoneRoundGraph = 'solar-line-duotone-round_graph';
    case LineduotoneGraphUp = 'solar-line-duotone-graph_up';
    case LineduotoneChartSquare = 'solar-line-duotone-chart_square';
    case LineduotoneCourseDown = 'solar-line-duotone-course_down';
    case LineduotoneChatSquare2 = 'solar-line-duotone-chat_square2';
    case LineduotoneGraphDown = 'solar-line-duotone-graph_down';
    case LineduotoneGraph = 'solar-line-duotone-Graph';
    case LineduotonePresentationGraph = 'solar-line-duotone-presentation_graph';
    case LineduotoneMaximizeSquare3 = 'solar-line-duotone-maximize_square3';
    case LineduotoneMaximizeSquareMinimalistic = 'solar-line-duotone-maximize_square_minimalistic';
    case LineduotoneMaximizeSquare2 = 'solar-line-duotone-maximize_square2';
    case LineduotoneMinimizeSquare = 'solar-line-duotone-minimize_square';
    case LineduotoneDownloadSquare = 'solar-line-duotone-download_square';
    case LineduotoneUndoLeftRoundSquare = 'solar-line-duotone-undo_left_round_square';
    case LineduotoneReply = 'solar-line-duotone-Reply';
    case LineduotoneLogout = 'solar-line-duotone-Logout';
    case LineduotoneReciveSquare = 'solar-line-duotone-recive_square';
    case LineduotoneExport = 'solar-line-duotone-Export';
    case LineduotoneSendTwiceSquare = 'solar-line-duotone-send_twice_square';
    case LineduotoneUndoLeftRound = 'solar-line-duotone-undo_left_round';
    case LineduotoneForward2 = 'solar-line-duotone-forward2';
    case LineduotoneMaximize = 'solar-line-duotone-Maximize';
    case LineduotoneUndoRightRound = 'solar-line-duotone-undo_right_round';
    case LineduotoneMinimizeSquare2 = 'solar-line-duotone-minimize_square2';
    case LineduotoneMinimizeSquare3 = 'solar-line-duotone-minimize_square3';
    case LineduotoneUploadTwiceSquare = 'solar-line-duotone-upload_twice_square';
    case LineduotoneMinimize = 'solar-line-duotone-Minimize';
    case LineduotoneCircleTopUp = 'solar-line-duotone-circle_top_up';
    case LineduotoneUploadMinimalistic = 'solar-line-duotone-upload_minimalistic';
    case LineduotoneDownload = 'solar-line-duotone-Download';
    case LineduotoneImport = 'solar-line-duotone-Import';
    case LineduotoneLogin = 'solar-line-duotone-Login';
    case LineduotoneUndoLeft = 'solar-line-duotone-undo_left';
    case LineduotoneSquareTopUp = 'solar-line-duotone-square_top_up';
    case LineduotoneDownloadTwiceSquare = 'solar-line-duotone-download_twice_square';
    case LineduotoneCircleBottomDown = 'solar-line-duotone-circle_bottom_down';
    case LineduotoneMaximizeSquare = 'solar-line-duotone-maximize_square';
    case LineduotoneUploadSquare = 'solar-line-duotone-upload_square';
    case LineduotoneUndoRightSquare = 'solar-line-duotone-undo_right_square';
    case LineduotoneReciveTwiceSquare = 'solar-line-duotone-recive_twice_square';
    case LineduotoneCircleTopDown = 'solar-line-duotone-circle_top_down';
    case LineduotoneArrowToDownLeft = 'solar-line-duotone-arrow_to_down_left';
    case LineduotoneLogout2 = 'solar-line-duotone-logout2';
    case LineduotoneLogout3 = 'solar-line-duotone-logout3';
    case LineduotoneScale = 'solar-line-duotone-Scale';
    case LineduotoneArrowToDownRight = 'solar-line-duotone-arrow_to_down_right';
    case LineduotoneDownloadMinimalistic = 'solar-line-duotone-download_minimalistic';
    case LineduotoneMinimizeSquareMinimalistic = 'solar-line-duotone-minimize_square_minimalistic';
    case LineduotoneReply2 = 'solar-line-duotone-reply2';
    case LineduotoneSquareBottomUp = 'solar-line-duotone-square_bottom_up';
    case LineduotoneUndoRight = 'solar-line-duotone-undo_right';
    case LineduotoneUndoLeftSquare = 'solar-line-duotone-undo_left_square';
    case LineduotoneSendSquare = 'solar-line-duotone-send_square';
    case LineduotoneExit = 'solar-line-duotone-Exit';
    case LineduotoneSquareBottomDown = 'solar-line-duotone-square_bottom_down';
    case LineduotoneUndoRightRoundSquare = 'solar-line-duotone-undo_right_round_square';
    case LineduotoneArrowToTopLeft = 'solar-line-duotone-arrow_to_top_left';
    case LineduotoneCircleBottomUp = 'solar-line-duotone-circle_bottom_up';
    case LineduotoneScreenShare = 'solar-line-duotone-screen_share';
    case LineduotoneUpload = 'solar-line-duotone-Upload';
    case LineduotoneSquareTopDown = 'solar-line-duotone-square_top_down';
    case LineduotoneArrowToTopRight = 'solar-line-duotone-arrow_to_top_right';
    case LineduotoneLogin3 = 'solar-line-duotone-login3';
    case LineduotoneLogin2 = 'solar-line-duotone-login2';
    case LineduotonePassport = 'solar-line-duotone-Passport';
    case LineduotoneDiplomaVerified = 'solar-line-duotone-diploma_verified';
    case LineduotoneCaseRound = 'solar-line-duotone-case_round';
    case LineduotoneBackpack = 'solar-line-duotone-Backpack';
    case LineduotoneBook2 = 'solar-line-duotone-book2';
    case LineduotoneSquareAcademicCap2 = 'solar-line-duotone-square_academic_cap2';
    case LineduotoneCaseRoundMinimalistic = 'solar-line-duotone-case_round_minimalistic';
    case LineduotoneCase = 'solar-line-duotone-Case';
    case LineduotoneBookBookmarkMinimalistic = 'solar-line-duotone-book_bookmark_minimalistic';
    case LineduotoneBookmarkOpened = 'solar-line-duotone-bookmark_opened';
    case LineduotoneDiploma = 'solar-line-duotone-Diploma';
    case LineduotoneBook = 'solar-line-duotone-Book';
    case LineduotoneSquareAcademicCap = 'solar-line-duotone-square_academic_cap';
    case LineduotoneBookmarkCircle = 'solar-line-duotone-bookmark_circle';
    case LineduotoneCalculatorMinimalistic = 'solar-line-duotone-calculator_minimalistic';
    case LineduotoneNotebookSquare = 'solar-line-duotone-notebook_square';
    case LineduotoneBookMinimalistic = 'solar-line-duotone-book_minimalistic';
    case LineduotoneCaseMinimalistic = 'solar-line-duotone-case_minimalistic';
    case LineduotoneNotebookBookmark = 'solar-line-duotone-notebook_bookmark';
    case LineduotonePassportMinimalistic = 'solar-line-duotone-passport_minimalistic';
    case LineduotoneBookBookmark = 'solar-line-duotone-book_bookmark';
    case LineduotoneBookmarkSquareMinimalistic = 'solar-line-duotone-bookmark_square_minimalistic';
    case LineduotoneBookmark = 'solar-line-duotone-Bookmark';
    case LineduotonePlusMinus = 'solar-line-duotone-plus,_minus';
    case LineduotoneCalculator = 'solar-line-duotone-Calculator';
    case LineduotoneBookmarkSquare = 'solar-line-duotone-bookmark_square';
    case LineduotoneNotebookMinimalistic = 'solar-line-duotone-notebook_minimalistic';
    case LineduotoneFireSquare = 'solar-line-duotone-fire_square';
    case LineduotoneSuitcaseLines = 'solar-line-duotone-suitcase_lines';
    case LineduotoneFire = 'solar-line-duotone-Fire';
    case LineduotoneBonfire = 'solar-line-duotone-Bonfire';
    case LineduotoneSuitcaseTag = 'solar-line-duotone-suitcase_tag';
    case LineduotoneLeaf = 'solar-line-duotone-Leaf';
    case LineduotoneSuitcase = 'solar-line-duotone-Suitcase';
    case LineduotoneFlame = 'solar-line-duotone-Flame';
    case LineduotoneFireMinimalistic = 'solar-line-duotone-fire_minimalistic';
    case LineduotoneBellBing = 'solar-line-duotone-bell_bing';
    case LineduotoneNotificationLinesRemove = 'solar-line-duotone-notification_lines_remove';
    case LineduotoneNotificationUnread = 'solar-line-duotone-notification_unread';
    case LineduotoneBell = 'solar-line-duotone-Bell';
    case LineduotoneNotificationRemove = 'solar-line-duotone-notification_remove';
    case LineduotoneNotificationUnreadLines = 'solar-line-duotone-notification_unread_lines';
    case LineduotoneBellOff = 'solar-line-duotone-bell_off';
    case LineduotoneLightning = 'solar-line-duotone-Lightning';
    case LineduotoneLightbulbMinimalistic = 'solar-line-duotone-lightbulb_minimalistic';
    case LineduotoneServerSquareCloud = 'solar-line-duotone-server_square_cloud';
    case LineduotoneLightbulbBolt = 'solar-line-duotone-lightbulb_bolt';
    case LineduotoneAirbudsCharge = 'solar-line-duotone-airbuds_charge';
    case LineduotoneServerPath = 'solar-line-duotone-server_path';
    case LineduotoneSimCardMinimalistic = 'solar-line-duotone-sim_card_minimalistic';
    case LineduotoneSmartphone = 'solar-line-duotone-Smartphone';
    case LineduotoneTurntable = 'solar-line-duotone-Turntable';
    case LineduotoneAirbudsCheck = 'solar-line-duotone-airbuds_check';
    case LineduotoneMouseMinimalistic = 'solar-line-duotone-mouse_minimalistic';
    case LineduotoneSmartphoneRotateAngle = 'solar-line-duotone-smartphone_rotate_angle';
    case LineduotoneRadioMinimalistic = 'solar-line-duotone-radio_minimalistic';
    case LineduotoneAirbuds = 'solar-line-duotone-Airbuds';
    case LineduotoneSmartphoneRotateOrientation = 'solar-line-duotone-smartphone_rotate_orientation';
    case LineduotoneIPhone = 'solar-line-duotone-i_phone';
    case LineduotoneSimCard = 'solar-line-duotone-sim_card';
    case LineduotoneFlashDrive = 'solar-line-duotone-flash_drive';
    case LineduotoneDevices = 'solar-line-duotone-Devices';
    case LineduotoneSimCards = 'solar-line-duotone-sim_cards';
    case LineduotoneAirbudsCaseOpen = 'solar-line-duotone-airbuds_case_open';
    case LineduotoneTurntableMusicNote = 'solar-line-duotone-turntable_music_note';
    case LineduotoneKeyboard = 'solar-line-duotone-Keyboard';
    case LineduotoneGamepadCharge = 'solar-line-duotone-gamepad_charge';
    case LineduotoneBoombox = 'solar-line-duotone-Boombox';
    case LineduotoneSmartSpeakerMinimalistic = 'solar-line-duotone-smart_speaker_minimalistic';
    case LineduotoneTelescope = 'solar-line-duotone-Telescope';
    case LineduotoneMonitorCamera = 'solar-line-duotone-monitor_camera';
    case LineduotoneLaptopMinimalistic = 'solar-line-duotone-laptop_minimalistic';
    case LineduotoneServer2 = 'solar-line-duotone-server2';
    case LineduotoneSmartSpeaker = 'solar-line-duotone-smart_speaker';
    case LineduotoneProjector = 'solar-line-duotone-Projector';
    case LineduotoneServer = 'solar-line-duotone-Server';
    case LineduotoneTV = 'solar-line-duotone-TV';
    case LineduotoneCassette2 = 'solar-line-duotone-cassette2';
    case LineduotoneRadio = 'solar-line-duotone-Radio';
    case LineduotoneSmartphoneVibration = 'solar-line-duotone-smartphone_vibration';
    case LineduotoneAirbudsLeft = 'solar-line-duotone-airbuds_left';
    case LineduotoneHeadphonesRound = 'solar-line-duotone-headphones_round';
    case LineduotoneGameboy = 'solar-line-duotone-Gameboy';
    case LineduotoneHeadphonesRoundSound = 'solar-line-duotone-headphones_round_sound';
    case LineduotoneCPU = 'solar-line-duotone-CPU';
    case LineduotonePrinter2 = 'solar-line-duotone-printer2';
    case LineduotoneHeadphonesSquare = 'solar-line-duotone-headphones_square';
    case LineduotoneServerSquareUpdate = 'solar-line-duotone-server_square_update';
    case LineduotonePrinterMinimalistic = 'solar-line-duotone-printer_minimalistic';
    case LineduotoneBluetooth = 'solar-line-duotone-Bluetooth';
    case LineduotoneWirelessCharge = 'solar-line-duotone-wireless_charge';
    case LineduotoneBluetoothCircle = 'solar-line-duotone-bluetooth_circle';
    case LineduotoneAirbudsCaseMinimalistic = 'solar-line-duotone-airbuds_case_minimalistic';
    case LineduotoneLightbulb = 'solar-line-duotone-Lightbulb';
    case LineduotoneAirbudsRemove = 'solar-line-duotone-airbuds_remove';
    case LineduotoneSmartphoneRotate2 = 'solar-line-duotone-smartphone_rotate2';
    case LineduotoneSsdSquare = 'solar-line-duotone-ssd_square';
    case LineduotonePrinter = 'solar-line-duotone-Printer';
    case LineduotoneSmartphone2 = 'solar-line-duotone-smartphone2';
    case LineduotoneServerMinimalistic = 'solar-line-duotone-server_minimalistic';
    case LineduotoneHeadphonesSquareSound = 'solar-line-duotone-headphones_square_sound';
    case LineduotoneDiskette = 'solar-line-duotone-Diskette';
    case LineduotoneBluetoothWave = 'solar-line-duotone-bluetooth_wave';
    case LineduotoneSmartSpeaker2 = 'solar-line-duotone-smart_speaker2';
    case LineduotoneLaptop3 = 'solar-line-duotone-laptop3';
    case LineduotoneLaptop2 = 'solar-line-duotone-laptop2';
    case LineduotoneMouseCircle = 'solar-line-duotone-mouse_circle';
    case LineduotoneTurntableMinimalistic = 'solar-line-duotone-turntable_minimalistic';
    case LineduotoneSmartphoneUpdate = 'solar-line-duotone-smartphone_update';
    case LineduotoneGamepadMinimalistic = 'solar-line-duotone-gamepad_minimalistic';
    case LineduotoneSdCard = 'solar-line-duotone-sd_card';
    case LineduotonePlugCircle = 'solar-line-duotone-plug_circle';
    case LineduotoneAirbudsCase = 'solar-line-duotone-airbuds_case';
    case LineduotoneSsdRound = 'solar-line-duotone-ssd_round';
    case LineduotoneLaptop = 'solar-line-duotone-Laptop';
    case LineduotoneAirbudsRight = 'solar-line-duotone-airbuds_right';
    case LineduotoneDisplay = 'solar-line-duotone-Display';
    case LineduotoneMonitorSmartphone = 'solar-line-duotone-monitor_smartphone';
    case LineduotoneSocket = 'solar-line-duotone-Socket';
    case LineduotoneGamepadOld = 'solar-line-duotone-gamepad_old';
    case LineduotoneCpuBolt = 'solar-line-duotone-cpu_bolt';
    case LineduotoneAirbudsCaseCharge = 'solar-line-duotone-airbuds_case_charge';
    case LineduotoneTablet = 'solar-line-duotone-Tablet';
    case LineduotoneWeigher = 'solar-line-duotone-Weigher';
    case LineduotoneServerSquare = 'solar-line-duotone-server_square';
    case LineduotoneMouse = 'solar-line-duotone-Mouse';
    case LineduotoneGamepadNoCharge = 'solar-line-duotone-gamepad_no_charge';
    case LineduotoneBluetoothSquare = 'solar-line-duotone-bluetooth_square';
    case LineduotoneCloudStorage = 'solar-line-duotone-cloud_storage';
    case LineduotoneGamepad = 'solar-line-duotone-Gamepad';
    case LineduotoneMonitor = 'solar-line-duotone-Monitor';
    case LineduotoneCassette = 'solar-line-duotone-Cassette';

    /**
     * Get the icon for a specific size.
     *
     * This method provides compatibility with Filament's ScalableIcon interface.
     * For Solar icons, we return the same icon regardless of size
     * since they are SVG and scale naturally.
     *
     * @param string $size The requested icon size (ignored for SVG icons)
     * @return string The icon identifier
     */
    public function getIconForSize(string $size): string
    {
        return $this->value;
    }

    public function getIconName(): string
    {
        return $this->value;
    }

    /**
     * Get all available Solar icon styles with descriptions.
     *
     * @return array<string, string> Array of style names and descriptions
     */
    public static function getAvailableStyles(): array
    {
        return [
            'bold' => 'Bold - Filled, strong visual weight',
            'bold-duotone' => 'Bold Duotone - Two-tone bold style',
            'broken' => 'Broken - Stylized broken line style',
            'line-duotone' => 'Line Duotone - Two-tone line style',
            'linear' => 'Linear - Clean, minimal lines',
            'outline' => 'Outline - Clean outlined style',
        ];
    }

    /**
     * Get icon identifier by name and style.
     *
     * @param string $name The icon name
     * @param string $style The icon style (default: 'linear')
     * @return string The full icon identifier
     */
    public static function getIcon(string $name, string $style = 'linear'): string
    {
        return "solar-{$style}-{$name}";
    }

    /**
     * Check if an icon exists in the enum.
     *
     * @param string $iconName The icon identifier to check
     * @return bool True if the icon exists in the enum, false otherwise
     */
    public static function exists(string $iconName): bool
    {
        if (empty(trim($iconName))) {
            return false;
        }

        return collect(self::cases())->contains(fn($case) => $case->value === $iconName);
    }

    /**
     * Get all enum cases as an array of values.
     *
     * @return array<string> Array of all icon identifiers
     */
    public static function values(): array
    {
        return array_map(fn($case) => $case->value, self::cases());
    }

    /**
     * Get enum cases grouped by style.
     *
     * @return array<string, array<self>> Array of styles with their icons
     */
    public static function groupedByStyle(): array
    {
        $grouped = [];

        foreach (self::cases() as $case) {
            $parts = explode('-', $case->value, 3);
            if (count($parts) >= 2) {
                $style = $parts[1];
                if (count($parts) === 3 && $parts[2] === 'duotone') {
                    $style .= '-duotone';
                }
                $grouped[$style][] = $case;
            }
        }

        return $grouped;
    }

    /**
     * Get all icon names grouped by style
     */
    public static function getIconsByStyle(): array
    {
        $icons = [];
        foreach (self::cases() as $case) {
            $parts = explode('-', $case->value, 3);
            if (count($parts) >= 3) {
                $style = $parts[1];
                $name = $parts[2];
                $icons[$style][] = $name;
            }
        }
        return $icons;
    }

}
